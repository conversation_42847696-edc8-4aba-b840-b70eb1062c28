# Manus HTML Crawler

Công cụ này giúp trích xuất cấu trúc HTML từ Manus để phục vụ việc crawl dữ liệu tự động.

## Thành phần

- `manus_html_crawler.py`: <PERSON><PERSON> tích cấu trúc HTML và tạo cấu hình crawl
- `manus_playwright_crawler.py`: Sử dụng Playwright để crawl dữ liệu từ Manus theo cấu hình

## Cài đặt

```bash
pip install -r requirements.txt
playwright install chromium
```

## Sử dụng

1. <PERSON>ân tích cấu trúc HTML và tạo cấu hình crawl:

```bash
python manus_html_crawler.py
```

2. Sử dụng cấu hình để crawl dữ liệu từ Manus (cần chỉnh sửa URL):

```bash
python manus_playwright_crawler.py
```

## Cấu trúc dữ liệu

Dữ liệu trích xuất có dạng:

```json
{
  "type": "user/assistant",
  "content": "Nội dung tin nhắn",
  "attachments": [
    {
      "type": "file",
      "name": "Tên file"
    }
  ],
  "timestamp": "Thời gian"
}
```
