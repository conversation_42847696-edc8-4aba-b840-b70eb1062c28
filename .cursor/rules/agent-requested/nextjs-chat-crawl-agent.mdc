---
description:
globs:
alwaysApply: always
---
# <PERSON><PERSON> thống Chat Realtime với Crawl Tự động trong NextJS

## Context

- Áp dụng cho dự án NextJS kết hợp crawling tự động (Playwright)
- Tri<PERSON><PERSON> khai hệ thống chat realtime với WebSocket giữa Frontend (NextJS) và Backend (FastAPI)
- Tự động crawl phản hồi từ dịch vụ bên thứ ba và hiển thị theo thời gian thực
- Quản lý trạng thái chat và xử lý nhiều loại nội dung (văn bản, code, danh sách)

## Critical Rules

- Tách biệt rõ ràng ba thành phần: Frontend (NextJS), Backend (FastAPI), và Playwright Worker
- X<PERSON>y dựng giao diện chat với khả năng hiển thị nhiều loại nội dung (text, code, lists, images)
- Trong NextJS, sử dụng Client Components (không phải Server Components) cho UI chat realtime
- Tạo custom hooks để quản lý kết nối WebSocket và trạng thái chat (`useWebSocket`, `useChat`)
- Định nghĩa cấu trúc tin nhắn nhất quán giữa tất cả các thành phần (JSON schema)
- Quản lý trạng thái loading và error rõ ràng trong UI khi chờ kết nối/crawling
- Triển khai WebSocket API Route trong NextJS để kết nối với Backend FastAPI
- Sử dụng Context API (hoặc Zustand/Redux) để quản lý trạng thái chat toàn cục
- Phân tách logic xử lý tin nhắn ra khỏi UI components để dễ bảo trì
- Cấu trúc component đúng: ContainerComponent (logic) -> PresentationalComponent (UI)
- Xử lý reconnection tự động cho WebSocket khi mất kết nối
- Streaming tin nhắn (hiển thị từng phần khi nhận được) để UX tốt hơn
- Hỗ trợ syntax highlighting cho code blocks trong chat

## Examples

<example>
// Context Provider cho Chat (contexts/ChatContext.tsx)
import React, { createContext, useContext, useState, useEffect, useCallback } from 'react'
import { useWebSocket } from '@/hooks/useWebSocket'

// Định nghĩa cấu trúc dữ liệu
export type MessagePart = {
  type: 'text' | 'code' | 'image' | 'list'
  content: string
  language?: string // cho code blocks
}

export type Message = {
  id: string
  sender: 'user' | 'assistant'
  timestamp: string
  parts: MessagePart[]
}

type ChatContextType = {
  messages: Message[]
  sendMessage: (content: string) => void
  isConnected: boolean
  isLoading: boolean
  error: Error | null
}

const ChatContext = createContext<ChatContextType | null>(null)

export function ChatProvider({ children, clientId }: { children: React.ReactNode, clientId: string }) {
  const [isLoading, setIsLoading] = useState(false)
  const { messages: wsMessages, isConnected, error, sendMessage: wsSend } = 
    useWebSocket('ws://localhost:8000/ws', clientId)
  
  const [messages, setMessages] = useState<Message[]>([])

  // Cập nhật messages khi nhận được dữ liệu mới từ WebSocket
  useEffect(() => {
    if (wsMessages.length > 0) {
      const latestMessage = wsMessages[wsMessages.length - 1]
      setMessages(prev => [...prev, latestMessage])
      setIsLoading(false)
    }
  }, [wsMessages])

  const sendMessage = useCallback((content: string) => {
    // Thêm tin nhắn của người dùng vào state
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      sender: 'user',
      timestamp: new Date().toISOString(),
      parts: [{ type: 'text', content }]
    }
    
    setMessages(prev => [...prev, userMessage])
    setIsLoading(true) // Bắt đầu loading khi gửi tin nhắn
    
    // Gửi tin nhắn qua WebSocket
    wsSend(content)
  }, [wsSend])

  return (
    <ChatContext.Provider value={{ messages, sendMessage, isConnected, isLoading, error }}>
      {children}
    </ChatContext.Provider>
  )
}

export const useChat = () => {
  const context = useContext(ChatContext)
  if (!context) throw new Error('useChat must be used within ChatProvider')
  return context
}
</example>

<example type="invalid">
// Thiết kế không đúng cho hệ thống chat realtime
import React, { useEffect, useState } from 'react'

function ChatPage() {
  const [messages, setMessages] = useState([])
  const [input, setInput] = useState('')
  
  // Lỗi: Gọi API trực tiếp từ Frontend thay vì qua WebSocket
  async function handleSend() {
    // Thêm tin nhắn người dùng
    setMessages(prev => [...prev, { sender: 'user', content: input }])
    
    // Lỗi: Gọi API trực tiếp để crawl, không dùng WebSocket để nhận kết quả realtime
    const response = await fetch('/api/send-message', {
      method: 'POST',
      body: JSON.stringify({ message: input }),
    })
    
    const data = await response.json()
    // Lỗi: Không xử lý nhiều loại nội dung (text, code, etc.)
    setMessages(prev => [...prev, { sender: 'assistant', content: data.message }])
    setInput('')
  }
  
  return (
    <div>
      <div className="chat-container">
        {messages.map((msg, i) => (
          <div key={i} className={msg.sender}>
            {/* Lỗi: Không xử lý định dạng khác nhau (chỉ hiển thị text thuần) */}
            {msg.content}
          </div>
        ))}
      </div>
      <input value={input} onChange={e => setInput(e.target.value)} />
      <button onClick={handleSend}>Send</button>
    </div>
  )
}

export default ChatPage
</example>
