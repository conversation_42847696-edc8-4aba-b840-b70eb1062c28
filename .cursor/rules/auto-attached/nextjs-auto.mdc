---
description:
globs:
alwaysApply: false
---
# Quy tắc NextJS

## Context

- Áp dụng cho dự án NextJS
- Tuân theo cấu trúc thư mục và quy ước đặt tên của NextJS
- Nắm bắt các quy tắc quan trọng về định tuyến, cấu hình và tối ưu hóa trong NextJS
- Hỗ trợ xây dựng ứng dụng chat realtime với tương tác websocket
- Tích hợp với các dịch vụ ngoài thông qua API hoặc crawling automation

## Critical Rules

- Hiểu cả hai hệ thống định tuyến: Pages Router (pages/) và App Router (app/)
- Trong App Router, sử dụng các file đặc biệt: page.tsx, layout.tsx, loading.tsx, error.tsx, not-found.tsx
- Trong Pages Router, file trong pages/ ánh xạ trực tiếp đến đường dẫn URL
- Tối ưu hóa hình ảnh luôn dùng next/image thay vì thẻ img thông thường
- Các API routes nằm trong pages/api/ (Pages Router) hoặc app/api/ (App Router)
- Sử dụng next/link cho điều hướng client-side thay vì thẻ a
- Metadata và SEO: Sử dụng next/head (Pages Router) hoặc metadata object (App Router)
- Đặt các thành phần dùng chung trong thư mục components/
- Các hooks tùy chỉnh đặt trong thư mục hooks/
- Các hàm tiện ích và service đặt trong thư mục lib/ hoặc utils/
- CSS Modules đặt tên file.module.css và đặt cùng thư mục với component
- Cấu hình toàn cục nằm trong next.config.mjs
- Server Components là mặc định trong App Router, Client Components cần "use client" directive
- Data fetching trong App Router: Ưu tiên sử dụng async/await trực tiếp trong component
- Tạo WebSocket API Route để xử lý kết nối realtime trong pages/api/ hoặc app/api/
- Sử dụng Context API hoặc state manager (như Redux, Zustand) để quản lý trạng thái chat
- Cấu trúc message phải nhất quán và hỗ trợ nhiều loại nội dung (text, code, list, image)
- Tối ưu re-rendering cho các component chat bằng React.memo, useMemo, useCallback
- WebSocket connections nên được quản lý trong custom hooks (useWebSocket, useChat)
- Xử lý kết nối bị ngắt và cơ chế tự động kết nối lại cho WebSocket
- Tạo cấu trúc contexts/providers thống nhất cho quản lý trạng thái và kết nối
- Hiển thị loading state và thông báo lỗi một cách rõ ràng trong UI chat

## Examples

<example>
// App Router Page Component (app/dashboard/page.tsx)
import Image from 'next/image'
import Link from 'next/link'
import { fetchData } from '@/lib/data'

export const metadata = {
  title: 'Dashboard',
  description: 'User dashboard page'
}

export default async function DashboardPage() {
  const data = await fetchData()
  
  return (
    <main>
      <h1>Dashboard</h1>
      <Link href="/profile">Go to Profile</Link>
      <Image src="/dashboard.jpg" alt="Dashboard" width={500} height={300} />
    </main>
  )
}
</example>

<example type="invalid">
// Không tuân thủ quy tắc NextJS
import React from 'react'

function Dashboard() {
  // Sử dụng useEffect cho data fetching thay vì async Server Component
  React.useEffect(() => {
    fetch('/api/data').then(res => res.json())
  }, [])
  
  return (
    <div>
      <h1>Dashboard</h1>
      <a href="/profile">Go to Profile</a>
      <img src="/dashboard.jpg" alt="Dashboard" />
    </div>
  )
}

export default Dashboard
</example>

<example>
// WebSocket Custom Hook (hooks/useWebSocket.ts)
import { useState, useEffect, useCallback, useRef } from 'react'

export function useWebSocket(url: string, clientId: string) {
  const [messages, setMessages] = useState<any[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const socketRef = useRef<WebSocket | null>(null)

  // Thiết lập kết nối WebSocket
  useEffect(() => {
    const socket = new WebSocket(`${url}/${clientId}`)
    socketRef.current = socket

    socket.onopen = () => setIsConnected(true)
    socket.onclose = () => setIsConnected(false)
    socket.onerror = (event) => setError(new Error('WebSocket error'))
    
    socket.onmessage = (event) => {
      const data = JSON.parse(event.data)
      setMessages(prev => [...prev, data])
    }

    return () => {
      socket.close()
    }
  }, [url, clientId])

  // Gửi tin nhắn qua WebSocket
  const sendMessage = useCallback((content: string) => {
    if (socketRef.current?.readyState === WebSocket.OPEN) {
      socketRef.current.send(JSON.stringify({ content, clientId }))
    }
  }, [clientId])

  return { messages, isConnected, error, sendMessage }
}
</example>

<example type="invalid">
// Cách làm sai khi xử lý WebSocket trong NextJS
import React, { useState } from 'react'

function ChatComponent() {
  const [messages, setMessages] = useState([])
  
  // Lỗi: Khởi tạo WebSocket trong component mà không có useEffect 
  // sẽ gây vấn đề khi SSR
  const socket = new WebSocket('ws://localhost:8000/ws')
  
  // Lỗi: Không quản lý vòng đời của WebSocket connection
  socket.onmessage = (event) => {
    const data = JSON.parse(event.data)
    // Lỗi: Cập nhật state không dùng hàm callback
    setMessages([...messages, data])
  }
  
  // Lỗi: Không xử lý lỗi kết nối
  // Lỗi: Không xử lý việc đóng kết nối khi component unmount
  
  return (
    <div>
      {messages.map((msg, i) => <div key={i}>{msg.content}</div>)}
      <button onClick={() => socket.send('Hello')}>Send</button>
    </div>
  )
}

export default ChatComponent
</example>
