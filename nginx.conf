events {
    worker_connections 1024;
}

http {
    # Thêm MIME types
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # <PERSON><PERSON><PERSON> hình proxy chung
    proxy_http_version 1.1;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    server {
        listen 80;
        
        # Proxy cho API endpoints với đường dẫn chính xác
        location /api/ {
            # Loại bỏ /api/ trước khi chuyển tiếp đến backend
            rewrite ^/api/(.*)$ /$1 break;
            proxy_pass http://backend:8000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_read_timeout 300s;
        }
        
        # Proxy trực tiếp cho WebSocket
        location /ws/ {
            proxy_pass http://backend:8000/ws/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_read_timeout 300s;
        }
        
        # Proxy cho frontend
        location / {
            proxy_pass http://frontend:3000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
        }

        # Thêm endpoint mới vào location pattern
        location ~ ^/(setup-chrome-profile|open-browser-login|minus-chat|proxy|ws) {
            proxy_pass http://backend:8000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_read_timeout 300s;
        }
    }
} 