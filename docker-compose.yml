version: '3.8'
services:
  # Service backend đa năng, hỗ trợ cả GUI và không GUI
  backend:
    build:
      context: ./fastapi
    container_name: youhome-backend
    ports:
      - "8000:8000"
      - "5900:5900"  # VNC port (sửa từ 5901:5900 thành 5900:5900)
      - "6080:6080"  # noVNC WebSocket port (sửa từ 6081:6080 thành 6080:6080)
    volumes:
      - ./fastapi:/app
      - ./chrome_profiles:/root/chrome_profiles  # Sử dụng thư mục local trong dự án
      - /tmp/.X11-unix:/tmp/.X11-unix  # X11 socket cho VNC
    restart: unless-stopped
    environment:
      - PYTHONUNBUFFERED=1
      - USE_GUI=true
      - DISPLAY=:1
      - WATCHFILES_FORCE_POLLING=true
      - CORS_ORIGINS=http://localhost,http://localhost:80,http://localhost:3000,http://frontend:3000
      - VNC_PASSWORD=playwright
    shm_size: 2gb
    privileged: true  # <PERSON><PERSON><PERSON> quyền cao hơn để chạy X11 và VNC
    command: bash -c "chmod +x /app/docker-entrypoint.sh && /app/docker-entrypoint.sh"
  frontend:
    build:
      context: ./nextjs
      dockerfile: Dockerfile.dev  # Sử dụng Dockerfile riêng cho dev
    container_name: youhome-frontend
    ports:
      - "3000:3000"
    volumes:
      - ./nextjs:/app  # Mount thư mục local vào container
      - /app/node_modules  # Tránh ghi đè node_modules
      - /app/.next  # Tránh ghi đè .next
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true  # Bật polling cho file watcher
      - WATCHPACK_POLLING=true  # Bật polling cho Next.js
      - API_BASE_URL=http://localhost:80  # Sử dụng cổng 80 qua Nginx
      - NEXT_PUBLIC_WEBSOCKET_URL=ws://localhost:80/ws/chat  # Sử dụng cổng 80 qua Nginx
    depends_on:
      - backend
    command: npm run dev  # Sử dụng lệnh dev
    restart: unless-stopped
  # Thêm dịch vụ proxy Nginx để xử lý WebSocket
  nginx:
    image: nginx:alpine
    container_name: youhome-nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - backend
      - frontend