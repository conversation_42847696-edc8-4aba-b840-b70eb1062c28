"""
Markdown processing utilities for Manus Chat.
Handles content formatting, cleaning, and markdown conversion.
"""

import re
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

class MarkdownProcessor:
    """Xử lý và format nội dung thành markdown."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def clean_manus_content(self, content: str) -> str:
        """Làm sạch nội dung từ Manus AI interface."""
        if not content:
            return ""
        
        # Remove timestamps
        content = re.sub(r'\d{1,2}:\d{2}', '', content)
        
        # Remove HelloShare patterns
        content = re.sub(r'HelloShare\d+:\d+hello\d+:\d+Hello!', '', content)
        
        # Remove await patterns
        content = re.sub(r'Await user instructions\d+:\d+Waiting for user instructions\.\.\.', '', content)
        
        # Remove completion + await patterns
        content = re.sub(r'<PERSON><PERSON> has completed the current taskAwait user instructions\d+\s*\/\s*\d+', '', content)
        
        # Remove duplicate completion messages
        content = re.sub(r'(<PERSON><PERSON> has completed the current task\s*){2,}', '<PERSON><PERSON> has completed the current task', content)
        
        # Normalize whitespace
        content = re.sub(r'\s+', ' ', content)
        
        return content.strip()
    
    def format_as_markdown(self, content: str, message_type: str = "assistant") -> str:
        """Format nội dung thành markdown."""
        if not content:
            return ""
        
        # Clean content first
        content = self.clean_manus_content(content)
        
        if message_type == "assistant":
            # Format AI responses
            return self._format_ai_response(content)
        elif message_type == "user":
            # Format user messages
            return self._format_user_message(content)
        elif message_type == "system":
            # Format system messages (completion notifications)
            return self._format_system_message(content)
        
        return content
    
    def _format_ai_response(self, content: str) -> str:
        """Format AI response content."""
        # Check if it's a greeting
        if "Hello! I'm Manus" in content or "I'm Manus, your AI assistant" in content:
            return f"👋 **Manus AI**\n\n{content}"
        
        # Check if it contains code
        if self._contains_code(content):
            return self._format_code_content(content)
        
        # Check if it's a list or structured content
        if self._is_structured_content(content):
            return self._format_structured_content(content)
        
        # Default formatting
        return content
    
    def _format_user_message(self, content: str) -> str:
        """Format user message content."""
        return content
    
    def _format_system_message(self, content: str) -> str:
        """Format system message content."""
        if "completed" in content.lower():
            return f"✅ **Task Completed**\n\n{content}"
        elif "waiting" in content.lower():
            return f"⏳ **Status Update**\n\n{content}"
        
        return f"ℹ️ **System**\n\n{content}"
    
    def _contains_code(self, content: str) -> bool:
        """Kiểm tra xem nội dung có chứa code không."""
        code_indicators = [
            'function', 'const', 'let', 'var', 'class',
            'import', 'export', 'def', 'return',
            '```', '`', '{', '}', ';', '//'
        ]
        return any(indicator in content for indicator in code_indicators)
    
    def _is_structured_content(self, content: str) -> bool:
        """Kiểm tra xem nội dung có cấu trúc không."""
        return bool(re.search(r'\d+\.\s+|\*\s+|-\s+|•\s+', content))
    
    def _format_code_content(self, content: str) -> str:
        """Format nội dung chứa code."""
        # Try to detect language
        if 'javascript' in content.lower() or 'js' in content.lower():
            lang = 'javascript'
        elif 'python' in content.lower() or 'py' in content.lower():
            lang = 'python'
        elif 'html' in content.lower():
            lang = 'html'
        elif 'css' in content.lower():
            lang = 'css'
        else:
            lang = ''
        
        # If content already has code blocks, return as is
        if '```' in content:
            return content
        
        # Wrap in code block if it looks like code
        lines = content.split('\n')
        code_lines = []
        text_lines = []
        
        for line in lines:
            if any(indicator in line for indicator in ['{', '}', ';', 'function', 'const', 'let']):
                code_lines.append(line)
            else:
                text_lines.append(line)
        
        if code_lines and len(code_lines) > 2:
            result = ""
            if text_lines:
                result += '\n'.join(text_lines) + '\n\n'
            result += f"```{lang}\n" + '\n'.join(code_lines) + "\n```"
            return result
        
        return content
    
    def _format_structured_content(self, content: str) -> str:
        """Format nội dung có cấu trúc."""
        # Convert numbered lists
        content = re.sub(r'(\d+)\.\s+', r'\n\1. ', content)
        
        # Convert bullet points
        content = re.sub(r'[•*-]\s+', r'\n- ', content)
        
        return content.strip()
    
    def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Xử lý một tin nhắn và thêm markdown formatting."""
        if not message or not message.get('content'):
            return message
        
        # Create a copy to avoid modifying original
        processed_message = message.copy()
        
        # Get original content
        original_content = message['content']
        role = message.get('role', 'assistant')
        
        # Process content
        markdown_content = self.format_as_markdown(original_content, role)
        
        # Add markdown fields
        processed_message['markdown_content'] = markdown_content
        processed_message['original_content'] = original_content
        processed_message['processed_at'] = datetime.now().isoformat()
        processed_message['content_type'] = self._detect_content_type(markdown_content)
        
        return processed_message
    
    def _detect_content_type(self, content: str) -> str:
        """Phát hiện loại nội dung."""
        if '```' in content:
            return 'code'
        elif re.search(r'\d+\.\s+|\*\s+|-\s+', content):
            return 'list'
        elif "Hello! I'm Manus" in content:
            return 'greeting'
        elif "completed" in content.lower():
            return 'completion'
        else:
            return 'text'
    
    def process_chat_history(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Xử lý toàn bộ lịch sử chat."""
        processed_messages = []
        
        for message in messages:
            processed_message = self.process_message(message)
            processed_messages.append(processed_message)
        
        return processed_messages

# Global instance
markdown_processor = MarkdownProcessor()
