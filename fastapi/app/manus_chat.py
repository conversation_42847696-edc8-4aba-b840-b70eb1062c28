"""
Module x<PERSON> lý tương tác với <PERSON> AI với cấu trúc crawl được tối ưu hóa.
Sử dụng constants từ manus-selectors.js và report từ manus-html-structure-report.md
"""
import asyncio
import logging
import time
import os
import subprocess
import shutil
import glob
from typing import Dict, List, Any, Optional
from datetime import datetime
from playwright.async_api import Page
from .session_manager import session_manager
from .websocket_manager import connection_manager
from .markdown_processor import markdown_processor

# --- Cấu hình Logging ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Import Manus Selectors Constants ---
# Tương đương với MANUS_SELECTORS từ constants/manus-selectors.js
MANUS_SELECTORS = {
    # Main chat container
    "CHAT_CONTAINER": {
        "MAIN_WRAPPER": ".simplebar-content",
        "SCROLLABLE_CONTENT": '[role="scrollable content"]',
        "MESSAGES_CONTAINER": 'div[class*="flex flex-col"]'
    },

    # Input elements
    "INPUT": {
        "MESSAGE_INPUT": 'textarea[placeholder*="Give Manus a task to work on"]',
        "MESSAGE_INPUT_ACTIVE": 'textarea[placeholder*="Send message to Manus"]',
        "SEND_BUTTON": 'button[class*="flex items-center justify-center"]',
        "SEND_BUTTON_ALT": 'button:has(img[alt*="send"])'
    },

    # Chat messages
    "MESSAGES": {
        "USER_MESSAGE": 'div[class*="flex justify-end"]',
        "USER_MESSAGE_CONTENT": 'div[class*="bg-[var(--primary)]"]',
        "AI_MESSAGE": 'div[class*="prose"]',
        "AI_MESSAGE_CONTAINER": 'div:has(.prose)',
        "TIMESTAMP": 'div[class*="text-xs text-[var(--text-secondary)]"]',
        "MESSAGE_WRAPPER": 'div[class*="max-w-[80%]"]'
    },

    # Task completion indicators
    "COMPLETION": {
        "SUCCESS_NOTIFICATION": 'div[class*="px-3 py-\\[5px\\] rounded-full text-\\[var\\(--function-success\\)\\]"]',
        "SUCCESS_TEXT": 'div:contains("Manus has completed the current task")',
        "TASK_STATUS": 'div[class*="text-[var(--function-success)]"]',
        "COMPLETION_BADGE": 'div[class*="rounded-full"][class*="text-[13px]"]'
    },

    # UI elements
    "UI": {
        "SIDEBAR": 'div[class*="w-64"]',
        "SIDEBAR_CONTENT": 'nav[class*="flex flex-col"]',
        "HEADER": 'header',
        "TITLE": 'h1, h2, h3',
        "NEW_TASK_BUTTON": 'button:contains("New task")',
        "MENU_BUTTON": 'button[class*="cursor-pointer"]',
        "SETTINGS_ICON": 'img[alt*="settings"], img[alt*="menu"]'
    },

    # Loading states
    "LOADING": {
        "LOADING_SPINNER": 'div[class*="animate-spin"]',
        "LOADING_DOTS": 'div[class*="loading"]',
        "TYPING_INDICATOR": 'div[class*="typing"]',
        "PROCESSING": 'div:contains("Processing")'
    },

    # Error states
    "ERROR": {
        "ERROR_MESSAGE": 'div[class*="error"]',
        "ERROR_NOTIFICATION": 'div[class*="text-red"]',
        "WARNING": 'div[class*="warning"]',
        "WARNING_NOTIFICATION": 'div[class*="text-yellow"]'
    }
}

# CSS Selector utilities for complex queries
MANUS_QUERIES = {
    "ALL_MESSAGES": f'{MANUS_SELECTORS["MESSAGES"]["USER_MESSAGE"]}, {MANUS_SELECTORS["MESSAGES"]["AI_MESSAGE_CONTAINER"]}',
    "LATEST_MESSAGE": f'({MANUS_SELECTORS["MESSAGES"]["USER_MESSAGE"]}, {MANUS_SELECTORS["MESSAGES"]["AI_MESSAGE_CONTAINER"]}):last-child',
    "IS_COMPLETED": MANUS_SELECTORS["COMPLETION"]["SUCCESS_NOTIFICATION"],
    "ACTIVE_INPUT": f'{MANUS_SELECTORS["INPUT"]["MESSAGE_INPUT"]}, {MANUS_SELECTORS["INPUT"]["MESSAGE_INPUT_ACTIVE"]}',
    "ACTIVE_SEND_BUTTON": f'{MANUS_SELECTORS["INPUT"]["SEND_BUTTON"]}, {MANUS_SELECTORS["INPUT"]["SEND_BUTTON_ALT"]}'
}

# XPath selectors for more complex queries
MANUS_XPATH = {
    "COMPLETION_TEXT": "//div[contains(text(), 'Manus has completed the current task')]",
    "NEW_TASK_BUTTON": "//button[contains(text(), 'New task')]",
    "MESSAGE_INPUT": "//textarea[contains(@placeholder, 'Give Manus a task') or contains(@placeholder, 'Send message')]",
    "CHAT_MESSAGES": "//div[contains(@class, 'prose') or contains(@class, 'justify-end')]",
    "MESSAGE_WITH_TIMESTAMP": "//div[contains(@class, 'text-xs')]/parent::div"
}

# Fallback selectors for compatibility
FALLBACK_SELECTORS = {
    "messageListSelector": "div.flex.flex-col.w-full.gap-[12px].pb-[80px].pt-[12px].flex-1",
    "messageBlockSelector": "div[data-event-id]",
    "userMessageIndicatorClass": "items-end",
    "aiMessageIndicatorSelector": "div.flex.items-center.justify-between.h-7.group svg",
    "messageContentSelector": "span.text-[var(--text-primary)] u-break-words whitespace-pre-wrap",
    "timestampSelector": "div.float-right.transition.text-[12px].text-[var(--text-tertiary)] invisible group-hover:visible"
}

# Cấu hình Playwright
PLAYWRIGHT_CONFIG = {
    "headless": False,  # Sửa thành True trong môi trường production
    "slow_mo": 50,
    "timeout": 60000    # 60 giây
}

# --- Cấu trúc dữ liệu cho Frontend ---
from dataclasses import dataclass
from typing import Union
import re

@dataclass
class PlanStep:
    """Cấu trúc cho một bước trong plan."""
    title: str
    description: str
    status: str = 'pending'  # 'pending', 'in_progress', 'completed'
    files: Optional[List[Dict[str, str]]] = None

    def __post_init__(self):
        if self.files is None:
            self.files = []

@dataclass
class PlanData:
    """Cấu trúc dữ liệu cho content_type='plan'."""
    title: str
    steps: List[PlanStep]

@dataclass
class CodeData:
    """Cấu trúc dữ liệu cho content_type='code'."""
    language: str
    code: str
    filename: Optional[str] = None
    description: Optional[str] = None

@dataclass
class FileData:
    """Cấu trúc dữ liệu cho content_type='file_attachment'."""
    filename: str
    size: str
    type: str  # 'code', 'document', 'image', 'other'
    description: Optional[str] = None
    url: Optional[str] = None

@dataclass
class CompletionData:
    """Cấu trúc dữ liệu cho content_type='completion'."""
    title: str
    description: str
    deliverables: Optional[List[Dict[str, str]]] = None

    def __post_init__(self):
        if self.deliverables is None:
            self.deliverables = []

# --- Cấu trúc tin nhắn ---
class ChatMessage:
    """Lớp đại diện cho một tin nhắn trong cuộc trò chuyện với hỗ trợ frontend format."""

    def __init__(self,
                 role: str,
                 content: str,
                 timestamp: float = None,
                 timestamp_str: Optional[str] = None,
                 attachments: Optional[List[Dict]] = None,
                 metadata: Optional[Dict[str, Any]] = None,
                 message_id: Optional[str] = None,
                 content_type: Optional[str] = None,
                 plan_data: Optional[PlanData] = None,
                 code_data: Optional[CodeData] = None,
                 file_data: Optional[FileData] = None,
                 completion_data: Optional[CompletionData] = None):
        """Khởi tạo một đối tượng ChatMessage.

        Args:
            role: Vai trò của người gửi ('user', 'assistant', 'system')
            content: Nội dung tin nhắn
            timestamp: Thời gian gửi dạng timestamp (giây)
            timestamp_str: Thời gian gửi dạng chuỗi hiển thị
            attachments: Danh sách tệp đính kèm
            metadata: Thông tin bổ sung về tin nhắn
            message_id: ID duy nhất cho tin nhắn
            content_type: Loại nội dung ('text', 'plan', 'code', 'file_attachment', 'completion')
            plan_data: Dữ liệu cho plan content
            code_data: Dữ liệu cho code content
            file_data: Dữ liệu cho file attachment content
            completion_data: Dữ liệu cho completion content
        """
        self.role = role
        self.content = content
        self.timestamp = timestamp or time.time()
        self.timestamp_str = timestamp_str
        self.attachments = attachments or []
        self.metadata = metadata or {}

        # Frontend-specific fields
        self.content_type = content_type or self._detect_content_type()

        # Auto-extract specialized data if not provided
        self.plan_data = plan_data or (self._extract_plan_data() if self.content_type == 'plan' else None)
        self.code_data = code_data or (self._extract_code_data() if self.content_type == 'code' else None)
        self.file_data = file_data or (self._extract_file_data() if self.content_type == 'file_attachment' else None)
        self.completion_data = completion_data or (self._extract_completion_data() if self.content_type == 'completion' else None)

        # Tạo ID duy nhất cho tin nhắn với random component để tránh collision
        if message_id:
            self.id = message_id
        else:
            import random
            content_hash = abs(hash(content)) % 100000
            random_component = random.randint(1000, 9999)
            timestamp_ms = int(self.timestamp * 1000)
            self.id = f"{timestamp_ms}_{content_hash}_{random_component}_{role[:3]}"

    def _detect_content_type(self) -> str:
        """Tự động phát hiện loại nội dung dựa trên content."""
        content_lower = self.content.lower().strip()

        # Detect completion messages
        completion_patterns = [
            "manus has completed",
            "task complete",
            "task completed",
            "successfully completed",
            "finished the task",
            "deliverables sent",
            "all deliverables"
        ]

        if any(pattern in content_lower for pattern in completion_patterns):
            return 'completion'

        # Detect code blocks
        code_patterns = [
            "```",
            "curl -x",
            "def ",
            "function ",
            "class ",
            "import ",
            "from ",
            "npm install",
            "pip install",
            "docker run"
        ]

        if any(pattern in content_lower for pattern in code_patterns):
            return 'code'

        # Detect plan/task structure
        plan_patterns = [
            "step 1:",
            "step 2:",
            "1.",
            "2.",
            "3.",
            "plan:",
            "steps:",
            "workflow:",
            "process:",
            "analyze",
            "identify",
            "modify",
            "validate",
            "report"
        ]

        # Count plan indicators
        plan_count = sum(1 for pattern in plan_patterns if pattern in content_lower)
        if plan_count >= 2:  # Multiple plan indicators suggest it's a plan
            return 'plan'

        # Detect file attachments
        file_patterns = [
            ".py",
            ".js",
            ".md",
            ".txt",
            ".json",
            ".csv",
            ".pdf",
            "kb",
            "mb",
            "file:",
            "attachment:",
            "download"
        ]

        if any(pattern in content_lower for pattern in file_patterns):
            return 'file_attachment'

        # Default to text
        return 'text'

    def _extract_plan_data(self) -> Optional[PlanData]:
        """Trích xuất dữ liệu plan từ content."""
        if self.content_type != 'plan':
            return None

        lines = self.content.split('\n')
        title = ""
        steps = []

        # Extract title (usually first line or line with "plan", "workflow", etc.)
        for line in lines[:3]:  # Check first 3 lines
            line = line.strip()
            if line and not line.startswith(('1.', '2.', '3.', 'Step', '-')):
                title = line
                break

        # Extract steps
        current_step = None
        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Check if it's a step header
            step_patterns = [
                r'^\d+\.\s*(.+)',  # "1. Step title"
                r'^Step\s+\d+:\s*(.+)',  # "Step 1: Step title"
                r'^-\s*(.+)',  # "- Step title"
            ]

            for pattern in step_patterns:
                match = re.match(pattern, line, re.IGNORECASE)
                if match:
                    if current_step:
                        steps.append(current_step)

                    step_title = match.group(1).strip()
                    current_step = PlanStep(
                        title=step_title,
                        description="",
                        status='pending'
                    )
                    break
            else:
                # If not a step header, add to current step description
                if current_step and line:
                    if current_step.description:
                        current_step.description += " " + line
                    else:
                        current_step.description = line

        # Add the last step
        if current_step:
            steps.append(current_step)

        if not title and steps:
            title = "Task Plan"

        return PlanData(title=title, steps=steps) if steps else None

    def _extract_code_data(self) -> Optional[CodeData]:
        """Trích xuất dữ liệu code từ content."""
        if self.content_type != 'code':
            return None

        # Extract code blocks
        code_block_pattern = r'```(\w+)?\n(.*?)\n```'
        matches = re.findall(code_block_pattern, self.content, re.DOTALL)

        if matches:
            language, code = matches[0]
            language = language or 'text'

            # Extract description (text before code block)
            description_match = re.search(r'^(.*?)```', self.content, re.DOTALL)
            description = description_match.group(1).strip() if description_match else ""

            return CodeData(
                language=language,
                code=code.strip(),
                description=description or None
            )

        # If no code blocks, treat entire content as code
        # Try to detect language
        language = 'bash'
        if 'curl' in self.content.lower():
            language = 'bash'
        elif 'def ' in self.content or 'import ' in self.content:
            language = 'python'
        elif 'function ' in self.content or 'const ' in self.content:
            language = 'javascript'

        return CodeData(
            language=language,
            code=self.content,
            description=None
        )

    def _extract_file_data(self) -> Optional[FileData]:
        """Trích xuất dữ liệu file attachment từ content."""
        if self.content_type != 'file_attachment':
            return None

        # Extract filename from content
        filename_patterns = [
            r'(\w+\.\w+)',  # filename.ext
            r'File:\s*([^\s]+)',  # File: filename
            r'Attachment:\s*([^\s]+)',  # Attachment: filename
        ]

        filename = "unknown_file"
        for pattern in filename_patterns:
            matches = re.findall(pattern, self.content)
            if matches:
                filename = matches[0]
                break

        # Extract file size
        size_patterns = [
            r'(\d+\.?\d*\s*[KMG]B)',  # file sizes like 35.86 KB
            r'(\d+\s*bytes?)',  # bytes
        ]

        size = "Unknown"
        for pattern in size_patterns:
            matches = re.findall(pattern, self.content, re.IGNORECASE)
            if matches:
                size = matches[0]
                break

        # Determine file type based on extension
        file_type = "other"
        if filename:
            ext = filename.split('.')[-1].lower() if '.' in filename else ""
            if ext in ['py', 'js', 'ts', 'java', 'cpp', 'c', 'h', 'css', 'html', 'php']:
                file_type = "code"
            elif ext in ['md', 'txt', 'doc', 'docx', 'pdf']:
                file_type = "document"
            elif ext in ['jpg', 'jpeg', 'png', 'gif', 'svg', 'bmp']:
                file_type = "image"

        # Extract description (usually the content without filename and size)
        description = self.content
        for pattern in filename_patterns + size_patterns:
            description = re.sub(pattern, '', description, flags=re.IGNORECASE)
        description = description.strip()

        return FileData(
            filename=filename,
            size=size,
            type=file_type,
            description=description if description else None,
            url="#"
        )

    def _extract_completion_data(self) -> Optional[CompletionData]:
        """Trích xuất dữ liệu completion từ content."""
        if self.content_type != 'completion':
            return None

        title = "Manus has completed the task"
        description = self.content
        deliverables = []

        # Extract deliverables from content
        file_patterns = [
            r'(\w+\.\w+)',  # filename.ext
            r'(\d+\.?\d*\s*[KMG]B)',  # file sizes
        ]

        for pattern in file_patterns:
            matches = re.findall(pattern, self.content)
            for match in matches:
                if '.' in match and len(match.split('.')) == 2:  # Likely a filename
                    deliverables.append({
                        'name': match,
                        'type': 'File',
                        'size': 'Unknown'
                    })

        return CompletionData(
            title=title,
            description=description,
            deliverables=deliverables
        )

    def to_dict(self) -> Dict[str, Any]:
        """Chuyển đổi tin nhắn sang dạng từ điển để serialize với frontend format.

        Returns:
            Dict chứa thông tin tin nhắn theo format frontend
        """
        # Base structure
        result = {
            "id": int(self.id.split('_')[0]) if '_' in str(self.id) else int(time.time() * 1000),
            "content": self.content,
            "sender": 'assistant' if self.role == 'assistant' else 'user',
            "timestamp": datetime.fromtimestamp(self.timestamp).isoformat(),
            "content_type": self.content_type,
            "status": "sent"
        }

        # Add specialized data based on content type
        if self.content_type == 'plan' and self.plan_data:
            result["plan_data"] = {
                "title": self.plan_data.title,
                "steps": [
                    {
                        "title": step.title,
                        "description": step.description,
                        "status": step.status,
                        "files": step.files or []
                    }
                    for step in self.plan_data.steps
                ]
            }
        elif self.content_type == 'code' and self.code_data:
            result["code_data"] = {
                "language": self.code_data.language,
                "code": self.code_data.code,
                "filename": self.code_data.filename,
                "description": self.code_data.description
            }
        elif self.content_type == 'file_attachment' and self.file_data:
            result["file_data"] = {
                "filename": self.file_data.filename,
                "size": self.file_data.size,
                "type": self.file_data.type,
                "description": self.file_data.description,
                "url": self.file_data.url or "#"
            }
        elif self.content_type == 'completion' and self.completion_data:
            result["completion_data"] = {
                "title": self.completion_data.title,
                "description": self.completion_data.description,
                "deliverables": self.completion_data.deliverables or []
            }

        # Legacy fields for backward compatibility
        result.update({
            "role": self.role,
            "time": datetime.fromtimestamp(self.timestamp).strftime("%H:%M:%S"),
            "timestamp_str": self.timestamp_str,
            "attachments": self.attachments,
            "metadata": self.metadata
        })

        return result

    @classmethod
    def create_frontend_message(cls, content: str, role: str = "assistant", **kwargs) -> 'ChatMessage':
        """Tạo tin nhắn với format frontend từ content thô.

        Args:
            content: Nội dung tin nhắn
            role: Vai trò ('user', 'assistant')
            **kwargs: Các tham số bổ sung

        Returns:
            ChatMessage object với frontend format
        """
        return cls(
            role=role,
            content=content,
            timestamp=kwargs.get('timestamp', time.time()),
            timestamp_str=kwargs.get('timestamp_str'),
            attachments=kwargs.get('attachments', []),
            metadata=kwargs.get('metadata', {})
        )

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChatMessage':
        """Tạo đối tượng ChatMessage từ từ điển.

        Args:
            data: Từ điển chứa dữ liệu tin nhắn

        Returns:
            Đối tượng ChatMessage mới
        """
        return cls(
            role=data.get("role"),
            content=data.get("content"),
            timestamp=data.get("timestamp"),
            timestamp_str=data.get("timestamp_str"),
            attachments=data.get("attachments"),
            metadata=data.get("metadata"),
            message_id=data.get("id")
        )

    def is_duplicate_of(self, other: 'ChatMessage', check_content_only: bool = False) -> bool:
        """Kiểm tra xem tin nhắn này có trùng lặp với tin nhắn khác không.

        Args:
            other: Tin nhắn khác để so sánh
            check_content_only: Chỉ kiểm tra nội dung, không quan tâm đến vai trò

        Returns:
            True nếu là tin nhắn trùng lặp
        """
        if not check_content_only and self.role != other.role:
            return False

        # Normalize content để so sánh
        self_content = self.content.strip().lower()
        other_content = other.content.strip().lower()

        # Kiểm tra nội dung chính xác
        if self_content == other_content:
            return True

        # ENHANCED: Kiểm tra các trường hợp đặc biệt cho "hello" và greeting messages
        hello_patterns = [
            "hello",
            "hello!",
            "hello! i'm manus",
            "hello! how can i help you today?",
            "hello! i'm manus, your ai assistant",
            "hi there",
            "hi!",
            "hi",
            "greetings",
            "good morning",
            "good afternoon",
            "good evening",
            # Thêm các pattern từ Manus
            "i'm manus, your ai assistant",
            "i'm here to help you",
            "how can i assist you today",
            "what can i help you with",
            "how may i help you"
        ]

        # Nếu cả hai đều là hello/greeting messages
        if any(pattern in self_content for pattern in hello_patterns) and \
           any(pattern in other_content for pattern in hello_patterns):
            return True

        # ENHANCED: Kiểm tra các tin nhắn giới thiệu tương tự từ Manus
        if (("i'm manus" in self_content or "manus" in self_content) and
            ("assistant" in self_content or "help" in self_content) and
            ("i'm manus" in other_content or "manus" in other_content) and
            ("assistant" in other_content or "help" in other_content)):
            return True

        # ENHANCED: Kiểm tra các tin nhắn không có nội dung hoặc chỉ có khoảng trắng
        if len(self_content.strip()) == 0 and len(other_content.strip()) == 0:
            return True

        # ENHANCED: Kiểm tra các tin nhắn chỉ có ký tự đặc biệt
        if len(self_content.strip()) <= 3 and len(other_content.strip()) <= 3:
            if self_content.strip() == other_content.strip():
                return True

        # ENHANCED: Kiểm tra các tin nhắn "thinking" hoặc loading
        thinking_patterns = [
            "thinking",
            "processing",
            "loading",
            "...",
            "please wait",
            "one moment",
            "let me think"
        ]

        if any(pattern in self_content for pattern in thinking_patterns) and \
           any(pattern in other_content for pattern in thinking_patterns):
            return True

        # ENHANCED: Kiểm tra phần đầu của nội dung dài (tăng độ dài kiểm tra)
        if len(self.content) > 50 and len(other.content) > 50:
            # Kiểm tra 100 ký tự đầu
            if self_content[:100] == other_content[:100]:
                return True
            # Kiểm tra 50 ký tự đầu với độ tương tự cao
            if self_content[:50] == other_content[:50] and len(self_content) < 200:
                return True

        # ENHANCED: Kiểm tra similarity cao cho nội dung ngắn
        if len(self_content) < 100 and len(other_content) < 100:
            # Tính toán Levenshtein distance đơn giản
            similarity = self._calculate_similarity(self_content, other_content)
            if similarity > 0.85:  # Tăng threshold
                return True

        # ENHANCED: Kiểm tra các tin nhắn completion tương tự
        completion_patterns = [
            "completed",
            "finished",
            "done",
            "task complete",
            "successfully completed"
        ]

        if any(pattern in self_content for pattern in completion_patterns) and \
           any(pattern in other_content for pattern in completion_patterns):
            # Nếu cả hai đều chứa "manus" và completion patterns
            if "manus" in self_content and "manus" in other_content:
                return True

        return False

    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """Tính toán độ tương tự giữa hai chuỗi."""
        if not str1 or not str2:
            return 0.0

        # Đơn giản hóa: tính tỷ lệ ký tự chung
        set1 = set(str1.lower())
        set2 = set(str2.lower())

        if not set1 and not set2:
            return 1.0

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def is_meaningful_message(self) -> bool:
        """Kiểm tra xem tin nhắn có ý nghĩa và cần thiết không.

        Returns:
            True nếu tin nhắn có ý nghĩa, False nếu không cần thiết
        """
        content = self.content.strip().lower()

        # Loại bỏ tin nhắn rỗng hoặc chỉ có khoảng trắng
        if len(content) == 0:
            return False

        # ENHANCED: Loại bỏ các tin nhắn không có nội dung thực sự
        meaningless_exact_matches = [
            # Các tin nhắn trống hoặc placeholder
            "",
            "new task",
            "share",
            "hello",
            "hi",
            "manus is typing",
            "không có nội dung phản hồi",
            "no response content",
            "waiting for response",
            "loading",
            "processing",
            "thinking",
            "please wait",
            "one moment",
            "let me think",
            # Các ký tự đặc biệt
            "...",
            ".",
            "..",
            "....",
            ".....",
            # Các từ ngắn không có ý nghĩa
            "hmm",
            "uh",
            "um",
            "ah",
            "ok",
            "okay",
            "yes",
            "no",
            "sure",
            # Các thông báo hệ thống
            "task started",
            "task completed",
            "session started",
            "session ended"
        ]

        # Kiểm tra exact match với các pattern không có ý nghĩa
        if content in meaningless_exact_matches:
            return False

        # ENHANCED: Loại bỏ tin nhắn chỉ chứa các từ khóa hệ thống
        system_keywords = [
            "manus is typing",
            "manus is thinking",
            "waiting for user instructions",
            "task in progress",
            "processing request",
            "generating response"
        ]

        if any(keyword in content for keyword in system_keywords):
            return False

        # Loại bỏ tin nhắn chỉ có ký tự đặc biệt
        if len(content) <= 5 and all(c in '.,!?;:-_()[]{}*+=' for c in content):
            return False

        # ENHANCED: Loại bỏ tin nhắn quá ngắn không có thông tin có ý nghĩa
        if len(content) < 10 and not any(char.isalnum() for char in content):
            return False

        # ENHANCED: Chỉ chấp nhận tin nhắn có ít nhất 15 ký tự và chứa từ có nghĩa
        if len(content) < 15:
            # Kiểm tra xem có chứa từ có nghĩa không (ít nhất 3 ký tự)
            words = content.split()
            meaningful_words = [word for word in words if len(word) >= 3 and word.isalpha()]
            if len(meaningful_words) < 2:  # Cần ít nhất 2 từ có nghĩa
                return False

        return True

    def is_meaningful_user_message(self) -> bool:
        """Kiểm tra xem tin nhắn từ user có ý nghĩa không (đặc biệt cho user messages).

        Returns:
            True nếu tin nhắn user có ý nghĩa, False nếu chỉ là greeting đơn giản
        """
        if self.role != "user":
            return self.is_meaningful_message()

        content = self.content.strip().lower()

        # Loại bỏ các tin nhắn user quá đơn giản
        simple_user_messages = [
            "hello",
            "hi",
            "hey",
            "hello!",
            "hi!",
            "hey!",
            "good morning",
            "good afternoon",
            "good evening",
            "how are you",
            "how are you?",
            "what's up",
            "what's up?",
            "sup",
            "yo"
        ]

        # Nếu user chỉ gửi greeting đơn giản, có thể bỏ qua
        if content in simple_user_messages:
            return False

        # Chấp nhận tin nhắn user có nội dung thực sự
        return self.is_meaningful_message()

# --- Helper Functions for Frontend Format ---
def process_messages_for_frontend(messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Xử lý danh sách tin nhắn và chuyển đổi sang format frontend.

    Args:
        messages: Danh sách tin nhắn thô từ extraction

    Returns:
        Danh sách tin nhắn đã được format cho frontend
    """
    frontend_messages = []

    for msg_data in messages:
        try:
            # Tạo ChatMessage object để tự động detect content type và extract data
            chat_msg = ChatMessage.create_frontend_message(
                content=msg_data.get('content', ''),
                role=msg_data.get('role', 'assistant'),
                timestamp=msg_data.get('timestamp'),
                timestamp_str=msg_data.get('timestamp_str'),
                attachments=msg_data.get('attachments', []),
                metadata=msg_data.get('metadata', {})
            )

            # Convert to frontend format
            frontend_msg = chat_msg.to_dict()
            frontend_messages.append(frontend_msg)

        except Exception as e:
            logging.warning(f"Lỗi khi xử lý tin nhắn cho frontend: {e}")
            # Fallback to basic format
            frontend_messages.append({
                "id": int(time.time() * 1000),
                "content": msg_data.get('content', ''),
                "sender": 'assistant' if msg_data.get('role') == 'assistant' else 'user',
                "timestamp": datetime.now().isoformat(),
                "content_type": "text",
                "status": "sent"
            })

    return frontend_messages

def create_demo_messages() -> List[Dict[str, Any]]:
    """Tạo demo messages theo format frontend để test."""
    demo_data = [
        {
            "content": "I'll help you translate the Vietnamese text to English. Here's the translation:",
            "role": "assistant"
        },
        {
            "content": """Translate to English: Continue fixing feedback tasks

1. Read and analyze the API code.
   Reading and analyzing the API code file.
   Files: upload/pasted_content.txt, upload/pasted_content_2.txt

2. Identify required changes for the workflow.
   Identifying required changes for workflow alignment.

3. Modify the API code to match the test workflow.
   Modifying the API code to match the test code workflow.
   Files: analysis.md, modified_api_code.py

4. Validate the modified API code.
   Validating the modified API code.
   Files: validation_notes.md

5. Report and send the modified code and analysis documents to the user.
   Reporting and sending modified code and analysis documents to the user.""",
            "role": "assistant"
        },
        {
            "content": "Cách sử dụng mới:",
            "role": "assistant"
        },
        {
            "content": """Đối với profile đã được xác thực, hãy sử dụng tham số `skip_login_check`:

```bash
curl -X POST "http://localhost:8800/manus-chat-profile" \\
  -H "Content-Type: application/json" \\
  -d '{
    "chat_content": "Xin chào từ API test curl",
    "profile_name": "my_profile",
    "headless": false,
    "wait_time": 3,
    "skip_login_check": true
  }'
```""",
            "role": "assistant"
        },
        {
            "content": "Tôi đã đính kèm hai tệp:",
            "role": "assistant"
        },
        {
            "content": "refactored_api_code.py (35.86 KB) - Mã API đã được cải tiến",
            "role": "assistant"
        },
        {
            "content": "validation_report_refactored.md (2.84 KB) - Báo cáo chi tiết về các thay đổi và kiểm tra xác nhận",
            "role": "assistant"
        },
        {
            "content": "Manus has completed the current task. All deliverables sent to user and task complete. Deliverables: refactored_api_code.py (35.86 KB), validation_report_refactored.md (2.84 KB)",
            "role": "assistant"
        }
    ]

    return process_messages_for_frontend(demo_data)

async def navigate_to_manus(page: Page) -> bool:
    """Điều hướng đến trang Manus AI.

    Args:
        page: Đối tượng Page từ Playwright

    Returns:
        True nếu điều hướng thành công, False nếu thất bại
    """
    try:
        # Điều hướng đến trang chính của Manus
        await page.goto("https://manus.im/", timeout=PLAYWRIGHT_CONFIG["timeout"])
        # Đợi cho trang tải xong
        await page.wait_for_load_state("networkidle")

        # Kiểm tra xem đã đăng nhập chưa
        if "app" in page.url or "dashboard" in page.url:
            logging.info("Đã đăng nhập vào Manus AI")
            return True

        # Kiểm tra nút "Chat with Manus" và nhấp vào nếu có
        try:
            chat_button_selector = "text=Chat with Manus"
            if await page.is_visible(chat_button_selector, timeout=5000):
                logging.info("Tìm thấy nút 'Chat with Manus', đang nhấp vào...")
                await page.click(chat_button_selector)
                await page.wait_for_load_state("networkidle")
                return True
        except Exception as e:
            logging.warning(f"Không tìm thấy nút 'Chat with Manus': {e}")

        # Thử điều hướng trực tiếp đến trang chat
        logging.info("Điều hướng trực tiếp đến trang chat...")
        await page.goto("https://manus.im/chat", timeout=PLAYWRIGHT_CONFIG["timeout"])
        await page.wait_for_load_state("networkidle")

        return True
    except Exception as e:
        logging.error(f"Lỗi khi điều hướng đến Manus AI: {e}")
        return False

async def send_message_to_manus(page: Page, message: str) -> bool:
    """Gửi tin nhắn đến Manus AI.

    Args:
        page: Đối tượng Page từ Playwright
        message: Nội dung tin nhắn cần gửi

    Returns:
        True nếu gửi thành công, False nếu thất bại
    """
    try:
        # Kiểm tra xem có nút "Start a conversation" không
        start_button_selector = "text=Start a conversation"
        if await page.is_visible(start_button_selector, timeout=3000):
            logging.info("Tìm thấy nút 'Start a conversation', đang nhấp vào...")
            await page.click(start_button_selector)
            await page.wait_for_load_state("networkidle")
            await asyncio.sleep(1)  # Đợi thêm để giao diện chat hiển thị

        # Tìm ô nhập liệu - sử dụng MANUS_SELECTORS mới
        input_selectors = [
            # Selectors chính từ MANUS_SELECTORS
            MANUS_SELECTORS["INPUT"]["MESSAGE_INPUT"],
            MANUS_SELECTORS["INPUT"]["MESSAGE_INPUT_ACTIVE"],
            # Selectors dự phòng từ MANUS_QUERIES
            MANUS_QUERIES["ACTIVE_INPUT"],
            # Selectors fallback
            "textarea[placeholder='Send message to Manus']",
            "textarea[placeholder='Wait for user instructions']",
            "textarea[placeholder='Type your message...']",
            "textarea[placeholder='Give Manus a task to work on...']",
            # Các selector dự phòng
            "textarea.w-full",
            "textarea.resize-none",
            "textarea",
            "div[contenteditable='true']",
            "[role='textbox']"
        ]

        input_element = None
        for selector in input_selectors:
            try:
                if await page.is_visible(selector, timeout=1000):
                    input_element = await page.query_selector(selector)
                    logging.info(f"Đã tìm thấy ô nhập liệu với selector: {selector}")
                    break
            except Exception:
                continue

        if not input_element:
            logging.error("Không tìm thấy ô nhập liệu với bất kỳ selector nào")
            return False

        # Nhập tin nhắn
        await input_element.fill(message)

        # Đợi một chút sau khi nhập tin nhắn để đảm bảo nút gửi đã được kích hoạt
        await asyncio.sleep(0.5)

        # Thử gửi tin nhắn với các phương pháp khác nhau
        send_methods = [
            # Phương pháp 1: Nhấn Enter trực tiếp trên input element
            lambda: input_element.press("Enter"),
            # Phương pháp 2: Sử dụng keyboard.press
            lambda: page.keyboard.press("Enter")
        ]

        for method in send_methods:
            try:
                await method()
                logging.info("Đã gửi tin nhắn bằng phương pháp Enter")
                break
            except Exception as e:
                logging.warning(f"Lỗi khi gửi tin nhắn: {e}")
                continue

        # Đợi một chút sau khi gửi
        await asyncio.sleep(1)

        logging.info(f"Đã gửi tin nhắn: {message}")
        return True
    except Exception as e:
        logging.error(f"Lỗi khi gửi tin nhắn đến Manus AI: {e}")
        return False

async def extract_chat_messages(page: Page) -> List[Dict[str, Any]]:
    """Trích xuất tất cả tin nhắn từ cuộc trò chuyện hiện tại, sử dụng selectors từ file JSON.

    Args:
        page: Đối tượng Page từ Playwright

    Returns:
        Danh sách các tin nhắn đã được trích xuất dưới dạng dict
    """
    try:
        # Kiểm tra xem page có còn mở không
        if not page or page.is_closed():
            logging.warning("Không thể trích xuất tin nhắn: Page đã đóng")
            return []

        # Lấy các selector cần thiết từ MANUS_SELECTORS và FALLBACK_SELECTORS
        msg_list_selector = MANUS_SELECTORS["CHAT_CONTAINER"]["MAIN_WRAPPER"]
        msg_block_selector = MANUS_QUERIES["ALL_MESSAGES"]
        user_indicator_class = "justify-end"  # Từ MANUS_SELECTORS["MESSAGES"]["USER_MESSAGE"]
        ai_indicator_selector = MANUS_SELECTORS["MESSAGES"]["AI_MESSAGE"]
        content_selector = "span, p, div.prose"  # Selector tổng quát cho content
        timestamp_selector = MANUS_SELECTORS["MESSAGES"]["TIMESTAMP"]
        attachment_list_selector = 'div[class*="flex gap-2"]'
        attachment_block_selector = 'div[class*="group"]'
        attachment_filename_selector = 'div[class*="text-sm"]'
        attachment_type_selector = 'div[class*="text-xs"]'

        # Escape các ký tự đặc biệt trong CSS selectors
        for key, value in locals().copy().items():
            if key.endswith('_selector') and isinstance(value, str):
                locals()[key] = value.replace("[", "\\[").replace("]", "\\]")

        # Kiểm tra các selectors tối thiểu cần thiết
        if not all([msg_list_selector, msg_block_selector]):
            logging.error("Thiếu các selectors quan trọng trong cấu hình")
            return []

        # Đợi container chính của danh sách tin nhắn xuất hiện
        found_container = False
        try:
            await page.wait_for_selector(msg_list_selector, timeout=3000)
            found_container = True
        except Exception as e:
            logging.warning(f"Không tìm thấy container chính: {e} - thử phương pháp dự phòng")

            # Phương pháp dự phòng: thử các selectors đơn giản hơn
            fallback_selectors = [
                "div.flex.flex-col",
                "main div.flex-col",
                "div.chat-container",
                "div[class*='message']",
                "div[class*='chat']",
                "div.overflow-y-auto",
                "[role='main']"
            ]

            for fallback in fallback_selectors:
                try:
                    if await page.is_visible(fallback, timeout=1000):
                        logging.info(f"Tìm thấy container với selector dự phòng: {fallback}")
                        msg_list_selector = fallback
                        found_container = True
                        break
                except Exception:
                    continue

        if not found_container:
            logging.error("Không tìm thấy container tin nhắn nào, không thể trích xuất")
            return []

        # Trích xuất tin nhắn bằng JavaScript với metadata đầy đủ - sử dụng MANUS_SELECTORS
        messages = await page.evaluate(f"""
            () => {{
                const messages = [];
                const extractedAt = new Date().toISOString();

                // Hàm querySelector an toàn với xử lý lỗi
                const safeQuerySelector = (container, selector) => {{
                    if (!container) return null;
                    if (!selector || selector.trim() === '') return null;

                    try {{
                        return container.querySelector(selector);
                    }} catch (e) {{
                        console.warn(`Selector không hợp lệ: ${{selector}}, lỗi: ${{e.message}}`);

                        // Thử escape các ký tự đặc biệt nếu lỗi liên quan đến CSS
                        if (e.message.includes('selector') || e.message.includes('Selector')) {{
                            try {{
                                // Escape các ký tự đặc biệt
                                const safeSelector = selector
                                    .replace(/\\[/g, '\\\\[')
                                    .replace(/\\]/g, '\\\\]')
                                    .replace(/\\(/g, '\\\\(')
                                    .replace(/\\)/g, '\\\\)');

                                return container.querySelector(safeSelector);
                            }} catch (innerError) {{
                                // Bỏ qua lỗi thứ hai
                            }}
                        }}

                        return null;
                    }}
                }};

                // Hàm querySelectorAll an toàn với xử lý lỗi
                const safeQuerySelectorAll = (container, selector) => {{
                    if (!container) return [];
                    if (!selector || selector.trim() === '') return [];

                    try {{
                        return container.querySelectorAll(selector);
                    }} catch (e) {{
                        console.warn(`Selector không hợp lệ (All): ${{selector}}, lỗi: ${{e.message}}`);

                        // Thử escape các ký tự đặc biệt nếu lỗi liên quan đến CSS
                        if (e.message.includes('selector') || e.message.includes('Selector')) {{
                            try {{
                                // Escape các ký tự đặc biệt
                                const safeSelector = selector
                                    .replace(/\\[/g, '\\\\[')
                                    .replace(/\\]/g, '\\\\]')
                                    .replace(/\\(/g, '\\\\(')
                                    .replace(/\\)/g, '\\\\)');

                                return container.querySelectorAll(safeSelector);
                            }} catch (innerError) {{
                                // Bỏ qua lỗi thứ hai
                            }}
                        }}

                        return [];
                    }}
                }};

                // Tìm container chính của danh sách tin nhắn - sử dụng MANUS_SELECTORS
                let messageList = safeQuerySelector(document, '{msg_list_selector}');

                // Phương pháp dự phòng nếu không tìm thấy container
                if (!messageList) {{
                    // Thử các selectors từ MANUS_SELECTORS
                    const fallbackSelectors = [
                        '{MANUS_SELECTORS["CHAT_CONTAINER"]["MESSAGES_CONTAINER"]}',
                        '{MANUS_SELECTORS["CHAT_CONTAINER"]["SCROLLABLE_CONTENT"]}',
                        // Selectors phổ biến khác
                        'div.flex-col',
                        'div[class*="chat"]',
                        'div[class*="message"]',
                        'main div'
                    ];

                    for (const selector of fallbackSelectors) {{
                        messageList = safeQuerySelector(document, selector);
                        if (messageList) {{
                            console.log(`Tìm thấy container với selector dự phòng: ${{selector}}`);
                            break;
                        }}
                    }}

                    // Nếu vẫn không tìm thấy, sử dụng body làm container
                    if (!messageList) {{
                        console.warn('Không tìm thấy container, sử dụng document.body');
                        messageList = document.body;
                    }}
                }}

                // Lấy tất cả các khối tin nhắn - sử dụng MANUS_QUERIES
                let messageBlocks = safeQuerySelectorAll(messageList, '{msg_block_selector}');

                // Phương pháp dự phòng nếu không tìm thấy tin nhắn
                if (!messageBlocks || messageBlocks.length === 0) {{
                    console.warn('Không tìm thấy tin nhắn với selector chính, thử phương pháp dự phòng');

                    // Các selectors dự phòng cho khối tin nhắn từ MANUS_SELECTORS
                    const fallbackBlockSelectors = [
                        '{MANUS_SELECTORS["MESSAGES"]["USER_MESSAGE"]}',
                        '{MANUS_SELECTORS["MESSAGES"]["AI_MESSAGE_CONTAINER"]}',
                        'div[class*="message"]',
                        'div.flex',
                        'div > div.flex',
                        'div.mb-4',
                        'div[class*="chat-message"]',
                        'div > div'
                    ];

                    for (const selector of fallbackBlockSelectors) {{
                        const blocks = safeQuerySelectorAll(messageList, selector);
                        if (blocks && blocks.length > 0) {{
                            console.log(`Tìm thấy tin nhắn với selector dự phòng: ${{selector}}, số lượng: ${{blocks.length}}`);
                            messageBlocks = blocks;
                            break;
                        }}
                    }}

                    // Nếu vẫn không tìm thấy, trả về rỗng
                    if (!messageBlocks || messageBlocks.length === 0) {{
                        console.warn('Không tìm thấy khối tin nhắn nào');
                        return messages;
                    }}
                }}

                // Duyệt qua từng khối tin nhắn
                for (const block of messageBlocks) {{
                    try {{
                        let role = 'unknown';
                        let content = '';
                        let timestamp = null;
                        let attachments = [];
                        let metadata = {{}};

                        // Xác định role dựa trên class hoặc selector - sử dụng MANUS_SELECTORS
                        const blockClasses = block.className || '';
                        let isAssistant = false;

                        // Thử phát hiện role bằng class từ MANUS_SELECTORS
                        if (blockClasses.includes('{user_indicator_class}') ||
                            blockClasses.includes('justify-end')) {{
                            role = 'user';
                            metadata.role_detection = 'by_class';
                        }}
                        // Thử phát hiện role bằng selector từ MANUS_SELECTORS
                        else {{
                            try {{
                                // Kiểm tra AI message indicators từ MANUS_SELECTORS
                                if (safeQuerySelector(block, '{ai_indicator_selector}') ||
                                    safeQuerySelector(block, 'div[class*="prose"]') ||
                                    block.querySelector('.prose')) {{
                                    isAssistant = true;
                                }}
                            }} catch (e) {{
                                // Bỏ qua lỗi selector
                            }}

                            // Phương pháp dự phòng cho việc phát hiện assistant
                            if (!isAssistant) {{
                                // Kiểm tra các dấu hiệu của tin nhắn từ assistant từ MANUS_SELECTORS
                                const assistantIndicators = [
                                    'div svg', // Biểu tượng thường có trong tin nhắn AI
                                    'img[alt*="manus"]',
                                    'img[alt*="assistant"]',
                                    'div[class*="ai"]',
                                    'div[class*="assistant"]',
                                    'div[class*="prose"]',
                                    '.prose'
                                ];

                                for (const indicator of assistantIndicators) {{
                                    try {{
                                        if (safeQuerySelector(block, indicator)) {{
                                            isAssistant = true;
                                            break;
                                        }}
                                    }} catch (e) {{
                                        // Bỏ qua lỗi selector
                                    }}
                                }}

                                // Phát hiện bằng vị trí (tin nhắn assistant thường ở bên trái, user ở bên phải)
                                if (!isAssistant) {{
                                    if (blockClasses.includes('flex-start') ||
                                        blockClasses.includes('items-start') ||
                                        blockClasses.includes('justify-start') ||
                                        !blockClasses.includes('justify-end')) {{
                                        isAssistant = true;
                                    }}
                                }}
                            }}

                            if (isAssistant) {{
                                role = 'assistant';
                                metadata.role_detection = 'by_heuristic';
                            }} else if (role !== 'user') {{
                                // Nếu không phải user và không phát hiện được là assistant
                                // Gán mặc định dựa trên vị trí trong danh sách
                                const index = Array.from(messageBlocks).indexOf(block);
                                if (index % 2 === 0) {{
                                    role = 'user';
                                    metadata.role_detection = 'by_position';
                                }} else {{
                                    role = 'assistant';
                                    metadata.role_detection = 'by_position';
                                }}
                            }}
                        }}

                        // Trích xuất nội dung
                        let contentElement = safeQuerySelector(block, '{content_selector}');

                        // Phương pháp dự phòng cho nội dung
                        if (!contentElement) {{
                            // Các selectors phổ biến cho nội dung tin nhắn
                            const contentSelectors = [
                                'span', 'p', 'div.text', 'div.whitespace-pre-wrap',
                                'div.prose', 'div[class*="content"]', 'div[class*="text"]'
                            ];

                            for (const selector of contentSelectors) {{
                                try {{
                                    const el = safeQuerySelector(block, selector);
                                    if (el && el.textContent && el.textContent.trim().length > 0) {{
                                        contentElement = el;
                                        break;
                                    }}
                                }} catch (e) {{
                                    // Bỏ qua lỗi selector
                                }}
                            }}

                            // Nếu vẫn không tìm thấy, thử lấy text của block
                            if (!contentElement) {{
                                content = block.textContent.trim();
                                metadata.content_extraction = 'from_block';
                            }}
                        }}

                        // Lấy nội dung từ element
                        if (contentElement) {{
                            content = contentElement.textContent.trim();
                            metadata.content_extraction = 'success';
                        }}

                        // Bỏ qua nếu không tìm được nội dung
                        if (!content) {{
                            console.debug('Không tìm thấy nội dung tin nhắn, bỏ qua');
                            continue;
                        }}

                        // Trích xuất timestamp nếu có
                        if ('{timestamp_selector}') {{
                            try {{
                                const tsElement = safeQuerySelector(block, '{timestamp_selector}');
                                if (tsElement) {{
                                    timestamp = tsElement.textContent.trim();
                                    metadata.timestamp_extraction = 'success';
                                }} else {{
                                    // Phương pháp dự phòng cho timestamp
                                    const timestampSelectors = [
                                        'div.text-xs', 'span.text-xs', 'div.text-sm.text-gray-400',
                                        'div[class*="time"]', 'span[class*="time"]', 'div.text-gray-500'
                                    ];

                                    for (const selector of timestampSelectors) {{
                                        const el = safeQuerySelector(block, selector);
                                        if (el) {{
                                            const text = el.textContent.trim();
                                            // Kiểm tra xem có phải timestamp không (thường là ngắn và có số)
                                            if (text.length < 20 && /\d/.test(text)) {{
                                                timestamp = text;
                                                metadata.timestamp_extraction = 'by_fallback';
                                                break;
                                            }}
                                        }}
                                    }}
                                }}
                            }} catch (e) {{
                                // Bỏ qua lỗi selector
                            }}
                        }}

                        // Trích xuất tệp đính kèm nếu có
                        try {{
                            // Thử tìm danh sách tệp đính kèm
                            const attachmentList = safeQuerySelector(block, '{attachment_list_selector}');
                            if (attachmentList) {{
                                const attachmentBlocks = safeQuerySelectorAll(attachmentList, '{attachment_block_selector}');

                                for (const attachBlock of attachmentBlocks) {{
                                    let filename = '';
                                    let filetype = '';

                                    const filenameElement = safeQuerySelector(attachBlock, '{attachment_filename_selector}');
                                    const typeElement = safeQuerySelector(attachBlock, '{attachment_type_selector}');

                                    if (filenameElement) {{
                                        filename = filenameElement.textContent.trim();
                                    }}

                                    if (typeElement) {{
                                        filetype = typeElement.textContent.trim();
                                    }}

                                    if (filename) {{
                                        attachments.push({{ filename, type: filetype }});
                                    }}
                                }}

                                metadata.attachments_extraction = 'success';
                                metadata.attachments_count = attachments.length;
                            }}

                            // Phương pháp dự phòng: tìm các phần tử có thể là tệp đính kèm
                            if (attachments.length === 0) {{
                                // Tìm kiếm các liên kết tệp, hình ảnh, v.v.
                                const attachmentSelectors = [
                                    'a[href*="."]', // Liên kết có thể là tệp
                                    'img',          // Hình ảnh
                                    'div[class*="file"]',
                                    'div[class*="attachment"]'
                                ];

                                for (const selector of attachmentSelectors) {{
                                    const elements = safeQuerySelectorAll(block, selector);
                                    for (const el of elements) {{
                                        let filename = '';
                                        let filetype = '';

                                        if (el.tagName === 'A') {{
                                            const href = el.getAttribute('href') || '';
                                            if (href) {{
                                                filename = href.split('/').pop() || href;
                                                filetype = filename.split('.').pop() || '';
                                            }} else {{
                                                filename = el.textContent.trim();
                                            }}
                                        }} else if (el.tagName === 'IMG') {{
                                            const src = el.getAttribute('src') || '';
                                            if (src) {{
                                                filename = src.split('/').pop() || 'image';
                                                filetype = 'image';
                                            }}
                                        }} else {{
                                            filename = el.textContent.trim();
                                            filetype = 'file';
                                        }}

                                        if (filename) {{
                                            attachments.push({{ filename, type: filetype }});
                                        }}
                                    }}
                                }}

                                if (attachments.length > 0) {{
                                    metadata.attachments_extraction = 'by_fallback';
                                    metadata.attachments_count = attachments.length;
                                }}
                            }}
                        }} catch (e) {{
                            // Bỏ qua lỗi khi trích xuất tệp đính kèm
                            console.warn('Lỗi khi trích xuất tệp đính kèm:', e);
                        }}

                        // Thêm metadata
                        metadata.extraction_time = extractedAt;
                        metadata.element_id = block.id || null;
                        metadata.element_classes = block.className || null;
                        metadata.message_length = content.length;

                        // Thêm tin nhắn vào danh sách
                        messages.push({{
                            role,
                            content,
                            timestamp,
                            attachments,
                            metadata
                        }});
                    }} catch (e) {{
                        console.error('Lỗi khi trích xuất tin nhắn:', e);
                    }}
                }}

                return messages;
            }}
        """)

        # Xử lý tin nhắn với markdown_processor nếu có
        if markdown_processor:
            processed_messages = []
            for message in messages:
                processed_message = markdown_processor.process_message(message)
                processed_messages.append(processed_message)
            messages = processed_messages

        logging.info(f"Trích xuất được {len(messages)} tin nhắn từ cuộc trò chuyện")
        return messages
    except Exception as e:
        logging.error(f"Lỗi khi trích xuất tin nhắn: {e}")
        return []

async def extract_complete_chat_context(page: Page) -> Dict[str, Any]:
    """Trích xuất toàn bộ ngữ cảnh chat bao gồm HTML structure và metadata, tối ưu cho Manus AI interface."""
    try:
        # Kiểm tra xem page có còn mở không
        if not page or page.is_closed():
            logging.warning("Không thể trích xuất ngữ cảnh chat: Page đã đóng")
            return {
                "extracted_at": datetime.now().isoformat(),
                "error": "Page đã đóng",
                "messages": [],
                "completion_status": False,
                "manus_interface_detected": False,
                "interface_metadata": {"error": "Page đã đóng"}
            }

        # Lấy các selector cần thiết từ MANUS_SELECTORS
        message_list_selector = MANUS_SELECTORS["CHAT_CONTAINER"]["MAIN_WRAPPER"]

        # Escape các ký tự đặc biệt trong CSS selector để tương thích với querySelector
        message_list_selector = message_list_selector.replace("[", "\\[").replace("]", "\\]")

        # Lấy toàn bộ HTML của chat container với enhanced detection
        chat_context = await page.evaluate(f"""
            () => {{
                const context = {{
                    extracted_at: new Date().toISOString(),
                    page_url: window.location.href,
                    page_title: document.title,
                    chat_container_html: null,
                    sidebar_html: null,
                    full_page_html: null,
                    messages: [],
                    completion_status: false,
                    active_elements: [],
                    manus_interface_detected: false,
                    interface_metadata: {{}}
                }};

                // Enhanced chat container detection - Ưu tiên message container selector từ cấu hình
                let chatContainer = document.querySelector('{message_list_selector}');
                if (chatContainer) {{
                    context.chat_container_html = chatContainer.outerHTML;
                    context.interface_metadata.message_container_found = true;
                }} else {{
                    // Fallback to other selectors if message container not found
                    const chatContainerSelectors = [
                        'main',
                        '[role="main"]',
                        '.chat-container',
                        'div[class*="chat"]',
                        'div[class*="conversation"]',
                        'div[class*="messages"]',
                        // Manus specific selectors
                        'div[class*="manus"]',
                        'div[class*="ai-chat"]'
                    ];

                    for (const selector of chatContainerSelectors) {{
                        chatContainer = document.querySelector(selector);
                        if (chatContainer) {{
                            context.chat_container_html = chatContainer.outerHTML;
                            break;
                        }}
                    }}
                    context.interface_metadata.message_container_found = false;
                }}

                // Enhanced sidebar detection
                const sidebarSelectors = [
                    'aside',
                    '.sidebar',
                    'nav',
                    'div[class*="sidebar"]',
                    'div[class*="navigation"]',
                    'div[class*="menu"]'
                ];

                for (const selector of sidebarSelectors) {{
                    const sidebar = document.querySelector(selector);
                    if (sidebar) {{
                        context.sidebar_html = sidebar.outerHTML;
                        break;
                    }}
                }}

                // Detect Manus AI interface elements
                const manusElements = document.querySelectorAll('*');
                for (const element of manusElements) {{
                    const text = element.textContent || '';
                    const className = element.className || '';

                    if (text.toLowerCase().includes('manus') ||
                        className.toLowerCase().includes('manus') ||
                        text.toLowerCase().includes('ai assistant')) {{
                        context.manus_interface_detected = true;

                        // Extract interface metadata
                        if (text.includes("I'm Manus") || text.includes("Hello! I'm Manus")) {{
                            context.interface_metadata.greeting_detected = true;
                            context.interface_metadata.greeting_text = text.substring(0, 200);
                        }}

                        if (className.includes('avatar') || element.tagName.toLowerCase() === 'img') {{
                            context.interface_metadata.avatar_detected = true;
                        }}

                        break;
                    }}
                }}

                // Enhanced active elements detection
                const activeElementSelectors = [
                    '.typing',
                    '.loading',
                    '.spinner',
                    '[class*="animate"]',
                    '[class*="typing"]',
                    '[class*="loading"]',
                    '[class*="processing"]',
                    '[class*="thinking"]',
                    // Manus specific activity indicators
                    '[class*="wait"]',
                    '[class*="status"]'
                ];

                for (const selector of activeElementSelectors) {{
                    const elements = document.querySelectorAll(selector);
                    for (const element of elements) {{
                        const text = element.textContent || '';
                        if (text.trim()) {{
                            context.active_elements.push({{
                                tag: element.tagName,
                                classes: element.className,
                                text: text.substring(0, 100),
                                html: element.outerHTML,
                                selector_matched: selector
                            }});
                        }}
                    }}
                }}

                // Enhanced completion status detection - Tập trung vào message container và text chính xác
                try {{
                    // Tìm trong message container trước
                    const searchContainer = chatContainer || document;
                    const allElements = searchContainer.querySelectorAll('*');

                    for (const element of allElements) {{
                        const className = element.className || '';
                        const textContent = element.textContent || '';
                        const lowerText = textContent.toLowerCase();

                        // Check for exact completion text "Manus has completed the current task"
                        if (textContent.includes('Manus has completed the current task') ||
                            lowerText.includes('manus has completed the current task')) {{
                            context.completion_status = true;
                            context.interface_metadata.completion_text = textContent.trim();
                            context.interface_metadata.completion_element_class = className;
                            context.interface_metadata.exact_completion_detected = true;
                        }}

                        // Check for waiting status
                        if (lowerText.includes('wait for user instructions') ||
                            lowerText.includes('waiting for user')) {{
                            context.interface_metadata.waiting_status = true;
                            context.interface_metadata.waiting_text = textContent.trim();
                        }}

                        // Check for visual completion indicators (green, success classes)
                        if ((className.includes('function-success') ||
                             className.includes('text-green') ||
                             className.includes('success') ||
                             className.includes('completed')) &&
                            (textContent.includes('Manus has completed the current task') ||
                             lowerText.includes('manus has completed the current task'))) {{
                            context.completion_status = true;
                            context.interface_metadata.visual_completion_detected = true;
                        }}
                    }}
                }} catch (e) {{
                    console.warn('Error checking completion status:', e);
                }}

                // Get optimized page HTML (focus on chat area)
                if (chatContainer) {{
                    // Prioritize chat container HTML
                    context.full_page_html = chatContainer.outerHTML;
                }} else {{
                    // Fallback to full page with size limit
                    const fullHtml = document.documentElement.outerHTML;
                    if (fullHtml.length < 500000) {{ // 500KB limit
                        context.full_page_html = fullHtml;
                    }} else {{
                        context.full_page_html = fullHtml.substring(0, 500000) + '... [TRUNCATED]';
                    }}
                }}

                // Add extraction statistics
                context.interface_metadata.total_elements_scanned = document.querySelectorAll('*').length;
                context.interface_metadata.chat_container_found = !!chatContainer;
                context.interface_metadata.extraction_method = 'config_based_extraction';
                context.interface_metadata.config_selectors_used = true;

                return context;
            }}
        """)

        # Trích xuất messages với metadata đầy đủ
        messages = await extract_chat_messages(page)
        chat_context['messages'] = messages

        # Enhanced completion status based on messages
        completion_messages = [msg for msg in messages if msg.get('metadata', {}).get('is_completion', False)]
        if completion_messages:
            chat_context['completion_status'] = True
            chat_context['interface_metadata']['completion_messages_count'] = len(completion_messages)

        # Add message statistics
        chat_context['interface_metadata']['total_messages'] = len(messages)
        chat_context['interface_metadata']['user_messages'] = len([msg for msg in messages if msg.get('role') == 'user'])
        chat_context['interface_metadata']['assistant_messages'] = len([msg for msg in messages if msg.get('role') == 'assistant'])
        chat_context['interface_metadata']['system_messages'] = len([msg for msg in messages if msg.get('role') == 'system'])

        logging.info(f"Trích xuất được ngữ cảnh chat đầy đủ với {len(messages)} tin nhắn, Manus interface: {chat_context.get('manus_interface_detected', False)}")
        return chat_context

    except Exception as e:
        logging.error(f"Lỗi khi trích xuất ngữ cảnh chat: {e}")
        return {
            "extracted_at": datetime.now().isoformat(),
            "error": str(e),
            "messages": [],
            "completion_status": False,
            "manus_interface_detected": False,
            "interface_metadata": {"error": str(e)}
        }

async def monitor_manus_realtime(page: Page, session_id: str, max_duration: int = 900) -> None:
    """Giám sát Manus interface theo thời gian thực và stream data qua WebSocket."""
    start_time = time.time()
    last_message_index = -1 # Track the index of the last message sent
    last_completion_check = 0
    monitoring_interval = 2  # Kiểm tra mỗi 2 giây

    logging.info(f"Bắt đầu giám sát real-time cho session {session_id}, thời gian tối đa: {max_duration}s")

    try:
        while time.time() - start_time < max_duration:
            try:
                # Kiểm tra xem page có còn mở không
                if not page or page.is_closed():
                    logging.warning(f"Dừng monitoring: Page đã đóng (session {session_id})")
                    await connection_manager.send_message(session_id, {
                        "type": "monitoring_error",
                        "data": {
                            "message": "Kết nối trình duyệt đã đóng",
                            "elapsed_time": time.time() - start_time,
                            "monitoring_status": "error"
                        }
                    })
                    break

                # Trích xuất ngữ cảnh chat hiện tại
                chat_context = await extract_complete_chat_context(page)
                current_time = time.time()
                elapsed_time = current_time - start_time

                # Lấy danh sách tin nhắn hiện tại
                messages = chat_context.get('messages', [])
                current_message_count = len(messages)
                current_time = time.time()
                elapsed_time = current_time - start_time

                # Gửi tin nhắn mới qua WebSocket với enhanced filtering
                if current_message_count > last_message_index + 1:
                    new_messages = messages[last_message_index + 1:]

                    # Enhanced filter: duplicate + meaningless messages
                    filtered_messages = []
                    for new_msg in new_messages:
                        new_chat_msg = ChatMessage(
                            role=new_msg.get('role', 'unknown'),
                            content=new_msg.get('content', ''),
                            timestamp=new_msg.get('timestamp'),
                            metadata=new_msg.get('metadata', {})
                        )

                        # STEP 1: Kiểm tra xem tin nhắn có ý nghĩa không (với logic đặc biệt cho user)
                        if new_chat_msg.role == "user":
                            if not new_chat_msg.is_meaningful_user_message():
                                logging.info(f"Filtered meaningless user message: {new_msg.get('content', '')[:30]}...")
                                continue
                        else:
                            if not new_chat_msg.is_meaningful_message():
                                logging.info(f"Filtered meaningless message: {new_msg.get('content', '')[:30]}...")
                                continue

                        # STEP 2: Kiểm tra duplicate với các tin nhắn đã có
                        is_duplicate = False
                        for existing_msg in messages[:last_message_index + 1]:
                            existing_chat_msg = ChatMessage(
                                role=existing_msg.get('role', 'unknown'),
                                content=existing_msg.get('content', ''),
                                timestamp=existing_msg.get('timestamp'),
                                metadata=existing_msg.get('metadata', {})
                            )
                            if new_chat_msg.is_duplicate_of(existing_chat_msg):
                                is_duplicate = True
                                logging.info(f"Filtered duplicate message: {new_msg.get('content', '')[:30]}...")
                                break

                        # STEP 3: Kiểm tra duplicate với các tin nhắn mới khác trong batch
                        if not is_duplicate:
                            for filtered_msg in filtered_messages:
                                filtered_chat_msg = ChatMessage(
                                    role=filtered_msg.get('role', 'unknown'),
                                    content=filtered_msg.get('content', ''),
                                    timestamp=filtered_msg.get('timestamp'),
                                    metadata=filtered_msg.get('metadata', {})
                                )
                                if new_chat_msg.is_duplicate_of(filtered_chat_msg):
                                    is_duplicate = True
                                    logging.info(f"Filtered duplicate in new batch: {new_msg.get('content', '')[:30]}...")
                                    break

                        # STEP 4: Chỉ thêm tin nhắn nếu không duplicate và có ý nghĩa
                        if not is_duplicate:
                            filtered_messages.append(new_msg)

                    if filtered_messages:
                        filtered_count = len(new_messages) - len(filtered_messages)
                        logging.info(f"✅ Phát hiện {len(filtered_messages)} tin nhắn mới có ý nghĩa (đã lọc {filtered_count} tin nhắn không cần thiết)")

                        # Convert messages to frontend format
                        frontend_messages = process_messages_for_frontend(filtered_messages)

                        await connection_manager.send_message(session_id, {
                        "type": "new_messages",
                        "data": {
                            "messages": frontend_messages,
                            "elapsed_time": elapsed_time,
                            "monitoring_status": "active",
                            "filtered_count": filtered_count,
                            "total_new_messages": len(new_messages)
                        }
                    })

                        # Cập nhật last_message_index chỉ với số lượng tin nhắn thực sự được gửi
                        last_message_index += len(filtered_messages)
                    else:
                        # Nếu tất cả tin nhắn đều bị filter, vẫn cập nhật index để tránh lặp lại
                        last_message_index = current_message_count - 1

                # Kiểm tra completion status
                if chat_context.get('completion_status', False):
                    logging.info("Phát hiện task completion, dừng monitoring")

                    # Convert final messages to frontend format
                    final_frontend_messages = process_messages_for_frontend(messages)

                    await connection_manager.send_message(session_id, {
                        "type": "task_completed",
                        "data": {
                            "elapsed_time": elapsed_time,
                            "completion_detected": True,
                            "monitoring_status": "completed",
                            "final_messages": final_frontend_messages # Send all messages one last time
                        }
                    })
                    break

                # Gửi heartbeat mỗi 30 giây
                if current_time - last_completion_check > 30:
                    await connection_manager.send_message(session_id, {
                        "type": "monitoring_heartbeat",
                        "data": {
                            "elapsed_time": elapsed_time,
                            "message_count": current_message_count,
                            "monitoring_status": "active",
                            "remaining_time": max_duration - elapsed_time
                        }
                    })
                    last_completion_check = current_time

                # Đợi trước khi kiểm tra lần tiếp theo
                await asyncio.sleep(monitoring_interval)

            except Exception as e:
                logging.error(f"Lỗi trong vòng lặp monitoring: {e}")
                # Thông báo lỗi cho client
                await connection_manager.send_message(session_id, {
                    "type": "monitoring_error",
                    "data": {
                        "message": f"Lỗi trong quá trình giám sát: {e}",
                        "elapsed_time": time.time() - start_time,
                        "monitoring_status": "error"
                    }
                })
                await asyncio.sleep(monitoring_interval)
                continue

    except Exception as e:
        logging.error(f"Lỗi trong monitoring real-time: {e}")

    finally:
        # Gửi thông báo kết thúc monitoring
        await connection_manager.send_message(session_id, {
            "type": "monitoring_ended",
            "data": {
                "elapsed_time": time.time() - start_time,
                "final_message_count": last_message_index + 1,
                "monitoring_status": "ended",
                "reason": "timeout" if time.time() - start_time >= max_duration else "completed"
            }
        })

        logging.info(f"Kết thúc monitoring real-time cho session {session_id}")

async def wait_for_manus_response(page: Page, timeout: int = 300) -> Optional[str]:
    """Đợi phản hồi từ Manus AI.

    Args:
        page: Đối tượng Page từ Playwright
        timeout: Thời gian tối đa đợi phản hồi (giây)

    Returns:
        Nội dung phản hồi nếu thành công, None nếu timeout
    """
    try:
        # Mảng selectors cho các chỉ báo đang nhập - sử dụng MANUS_SELECTORS
        typing_selectors = [
            MANUS_SELECTORS["LOADING"]["TYPING_INDICATOR"],
            MANUS_SELECTORS["LOADING"]["LOADING_SPINNER"],
            MANUS_SELECTORS["LOADING"]["LOADING_DOTS"],
            MANUS_SELECTORS["LOADING"]["PROCESSING"],
            # Fallback selectors
            ".typing-indicator",
            ".chat-typing-indicator",
            "div.typing",
            "[class*='typing']",
            "[class*='loading']",
            "[class*='processing']",
            "[class*='thinking']"
        ]

        # Kiểm tra các chỉ báo đang nhập
        for selector in typing_selectors:
            try:
                if await page.is_visible(selector, timeout=2000):
                    logging.info(f"Phát hiện chỉ báo đang nhập: {selector}")
                    break
            except Exception:
                continue

        # Đợi cho đến khi Manus hoàn thành nhập
        start_time = time.time()
        response_text = ""
        last_update_time = time.time()
        last_message_count = 0

        while time.time() - start_time < timeout:
            try:
                # Trích xuất tất cả tin nhắn
                messages = await extract_chat_messages(page)

                # Lọc tin nhắn của trợ lý (Manus)
                assistant_messages = [msg for msg in messages if msg.get('role') == 'assistant']

                # Kiểm tra tin nhắn mới
                if len(assistant_messages) > last_message_count:
                    # Phát hiện tin nhắn mới
                    latest_message = assistant_messages[-1]
                    new_content = latest_message.get('content', '')

                    # Cập nhật nội dung nếu có gì đó đáng giá
                    if new_content and len(new_content.strip()) > 3:
                        if new_content != response_text:
                            logging.info(f"Phát hiện nội dung mới từ Manus: {new_content[:100]}...")
                            response_text = new_content
                            last_update_time = time.time()
                            last_message_count = len(assistant_messages)

                            # Kiểm tra trạng thái hoàn thành
                            metadata = latest_message.get('metadata', {})
                            if "completion" in new_content.lower() or metadata.get("is_completion"):
                                logging.info("Phát hiện tin nhắn completion từ Manus")
                                return response_text

                # Kiểm tra thông báo hoàn thành bằng JavaScript - sử dụng MANUS_SELECTORS
                completion_info = await page.evaluate(f"""
                    () => {{
                        // Tìm các thông báo hoàn thành - sử dụng selectors từ MANUS_SELECTORS
                        const completionTexts = [
                            'Manus has completed the current task',
                            'manus has completed the current task',
                            'Task completed'
                        ];

                        // Tìm các class thông báo từ MANUS_SELECTORS
                        const completionSelectors = [
                            '{MANUS_SELECTORS["COMPLETION"]["SUCCESS_NOTIFICATION"]}',
                            '{MANUS_SELECTORS["COMPLETION"]["TASK_STATUS"]}',
                            '{MANUS_SELECTORS["COMPLETION"]["COMPLETION_BADGE"]}',
                            // Fallback selectors
                            'div[class*="text-green"]',
                            'div[class*="success"]',
                            'div[class*="completed"]',
                            'div[class*="function-success"]'
                        ];

                        // Kiểm tra bằng XPath cho text chính xác
                        try {{
                            const xpath = '{MANUS_XPATH["COMPLETION_TEXT"]}';
                            const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
                            if (result.singleNodeValue) {{
                                return {{
                                    found: true,
                                    method: 'xpath',
                                    text: result.singleNodeValue.textContent,
                                    element: result.singleNodeValue.tagName
                                }};
                            }}
                        }} catch (e) {{
                            // XPath không hỗ trợ, tiếp tục với phương pháp khác
                        }}

                        // Kiểm tra bằng CSS selectors
                        for (const selector of completionSelectors) {{
                            try {{
                                const elements = document.querySelectorAll(selector);
                                for (const el of elements) {{
                                    const text = el.textContent || '';
                                    for (const completionText of completionTexts) {{
                                        if (text.toLowerCase().includes(completionText.toLowerCase())) {{
                                            return {{
                                                found: true,
                                                method: 'selector',
                                                selector: selector,
                                                text: text,
                                                element: el.tagName
                                            }};
                                        }}
                                    }}
                                }}
                            }} catch (e) {{
                                // Selector không hợp lệ, bỏ qua
                            }}
                        }}

                        // Kiểm tra tất cả phần tử như fallback
                        const allElements = document.querySelectorAll('*');
                        for (const el of allElements) {{
                            const text = el.textContent || '';
                            const className = el.className || '';

                            // Kiểm tra text hoàn thành
                            for (const completionText of completionTexts) {{
                                if (text.toLowerCase().includes(completionText.toLowerCase())) {{
                                    return {{
                                        found: true,
                                        method: 'text_fallback',
                                        text: text,
                                        element: el.tagName
                                    }};
                                }}
                            }}
                        }}

                        return {{ found: false }};
                    }}
                """)

                if completion_info.get('found'):
                    logging.info(f"Phát hiện hoàn thành qua {completion_info.get('method')}: {completion_info.get('text', '')[:50]}")
                    return response_text

                # Kiểm tra trạng thái chờ (waiting)
                waiting_info = await page.evaluate("""
                    () => {
                        const waitTexts = [
                            'wait for user instructions',
                            'waiting for user',
                            'waiting for instructions'
                        ];

                        const allElements = document.querySelectorAll('*');
                        for (const el of allElements) {
                            const text = (el.textContent || '').toLowerCase();

                            for (const waitText of waitTexts) {
                                if (text.includes(waitText)) {
                                    return {
                                        waiting: true,
                                        text: el.textContent
                                    };
                                }
                            }
                        }

                        return { waiting: false };
                    }
                """)

                if waiting_info.get('waiting'):
                    logging.info(f"Manus đang chờ hướng dẫn: {waiting_info.get('text', '')[:50]}")
                    if response_text:
                        return response_text

                # Nếu có phản hồi và không cập nhật trong 10 giây, coi như hoàn thành
                if response_text and time.time() - last_update_time > 10:
                    logging.info("Không có cập nhật trong 10 giây, coi như đã hoàn thành")
                    break

                # Nếu quá 30 giây không có phản hồi, lấy nội dung hiện có
                if not response_text and time.time() - start_time > 30:
                    if assistant_messages:
                        response_text = assistant_messages[-1].get('content', '')
                        if response_text:
                            logging.info(f"Lấy nội dung hiện có sau 30 giây: {response_text[:100]}...")
                            break

                await asyncio.sleep(2)  # Kiểm tra mỗi 2 giây

            except Exception as e:
                logging.warning(f"Lỗi trong vòng lặp đợi phản hồi: {e}")
                await asyncio.sleep(2)
                continue

        if response_text:
            logging.info(f"Hoàn thành đợi phản hồi từ Manus: {len(response_text)} ký tự")
        else:
            logging.warning("Không nhận được phản hồi từ Manus trong thời gian timeout")

        return response_text

    except Exception as e:
        logging.error(f"Lỗi khi đợi phản hồi từ Manus AI: {e}")
        return None

async def is_duplicate_message(session_id: str, content: str, role: str = "assistant") -> bool:
    """Kiểm tra xem tin nhắn có bị trùng lặp không.

    Args:
        session_id: ID phiên chat
        content: Nội dung tin nhắn cần kiểm tra
        role: Vai trò của tin nhắn ('user', 'assistant', 'system')

    Returns:
        True nếu tin nhắn trùng lặp, False nếu không
    """
    if not session_manager:
        return False

    chat_history = await session_manager.get_chat_history(session_id)
    if not chat_history:
        return False

    # Làm sạch nội dung để so sánh
    clean_content = content
    if markdown_processor:
        clean_content = markdown_processor.clean_manus_content(content)

    # Lọc tin nhắn theo role
    role_messages = [msg for msg in chat_history if msg.get("role") == role]
    if not role_messages:
        return False

    # Kiểm tra với 3 tin nhắn gần nhất
    recent_messages = role_messages[-3:] if len(role_messages) >= 3 else role_messages

    for msg in recent_messages:
        msg_content = msg.get("content", "")
        clean_msg_content = msg_content
        if markdown_processor:
            clean_msg_content = markdown_processor.clean_manus_content(msg_content)

        # So sánh nội dung chính xác
        if clean_msg_content == clean_content:
            logging.info(f"Phát hiện tin nhắn trùng lặp chính xác với role '{role}'")
            return True

        # So sánh phần đầu của nội dung dài
        if len(clean_content) > 50 and len(clean_msg_content) > 50:
            if clean_content[:50] == clean_msg_content[:50]:
                logging.info(f"Phát hiện tin nhắn tương tự với role '{role}'")
                return True

        # Kiểm tra tin nhắn completion
        if ("completed" in clean_content.lower() and "completed" in clean_msg_content.lower() and
            "manus" in clean_content.lower() and "manus" in clean_msg_content.lower()):
            logging.info(f"Phát hiện tin nhắn completion trùng lặp với role '{role}'")
            return True

    return False

async def clean_chat_history(session_id: str):
    """Dọn dẹp lịch sử chat bằng cách loại bỏ các tin nhắn trùng lặp và không cần thiết với enhanced cleaning."""
    session = session_manager.get_session(session_id)
    if not session or not session.chat_history:
        return

    # Lưu trữ tin nhắn đã xử lý để kiểm tra trùng lặp
    processed_messages = {}
    cleaned_history = []
    filtered_count = 0

    for message in session.chat_history:
        role = message.get("role")
        content = message.get("content", "")

        # Tạo ChatMessage object để sử dụng enhanced filtering
        chat_msg = ChatMessage(
            role=role,
            content=content,
            timestamp=message.get("timestamp"),
            metadata=message.get("metadata", {})
        )

        # STEP 1: Kiểm tra xem tin nhắn có ý nghĩa không (với logic đặc biệt cho user)
        if chat_msg.role == "user":
            if not chat_msg.is_meaningful_user_message():
                filtered_count += 1
                logging.info(f"Cleaned meaningless user message: {content[:30]}...")
                continue
        else:
            if not chat_msg.is_meaningful_message():
                filtered_count += 1
                logging.info(f"Cleaned meaningless message: {content[:30]}...")
                continue

        # Clean content for comparison
        clean_content = markdown_processor.clean_manus_content(content)

        # Tạo khóa duy nhất cho mỗi cặp role-content (sử dụng clean content)
        key = f"{role}:{clean_content}"

        # STEP 2: Kiểm tra trùng lặp với enhanced logic
        is_duplicate = False

        # Check exact duplicates
        if key in processed_messages:
            is_duplicate = True
        else:
            # Check for similar content in same role using enhanced duplicate detection
            for existing_msg in cleaned_history:
                existing_chat_msg = ChatMessage(
                    role=existing_msg.get("role"),
                    content=existing_msg.get("content", ""),
                    timestamp=existing_msg.get("timestamp"),
                    metadata=existing_msg.get("metadata", {})
                )

                if chat_msg.is_duplicate_of(existing_chat_msg):
                    is_duplicate = True
                    filtered_count += 1
                    logging.info(f"Cleaned duplicate message: {content[:30]}...")
                    break

        # Nếu tin nhắn chưa xuất hiện trước đó, thêm vào lịch sử đã làm sạch
        if not is_duplicate:
            processed_messages[key] = True
            # Process message with markdown before adding to history
            processed_message = markdown_processor.process_message(message)
            cleaned_history.append(processed_message)

    # Cập nhật lịch sử chat
    original_length = len(session.chat_history)
    if len(cleaned_history) < original_length:
        session.chat_history = cleaned_history
        removed_count = original_length - len(cleaned_history)
        logging.info(f"🧹 Đã dọn dẹp lịch sử chat: Loại bỏ {removed_count} tin nhắn không cần thiết (còn lại {len(cleaned_history)}/{original_length})")
    else:
        logging.info(f"✅ Lịch sử chat đã sạch: {len(cleaned_history)} tin nhắn có ý nghĩa")

async def cleanup_chrome_profile(profile_path: str) -> bool:
    """Dọn dẹp Chrome profile để khắc phục lỗi profile bị khóa.

    Args:
        profile_path: Đường dẫn đến thư mục profile Chrome

    Returns:
        True nếu dọn dẹp thành công, False nếu thất bại
    """
    try:
        logging.info(f"Bắt đầu dọn dẹp Chrome profile tại: {profile_path}")

        # Kiểm tra xem profile có tồn tại không
        if not os.path.exists(profile_path):
            logging.warning(f"Profile không tồn tại: {profile_path}")
            return False

        # Dừng các tiến trình Chrome đang chạy
        await kill_chrome_processes()

        # Danh sách các file và thư mục cần xóa
        lock_files = [
            os.path.join(profile_path, "SingletonLock"),
            os.path.join(profile_path, "SingletonCookie"),
            os.path.join(profile_path, "SingletonSocket")
        ]

        lock_patterns = [
            os.path.join(profile_path, "*.lock"),
            os.path.join(profile_path, "*.tmp"),
            os.path.join(profile_path, "Crashed*"),
            os.path.join(profile_path, "crash_dumps"),
            os.path.join(profile_path, "Session Storage"),
            os.path.join(profile_path, "Sessions")
        ]

        # Xóa các file lock cụ thể
        deleted_count = 0
        for lock_file in lock_files:
            if os.path.exists(lock_file):
                try:
                    os.remove(lock_file)
                    logging.info(f"Đã xóa file: {lock_file}")
                    deleted_count += 1
                except Exception as e:
                    logging.warning(f"Không thể xóa {lock_file}: {e}")

        # Xóa các file và thư mục match với pattern
        for pattern in lock_patterns:
            for item_path in glob.glob(pattern):
                try:
                    if os.path.isfile(item_path):
                        os.remove(item_path)
                        logging.info(f"Đã xóa file: {item_path}")
                    elif os.path.isdir(item_path):
                        shutil.rmtree(item_path, ignore_errors=True)
                        logging.info(f"Đã xóa thư mục: {item_path}")
                    deleted_count += 1
                except Exception as e:
                    logging.warning(f"Không thể xóa {item_path}: {e}")

        # Đợi một chút để đảm bảo các thay đổi được áp dụng
        await asyncio.sleep(1)

        logging.info(f"Đã hoàn tất dọn dẹp Chrome profile, đã xóa {deleted_count} items")
        return True
    except Exception as e:
        logging.error(f"Lỗi khi dọn dẹp Chrome profile: {e}")
        return False

async def kill_chrome_processes() -> None:
    """Dừng tất cả các tiến trình Chrome đang chạy."""
    try:
        if os.name == 'nt':  # Windows
            subprocess.run(['taskkill', '/F', '/IM', 'chrome.exe', '/T'],
                          stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            subprocess.run(['taskkill', '/F', '/IM', 'chromium.exe', '/T'],
                          stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        else:  # Linux/Mac
            subprocess.run(['pkill', '-f', 'chrome'],
                          stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            subprocess.run(['pkill', '-f', 'chromium'],
                          stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

        # Đợi một chút để đảm bảo các tiến trình đã được dừng hẳn
        await asyncio.sleep(1)
        logging.info("Đã dừng các tiến trình Chrome đang chạy")
    except Exception as e:
        logging.error(f"Lỗi khi dừng tiến trình Chrome: {e}")

async def process_chat_message(session_id: str, message: str, profile_name: str = None, enable_realtime_monitoring: bool = True) -> Dict[str, Any]:
    """Xử lý tin nhắn chat và tương tác với Manus AI với real-time monitoring."""
    # Xác định đường dẫn Chrome profile
    chrome_profile_path = None
    if profile_name:
        # Sử dụng thư mục profile được chỉ định
        from .main import CUSTOM_PROFILE_DIR
        chrome_profile_path = os.path.join(CUSTOM_PROFILE_DIR, profile_name)
    else:
        # Sử dụng thư mục profile mặc định cho session_id
        from .main import CUSTOM_PROFILE_DIR
        chrome_profile_path = os.path.join(CUSTOM_PROFILE_DIR, f"manus_profile_{session_id}")

    # Kiểm tra xem có profile "my_profile" không
    my_profile_path = os.path.join(CUSTOM_PROFILE_DIR, "my_profile")
    if os.path.exists(my_profile_path) and os.path.isdir(my_profile_path):
        chrome_profile_path = my_profile_path
        logging.info(f"Sử dụng Chrome profile đã tồn tại: {chrome_profile_path}")
    else:
        # Đảm bảo thư mục profile tồn tại
        os.makedirs(chrome_profile_path, exist_ok=True)
        logging.info(f"Tạo mới Chrome profile: {chrome_profile_path}")

    # Lấy phiên hiện tại
    session = session_manager.get_session(session_id)

    # Kiểm tra nếu session đã tồn tại nhưng page đã đóng
    if session and (not session.page or session.page.is_closed()):
        logging.warning(f"Phát hiện session {session_id} có page đã đóng, đóng session cũ")
        await session_manager.close_session(session_id)
        session = None

    # Nếu phiên không tồn tại hoặc có lỗi, thử làm sạch profile và tạo phiên mới
    max_retries = 2
    retry_count = 0

    while retry_count < max_retries and (not session or not session.page or not session.context):
        if retry_count > 0:
            # Dọn dẹp Chrome profile trước khi thử lại
            await cleanup_chrome_profile(chrome_profile_path)
            logging.info(f"Thử lại lần {retry_count}: Tạo lại phiên sau khi dọn dẹp profile")

        try:
            # Tạo phiên mới
            session = await session_manager.create_session(
                session_id=session_id,
                chrome_profile_path=chrome_profile_path,
                headless=False,  # Hiển thị trình duyệt để dễ debug
                context_options={
                    "viewport": {"width": 1280, "height": 720},
                    "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                }
            )

            # Điều hướng đến Manus AI
            success = await navigate_to_manus(session.page)
            if not success:
                logging.error("Không thể kết nối đến Manus AI")
                if retry_count < max_retries - 1:
                    retry_count += 1
                    await asyncio.sleep(2)
                    continue
                else:
                    return {"status": "error", "message": "Không thể kết nối đến Manus AI sau nhiều lần thử"}
            else:
                # Nếu kết nối thành công, thoát khỏi vòng lặp
                break

        except Exception as e:
            logging.error(f"Lỗi khi tạo phiên: {str(e)}")
            if "The profile appears to be in use by another Chromium process" in str(e):
                logging.warning("Phát hiện lỗi profile bị khóa bởi tiến trình khác")
                if retry_count < max_retries - 1:
                    retry_count += 1
                    await asyncio.sleep(2)
                    continue
                else:
                    return {"status": "error", "message": f"Profile Chrome bị khóa bởi tiến trình khác. Lỗi: {str(e)}"}
            else:
                return {"status": "error", "message": f"Lỗi không xác định khi tạo phiên: {str(e)}"}

        retry_count += 1

    # Nếu vẫn không thể tạo phiên sau khi đã thử lại
    if not session or not session.page or not session.context:
        return {"status": "error", "message": "Không thể tạo phiên sau nhiều lần thử"}

    # Kiểm tra xem page có còn mở không
    try:
        if session.page.is_closed():
            logging.error(f"Page đã đóng cho session {session_id}")
            return {"status": "error", "message": "Kết nối trình duyệt đã đóng"}
    except Exception as e:
        logging.error(f"Lỗi khi kiểm tra trạng thái page: {str(e)}")
        return {"status": "error", "message": f"Không thể kiểm tra trạng thái trình duyệt: {str(e)}"}

    # Tạo tin nhắn người dùng với frontend format
    user_message = ChatMessage.create_frontend_message(content=message, role="user")
    user_dict = user_message.to_dict()

    # Kiểm tra xem tin nhắn người dùng có bị trùng lặp không
    is_duplicate = await is_duplicate_message(session_id, message, role="user")

    # Chỉ cập nhật lịch sử nếu không phải tin nhắn trùng lặp
    if not is_duplicate:
        # Cập nhật lịch sử cuộc trò chuyện
        await session_manager.update_chat_history(session_id, user_dict)

    # Gửi thông báo bắt đầu xử lý với frontend format
    await connection_manager.send_message(session_id, {
        "type": "processing_started",
        "data": {
            "message": "Đang gửi tin nhắn đến Manus AI...",
            "user_message": user_dict,
            "realtime_monitoring": enable_realtime_monitoring
        }
    })

    try:
        # Gửi tin nhắn đến Manus AI
        success = await send_message_to_manus(session.page, message)
        if not success:
            logging.error("Không thể gửi tin nhắn đến Manus AI")
            return {"status": "error", "message": "Không thể gửi tin nhắn đến Manus AI"}

        # Bắt đầu real-time monitoring nếu được bật
        if enable_realtime_monitoring:
            # Gửi thông báo bắt đầu monitoring
            await connection_manager.send_message(session_id, {
                "type": "monitoring_started",
                "data": {
                    "message": "Bắt đầu giám sát real-time Manus interface...",
                    "monitoring_duration": 900  # 15 phút
                }
            })

            # Bắt đầu monitoring task trong background
            asyncio.create_task(
                monitor_manus_realtime(session.page, session_id, max_duration=900)
            )

            # Đợi một chút để monitoring bắt đầu
            await asyncio.sleep(2)

            # Trích xuất ngữ cảnh ban đầu
            try:
                initial_context = await extract_complete_chat_context(session.page)

                await connection_manager.send_message(session_id, {
                    "type": "initial_context",
                    "data": {
                        "chat_context": initial_context,
                        "message": "Đã gửi tin nhắn, đang chờ phản hồi từ Manus AI..."
                    }
                })
            except Exception as extract_error:
                logging.error(f"Lỗi khi trích xuất ngữ cảnh ban đầu: {str(extract_error)}")
                initial_context = {
                    "extracted_at": datetime.now().isoformat(),
                    "error": str(extract_error),
                    "messages": [],
                    "completion_status": False
                }

                await connection_manager.send_message(session_id, {
                    "type": "extract_error",
                    "data": {
                        "message": f"Đã gửi tin nhắn nhưng không thể trích xuất ngữ cảnh: {str(extract_error)}",
                        "error": str(extract_error)
                    }
                })

            return {
                "status": "success",
                "message": "Tin nhắn đã được gửi, đang giám sát real-time",
                "monitoring_active": True,
                "initial_context": initial_context,
                "history": await session_manager.get_chat_history(session_id)
            }
        else:
            # Sử dụng phương pháp cũ nếu không bật real-time monitoring
            response = await wait_for_manus_response(session.page)

            if response:
                # Trích xuất tất cả tin nhắn từ cuộc trò chuyện
                extracted_messages = await extract_chat_messages(session.page)

                # Tìm tin nhắn AI mới nhất
                ai_messages = [msg for msg in extracted_messages if msg.get("role") == "assistant"]
                if ai_messages:
                    latest_ai_message = ai_messages[-1]
                    response_content = latest_ai_message.get("content")
                    timestamp_text = latest_ai_message.get("timestamp")

                    # Tạo tin nhắn phản hồi với frontend format
                    assistant_message = ChatMessage.create_frontend_message(
                        content=response_content,
                        role="assistant",
                        timestamp_str=timestamp_text
                    )
                    assistant_dict = assistant_message.to_dict()

                    # Process with markdown
                    assistant_dict = markdown_processor.process_message(assistant_dict)

                    # Thêm timestamp nếu có
                    if timestamp_text:
                        assistant_dict["display_time"] = timestamp_text

                    # Kiểm tra xem tin nhắn này đã tồn tại trong lịch sử chưa
                    is_duplicate = await is_duplicate_message(session_id, response_content)

                    # Chỉ cập nhật lịch sử nếu không phải tin nhắn trùng lặp
                    if not is_duplicate:
                        # Cập nhật lịch sử cuộc trò chuyện
                        await session_manager.update_chat_history(session_id, assistant_dict)
                        logging.info(f"Phản hồi từ Manus AI: {response_content[:100]}...")

                    # Kiểm tra thông báo hoàn thành
                    completion_messages = [msg for msg in extracted_messages if msg.get("role") == "system" and "completed" in msg.get("content", "")]
                    has_completion = len(completion_messages) > 0

                    return {
                        "status": "success",
                        "message": assistant_dict,
                        "history": await session_manager.get_chat_history(session_id),
                        "task_completed": has_completion,
                        "monitoring_active": False
                    }
                else:
                    # Sử dụng phản hồi từ wait_for_manus_response nếu không tìm thấy tin nhắn AI
                    assistant_message = ChatMessage.create_frontend_message(
                        content=response,
                        role="assistant"
                    )
                    assistant_dict = assistant_message.to_dict()

                    # Process with markdown
                    assistant_dict = markdown_processor.process_message(assistant_dict)

                    # Kiểm tra xem tin nhắn này đã tồn tại trong lịch sử chưa
                    is_duplicate = await is_duplicate_message(session_id, response)

                    # Chỉ cập nhật lịch sử nếu không phải tin nhắn trùng lặp
                    if not is_duplicate:
                        # Cập nhật lịch sử cuộc trò chuyện
                        await session_manager.update_chat_history(session_id, assistant_dict)
                        logging.info(f"Phản hồi từ Manus AI: {response[:100]}...")

                    return {
                        "status": "success",
                        "message": assistant_dict,
                        "history": await session_manager.get_chat_history(session_id),
                        "monitoring_active": False
                    }
    except Exception as e:
        logging.error(f"Lỗi khi xử lý tin nhắn: {str(e)}")
        await connection_manager.send_message(session_id, {
            "type": "processing_error",
            "data": {
                "message": f"Lỗi khi xử lý tin nhắn: {str(e)}",
                "error": str(e)
            }
        })
        return {"status": "error", "message": f"Lỗi khi xử lý tin nhắn: {str(e)}"}

async def handle_chat_websocket(websocket, session_id: str):
    """Xử lý kết nối WebSocket cho chat."""
    try:
        # Kết nối WebSocket
        await connection_manager.connect(websocket, session_id)

        # Dọn dẹp lịch sử chat để loại bỏ các tin nhắn trùng lặp
        await clean_chat_history(session_id)

        # Gửi lịch sử cuộc trò chuyện nếu có
        history = await session_manager.get_chat_history(session_id)
        if history:
            await connection_manager.send_message(session_id, {
                "type": "history",
                "data": history
            })

        # Xử lý tin nhắn từ client
        async for data in websocket.iter_json():
            message_type = data.get("type")

            if message_type == "message":
                user_message = data.get("message")

                # Bắt đầu task xử lý tin nhắn
                async def process_message_task():
                    # Gửi trạng thái đang xử lý
                    await connection_manager.send_message(session_id, {
                        "type": "status",
                        "data": "processing"
                    })

                    # Xử lý tin nhắn
                    # Kiểm tra xem có thông tin profile_name không
                    profile_name = data.get("profile_name")
                    enable_realtime = data.get("enable_realtime_monitoring", True)

                    # Thêm xử lý lỗi profile bị khóa
                    try:
                        result = await process_chat_message(
                            session_id,
                            user_message,
                            profile_name,
                            enable_realtime_monitoring=enable_realtime
                        )
                    except Exception as e:
                        logging.error(f"Lỗi khi xử lý tin nhắn: {str(e)}")

                        # Kiểm tra nếu lỗi liên quan đến profile bị khóa
                        if "The profile appears to be in use by another Chromium process" in str(e):
                            # Thử dọn dẹp profile và thử lại
                            from .main import CUSTOM_PROFILE_DIR
                            profile_path = os.path.join(CUSTOM_PROFILE_DIR, profile_name or f"manus_profile_{session_id}")

                            # Gửi thông báo đang cố gắng khắc phục lỗi
                            await connection_manager.send_message(session_id, {
                                "type": "status",
                                "data": "cleaning_profile"
                            })

                            # Dọn dẹp profile
                            await cleanup_chrome_profile(profile_path)

                            # Thử lại
                            result = await process_chat_message(
                                session_id,
                                user_message,
                                profile_name,
                                enable_realtime_monitoring=enable_realtime
                            )
                        else:
                            result = {
                                "status": "error",
                                "message": f"Lỗi khi xử lý tin nhắn: {str(e)}"
                            }

                    # Gửi kết quả về client
                    await connection_manager.send_message(session_id, {
                        "type": "response",
                        "data": result
                    })

                await connection_manager.start_task(session_id, process_message_task())

            elif message_type == "cleanup_profile":
                # Xử lý yêu cầu dọn dẹp profile
                async def cleanup_profile_task():
                    from .main import CUSTOM_PROFILE_DIR
                    profile_path = data.get("profile_path")

                    if not profile_path:
                        profile_path = os.path.join(CUSTOM_PROFILE_DIR, "my_profile")

                    # Gửi thông báo đang dọn dẹp
                    await connection_manager.send_message(session_id, {
                        "type": "status",
                        "data": "cleaning_profile"
                    })

                    # Dọn dẹp profile
                    success = await cleanup_chrome_profile(profile_path)

                    # Gửi kết quả
                    await connection_manager.send_message(session_id, {
                        "type": "cleanup_result",
                        "data": {
                            "success": success,
                            "profile_path": profile_path,
                            "message": "Đã dọn dẹp profile thành công" if success else "Không thể dọn dẹp profile"
                        }
                    })

                await connection_manager.start_task(session_id, cleanup_profile_task())

            elif message_type == "ping":
                # Phản hồi ping để giữ kết nối
                await connection_manager.send_message(session_id, {
                    "type": "pong",
                    "data": {"timestamp": time.time()}
                })

    except Exception as e:
        logging.error(f"Lỗi trong WebSocket: {str(e)}")
    finally:
        # Xử lý ngắt kết nối
        await connection_manager.handle_websocket_disconnect(session_id)

# --- Test Functions for Frontend Format ---
def test_frontend_format():
    """Test function để kiểm tra frontend format."""
    print("=== Testing Frontend Format ===")

    # Test demo messages
    demo_messages = create_demo_messages()
    print(f"\nDemo messages created: {len(demo_messages)} messages")

    for i, msg in enumerate(demo_messages):
        print(f"\nMessage {i+1}:")
        print(f"  ID: {msg['id']}")
        print(f"  Content Type: {msg['content_type']}")
        print(f"  Sender: {msg['sender']}")
        print(f"  Content: {msg['content'][:100]}...")

        if msg['content_type'] == 'plan' and 'plan_data' in msg:
            print(f"  Plan Title: {msg['plan_data']['title']}")
            print(f"  Plan Steps: {len(msg['plan_data']['steps'])}")
        elif msg['content_type'] == 'code' and 'code_data' in msg:
            print(f"  Code Language: {msg['code_data']['language']}")
            print(f"  Code Length: {len(msg['code_data']['code'])} chars")
        elif msg['content_type'] == 'file_attachment' and 'file_data' in msg:
            print(f"  File: {msg['file_data']['filename']}")
            print(f"  Size: {msg['file_data']['size']}")
        elif msg['content_type'] == 'completion' and 'completion_data' in msg:
            print(f"  Completion: {msg['completion_data']['title']}")
            print(f"  Deliverables: {len(msg['completion_data']['deliverables'])}")

    print("\n=== Frontend Format Test Complete ===")
    return demo_messages

if __name__ == "__main__":
    # Run test when script is executed directly
    test_frontend_format()
