from fastapi import FastAPI, WebSocket, Request, HTTPException, Depends
from fastapi.responses import HTMLResponse, JSONResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import asyncio
import os
import platform
import uuid
from pathlib import Path
from typing import Optional, List, Dict, Any
from .playwright_utils import minus_login_and_chat, get_stealth_config
from .proxy_manager import proxy_manager, get_proxy_config
from .test_playwright import check_chrome_profile, send_message, try_google_login, is_logged_in, setup_xserver, cleanup_browser
from .profile_setup import setup_chrome_profile_with_websocket
from .session_manager import session_manager, PlaywrightSession
from playwright.async_api import async_playwright
import random
import logging
from fastapi.middleware.cors import CORSMiddleware
import time
import json
import datetime
import starlette.websockets

logging.basicConfig(level=logging.INFO)

app = FastAPI()

# C<PERSON>u hình CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Tạm thời cho phép tất cả để test
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Khởi tạo SessionManager khi ứng dụng khởi động
@app.on_event("startup")
async def startup_event():
    """Khởi tạo các tài nguyên khi ứng dụng khởi động."""
    logging.info("Khởi tạo SessionManager...")
    await session_manager.initialize()
    logging.info("SessionManager đã được khởi tạo")

# Đóng SessionManager khi ứng dụng tắt
@app.on_event("shutdown")
async def shutdown_event():
    """Dọn dẹp tài nguyên khi ứng dụng tắt."""
    logging.info("Đóng SessionManager...")
    await session_manager.shutdown()
    logging.info("SessionManager đã được đóng")

# Xác định thư mục mặc định cho Chrome profile dựa trên hệ điều hành
system = platform.system()
if system == "Windows":
    DEFAULT_PROFILE_DIR = os.path.join(os.path.expanduser("~"), "AppData", "Local", "Google", "Chrome", "User Data")
elif system == "Darwin":  # macOS
    DEFAULT_PROFILE_DIR = os.path.join(os.path.expanduser("~"), "Library", "Application Support", "Google", "Chrome")
elif system == "Linux":
    DEFAULT_PROFILE_DIR = os.path.join(os.path.expanduser("~"), ".config", "google-chrome")
else:
    DEFAULT_PROFILE_DIR = os.path.join(os.path.expanduser("~"), "chrome_profiles")

# Thay đổi đường dẫn Chrome profiles
CUSTOM_PROFILE_DIR = "/root/chrome_profiles"
os.makedirs(CUSTOM_PROFILE_DIR, exist_ok=True)

# Tạo thư mục chrome_profiles nếu không tồn tại
os.makedirs(CUSTOM_PROFILE_DIR, exist_ok=True)

# Tích hợp noVNC qua FastAPI
app.mount("/novnc", StaticFiles(directory="/usr/share/novnc"), name="novnc")

class MinusChatRequest(BaseModel):
    username: str
    password: str
    chat_content: str
    proxy_id: Optional[str] = None
    proxy_country: Optional[str] = None
    proxy_host: Optional[str] = None
    proxy_port: Optional[str] = None
    proxy_username: Optional[str] = None
    proxy_password: Optional[str] = None

class MinusChatWithProfileRequest(BaseModel):
    chat_content: str
    chrome_profile: Optional[str] = None
    profile_name: Optional[str] = None
    profile_dir: Optional[str] = CUSTOM_PROFILE_DIR
    wait_time: Optional[int] = 5
    headless: Optional[bool] = True
    proxy_id: Optional[str] = None
    proxy_country: Optional[str] = None
    proxy_host: Optional[str] = None
    proxy_port: Optional[str] = None
    proxy_username: Optional[str] = None
    proxy_password: Optional[str] = None
    use_stealth: Optional[bool] = True
    use_system_profile_dir: Optional[bool] = False  # Sử dụng thư mục profile hệ thống thay vì thư mục tùy chỉnh

class ProxyModel(BaseModel):
    id: str
    name: str
    username: str
    password: str
    host: str
    port: str
    country: str
    status: str
    is_active: str
    valid_from: str
    valid_to: str

class ProxyCreateModel(BaseModel):
    name: str
    username: str
    password: str
    host: str
    port: str
    country: str
    status: str = "1"
    is_active: str = "1"
    valid_from: str
    valid_to: str

class ProxyUpdateModel(BaseModel):
    name: Optional[str] = None
    username: Optional[str] = None
    password: Optional[str] = None
    host: Optional[str] = None
    port: Optional[str] = None
    country: Optional[str] = None
    status: Optional[str] = None
    is_active: Optional[str] = None
    valid_from: Optional[str] = None
    valid_to: Optional[str] = None

@app.get("/")
def read_root():
    return {"message": "FastAPI backend is running!"}

@app.websocket("/ws/chat/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """Endpoint WebSocket cho chat với Manus AI."""
    from .manus_chat import handle_chat_websocket

    # Nếu session_id không được cung cấp, tạo một ID mới
    if not session_id or session_id == "undefined":
        session_id = str(uuid.uuid4())

    # Xử lý kết nối WebSocket
    await handle_chat_websocket(websocket, session_id)

@app.get("/vnc", response_class=HTMLResponse)
async def vnc():
    """Endpoint để truy cập VNC qua FastAPI - cùng port 8000"""
    return """
    <html>
        <head>
            <title>YouHome VNC Viewer</title>
            <style>
                body, html {
                    margin: 0;
                    padding: 0;
                    height: 100%;
                    overflow: hidden;
                }
                iframe {
                    width: 100%;
                    height: 100%;
                    border: none;
                }
            </style>
        </head>
        <body>
            <iframe src="/novnc/vnc.html?autoconnect=true&resize=scale&reconnect=true&password=playwright"></iframe>
        </body>
    </html>
    """

@app.get("/websockify", include_in_schema=False)
def websockify():
    # Chuyển hướng đến websocket noVNC
    return RedirectResponse(url="ws://localhost:6080/")

@app.get("/chrome-profile-info")
def chrome_profile_info():
    """Trả về thông tin về thư mục Chrome profile mặc định và tùy chỉnh"""
    return {
        "system_profile_dir": DEFAULT_PROFILE_DIR,
        "custom_profile_dir": CUSTOM_PROFILE_DIR,
        "system": platform.system(),
        "platform": platform.platform()
    }

@app.post("/minus-chat")
async def minus_chat(data: MinusChatRequest):
    # Nếu không cung cấp thông tin proxy trực tiếp, thử lấy từ proxy manager
    proxy_config = None
    if data.proxy_id or data.proxy_country:
        proxy_config = get_proxy_config(proxy_id=data.proxy_id, country=data.proxy_country)
    elif data.proxy_host and data.proxy_port:
        proxy_config = {
            'host': data.proxy_host,
            'port': data.proxy_port,
            'username': data.proxy_username,
            'password': data.proxy_password
        }
    else:
        # Sử dụng proxy mặc định
        proxy_config = get_proxy_config()

    if not proxy_config:
        return {"status": "error", "message": "Không tìm thấy proxy phù hợp"}

    messages = await minus_login_and_chat(
        data.username, data.password, data.chat_content,
        proxy_host=proxy_config.get('host'),
        proxy_port=proxy_config.get('port'),
        proxy_username=proxy_config.get('username'),
        proxy_password=proxy_config.get('password')
    )
    return {"messages": messages}

@app.post("/minus-chat-profile")
async def minus_chat_profile(data: MinusChatWithProfileRequest):
    """
    API để gửi tin nhắn tới Minus sử dụng Chrome profile

    Quy trình:
    1. Xác định đường dẫn Chrome profile (từ tham số hoặc tạo từ tên profile)
    2. Kiểm tra tính hợp lệ của profile
    3. Thiết lập X server (nếu cần)
    4. Khởi động trình duyệt với profile đã chọn
    5. Kiểm tra trạng thái đăng nhập và đăng nhập bằng Google nếu cần
    6. Gửi tin nhắn và trả về kết quả
    """
    # Xử lý proxy
    proxy_config = None
    if data.proxy_id or data.proxy_country:
        proxy_config = get_proxy_config(proxy_id=data.proxy_id, country=data.proxy_country)
    elif data.proxy_host and data.proxy_port:
        proxy_config = {
            'host': data.proxy_host,
            'port': data.proxy_port,
            'username': data.proxy_username,
            'password': data.proxy_password
        }

    # Xử lý Chrome profile
    chrome_profile_path = data.chrome_profile

    if data.profile_name and not chrome_profile_path:
        # Chọn thư mục profile dựa trên tùy chọn
        profile_base_dir = DEFAULT_PROFILE_DIR if data.use_system_profile_dir else data.profile_dir
        profile_dir = profile_base_dir if profile_base_dir else CUSTOM_PROFILE_DIR
        chrome_profile_path = os.path.join(profile_dir, data.profile_name)

    # Khởi tạo kết quả mặc định
    result = {
        "status": "error",
        "message": "Gửi tin nhắn thất bại",
        "is_logged_in": False,
        "message_sent": False,
        "chrome_profile_valid": False,
        "chrome_profile_path": chrome_profile_path
    }

    # Kiểm tra Chrome profile có hợp lệ không
    if chrome_profile_path:
        result["chrome_profile_valid"] = check_chrome_profile(chrome_profile_path)
        if not result["chrome_profile_valid"]:
            result["message"] = f"Chrome profile không hợp lệ: {chrome_profile_path}"
            return result
    else:
        result["message"] = "Không có Chrome profile nào được chỉ định"
        return result

    # Thiết lập X server
    await setup_xserver()

    # Sử dụng biến môi trường để kiểm soát chế độ headless
    headless = data.headless

    browser = None
    context = None

    try:
        async with async_playwright() as p:
            # Tham số cần thiết cho browser để tránh phát hiện tự động hóa
            browser_args = [
                '--disable-gpu',
                '--disable-dev-shm-usage',
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=IsolateOrigins,site-per-process',
                '--ignore-certificate-errors',
                '--disable-extensions',
                '--disable-infobars',
                '--start-maximized',
                '--window-size=1280,720',
                '--disable-automation',
                '--disable-blink-features',
                '--disable-sync',
                '--disable-translate',
                '--disable-notifications',
                '--mute-audio',
                '--no-first-run',
                '--no-default-browser-check',
            ]

            # Thiết lập context options
            context_options = {}

            # Thiết lập proxy nếu có
            playwright_proxy = None
            if proxy_config:
                playwright_proxy = {
                    "server": f"http://{proxy_config.get('host')}:{proxy_config.get('port')}"
                }
                if proxy_config.get('username') and proxy_config.get('password'):
                    playwright_proxy["username"] = proxy_config.get('username')
                    playwright_proxy["password"] = proxy_config.get('password')

            # Slow_mo để làm chậm các thao tác trong mode headless
            slow_mo = random.randint(50, 150) if headless else 0

            # Áp dụng cấu hình stealth mode
            if data.use_stealth:
                stealth_config = get_stealth_config()
                context_options.update({
                    "viewport": stealth_config["viewport"],
                    "device_scale_factor": stealth_config["device_scale_factor"],
                    "locale": stealth_config["locale"],
                    "timezone_id": stealth_config["timezone_id"],
                    "user_agent": stealth_config["user_agent"]
                })

            # Khởi động browser với Chrome profile
            logging.info(f"Khởi động trình duyệt với Chrome profile: {chrome_profile_path}")
            context = await p.chromium.launch_persistent_context(
                user_data_dir=chrome_profile_path,
                headless=headless,
                args=browser_args,
                proxy=playwright_proxy,
                slow_mo=slow_mo,
                **context_options
            )
            browser = context.browser

            # Thêm JavaScript stealth để tránh phát hiện tự động hóa
            if data.use_stealth:
                await context.add_init_script("""
                // Ẩn thuộc tính webdriver
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => false,
                });

                // Tạo plugins và mimeTypes giả
                const mockPlugins = [{ description: "PDF Viewer", filename: "internal-pdf-viewer", name: "Chrome PDF Viewer" }];
                Object.defineProperty(navigator, 'plugins', {
                    get: () => Object.defineProperties({}, {
                        length: { value: mockPlugins.length },
                        item: { value: (index) => mockPlugins[index] },
                        namedItem: { value: (name) => mockPlugins.find(plugin => plugin.name === name) }
                    })
                });

                // Thiết lập giá trị ngẫu nhiên cho thuộc tính phần cứng
                const getRandomInt = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;
                Object.defineProperty(navigator, 'hardwareConcurrency', { get: () => getRandomInt(4, 16) });
                Object.defineProperty(navigator, 'deviceMemory', { get: () => getRandomInt(4, 32) });
                """)

            # Mở trang mới
            page = await context.new_page()
            logging.info("Mở trang mới...")

            # Thử truy cập trực tiếp vào trang chính trước
            logging.info("Thử truy cập trực tiếp vào trang chính Manus...")
            try:
                await page.goto("https://manus.im/app", timeout=60000)
                await page.wait_for_load_state('networkidle', timeout=60000)

                # Kiểm tra URL sau khi chuyển hướng
                current_url = page.url
                logging.info(f"URL hiện tại sau khi truy cập trang chính: {current_url}")

                if '/app' in current_url or 'dashboard' in current_url:
                    logging.info("Đã truy cập thành công vào trang chính!")
                    is_logged = True
                    result["is_logged_in"] = True
                    result["message"] = "Đã truy cập thành công vào trang chính"
                else:
                    # Nếu bị chuyển hướng đến trang đăng nhập, thực hiện quy trình đăng nhập
                    logging.info("Bị chuyển hướng đến trang đăng nhập, thực hiện quy trình đăng nhập...")

                    # Đợi trang đăng nhập tải xong
                    await page.wait_for_load_state('networkidle', timeout=60000)

                    # Kiểm tra trạng thái đăng nhập
                    is_logged = await is_logged_in(page)
                    result["is_logged_in"] = is_logged

                    if is_logged:
                        logging.info("Đã đăng nhập thành công!")
                        result["message"] = "Đã đăng nhập thành công"
                    else:
                        logging.info("Chưa đăng nhập, thử đăng nhập bằng Google...")

                        # Thử đăng nhập bằng Google
                        google_login_attempted = await try_google_login(page)

                        if google_login_attempted:
                            logging.info("Đã thử đăng nhập bằng Google, kiểm tra lại trạng thái...")
                            # Đợi đủ thời gian để quá trình đăng nhập hoàn tất
                            await asyncio.sleep(5)

                            # Kiểm tra lại trạng thái đăng nhập
                            is_logged = await is_logged_in(page)
                            result["is_logged_in"] = is_logged

                            if is_logged:
                                logging.info("Đăng nhập thành công sau khi thử đăng nhập bằng Google!")
                                result["message"] = "Đăng nhập thành công sau khi thử đăng nhập bằng Google"
                            else:
                                # Thử truy cập lại trang chính
                                try:
                                    logging.info("Vẫn chưa đăng nhập, thử truy cập lại trang chính...")
                                    await page.goto("https://manus.im/app", timeout=60000)
                                    await page.wait_for_load_state('networkidle', timeout=60000)

                                    # Kiểm tra URL sau khi chuyển hướng
                                    if '/app' in page.url or 'dashboard' in page.url:
                                        logging.info("Đã truy cập thành công vào trang chính!")
                                        is_logged = True
                                        result["is_logged_in"] = True
                                        result["message"] = "Đã truy cập thành công vào trang chính"
                                    else:
                                        logging.info("Vẫn chưa đăng nhập sau khi thử tất cả các phương pháp.")
                                        result["message"] = "Vẫn chưa đăng nhập sau khi thử tất cả các phương pháp"
                                        await cleanup_browser(context, browser, chrome_profile_path)
                                        return result
                                except Exception as e:
                                    logging.error(f"Lỗi khi truy cập lại trang chính: {str(e)}")
                                    result["message"] = "Vẫn chưa đăng nhập sau khi thử đăng nhập bằng Google"
                                    await cleanup_browser(context, browser, chrome_profile_path)
                                    return result
                        else:
                            # Thử truy cập lại trang chính
                            try:
                                logging.info("Không thể đăng nhập bằng Google, thử truy cập lại trang chính...")
                                await page.goto("https://manus.im/app", timeout=60000)
                                await page.wait_for_load_state('networkidle', timeout=60000)

                                # Kiểm tra URL sau khi chuyển hướng
                                if '/app' in page.url or 'dashboard' in page.url:
                                    logging.info("Đã truy cập thành công vào trang chính!")
                                    is_logged = True
                                    result["is_logged_in"] = True
                                    result["message"] = "Đã truy cập thành công vào trang chính"
                                else:
                                    logging.info("Không thể đăng nhập bằng bất kỳ phương pháp nào.")
                                    result["message"] = "Không thể đăng nhập bằng bất kỳ phương pháp nào"
                                    await cleanup_browser(context, browser, chrome_profile_path)
                                    return result
                            except Exception as e:
                                logging.error(f"Lỗi khi truy cập lại trang chính: {str(e)}")
                                result["message"] = "Không thể đăng nhập bằng Google"
                                await cleanup_browser(context, browser, chrome_profile_path)
                                return result
            except Exception as e:
                logging.error(f"Lỗi khi truy cập trang chính, thử trang đăng nhập: {str(e)}")

                # Nếu không thể truy cập trang chính, thử trang đăng nhập
                try:
                    logging.info("Truy cập trang đăng nhập Manus...")
                    await page.goto("https://manus.im/login?type=signIn", timeout=60000)
                    await page.wait_for_load_state('networkidle', timeout=60000)

                    # Kiểm tra trạng thái đăng nhập
                    is_logged = await is_logged_in(page)
                    result["is_logged_in"] = is_logged

                    if is_logged:
                        logging.info("Đã đăng nhập thành công bằng Chrome profile!")
                        result["message"] = "Đã đăng nhập thành công bằng Chrome profile"
                    else:
                        logging.info("Chưa đăng nhập, thử đăng nhập tự động bằng Google...")

                        # Thử đăng nhập bằng Google
                        google_login_attempted = await try_google_login(page)

                        if google_login_attempted:
                            logging.info("Đã thử đăng nhập bằng Google, kiểm tra lại trạng thái...")
                            # Đợi đủ thời gian để quá trình đăng nhập hoàn tất
                            await asyncio.sleep(5)

                            # Kiểm tra lại trạng thái đăng nhập
                            is_logged = await is_logged_in(page)
                            result["is_logged_in"] = is_logged

                            if is_logged:
                                logging.info("Đăng nhập thành công sau khi thử đăng nhập bằng Google!")
                                result["message"] = "Đăng nhập thành công sau khi thử đăng nhập bằng Google"
                            else:
                                # Thử truy cập trang chính
                                try:
                                    logging.info("Vẫn chưa đăng nhập, thử truy cập trang chính...")
                                    await page.goto("https://manus.im/app", timeout=60000)
                                    await page.wait_for_load_state('networkidle', timeout=60000)

                                    # Kiểm tra URL sau khi chuyển hướng
                                    if '/app' in page.url or 'dashboard' in page.url:
                                        logging.info("Đã truy cập thành công vào trang chính!")
                                        is_logged = True
                                        result["is_logged_in"] = True
                                        result["message"] = "Đã truy cập thành công vào trang chính"
                                    else:
                                        logging.info("Vẫn chưa đăng nhập sau khi thử tất cả các phương pháp.")
                                        result["message"] = "Vẫn chưa đăng nhập sau khi thử tất cả các phương pháp"
                                        await cleanup_browser(context, browser, chrome_profile_path)
                                        return result
                                except Exception as e:
                                    logging.error(f"Lỗi khi truy cập trang chính: {str(e)}")
                                    result["message"] = "Vẫn chưa đăng nhập sau khi thử đăng nhập bằng Google"
                                    await cleanup_browser(context, browser, chrome_profile_path)
                                    return result
                        else:
                            # Thử truy cập trang chính
                            try:
                                logging.info("Không thể đăng nhập bằng Google, thử truy cập trang chính...")
                                await page.goto("https://manus.im/app", timeout=60000)
                                await page.wait_for_load_state('networkidle', timeout=60000)

                                # Kiểm tra URL sau khi chuyển hướng
                                if '/app' in page.url or 'dashboard' in page.url:
                                    logging.info("Đã truy cập thành công vào trang chính!")
                                    is_logged = True
                                    result["is_logged_in"] = True
                                    result["message"] = "Đã truy cập thành công vào trang chính"
                                else:
                                    logging.info("Không thể đăng nhập bằng bất kỳ phương pháp nào.")
                                    result["message"] = "Không thể đăng nhập bằng bất kỳ phương pháp nào"
                                    await cleanup_browser(context, browser, chrome_profile_path)
                                    return result
                            except Exception as e:
                                logging.error(f"Lỗi khi truy cập trang chính: {str(e)}")
                                result["message"] = "Không thể đăng nhập bằng Google"
                                await cleanup_browser(context, browser, chrome_profile_path)
                                return result
                except Exception as e:
                    logging.error(f"Lỗi khi tải trang đăng nhập: {str(e)}")
                    result["message"] = f"Lỗi khi tải trang: {str(e)}"
                    await cleanup_browser(context, browser, chrome_profile_path)
                    return result

            # Đã đăng nhập thành công, gửi tin nhắn
            try:
                # Tìm và gửi tin nhắn
                success = await send_message(page, data.chat_content)
                result["message_sent"] = success

                if success:
                    result["status"] = "success"
                    result["message"] = "Gửi tin nhắn thành công"
                else:
                    result["message"] = "Không thể gửi tin nhắn"
            except Exception as e:
                logging.error(f"Lỗi khi gửi tin nhắn: {str(e)}")
                result["message"] = f"Lỗi khi gửi tin nhắn: {str(e)}"

            logging.info(f"Giữ trình duyệt mở trong {data.wait_time} giây để quan sát...")
            await asyncio.sleep(data.wait_time)

            # Đóng browser và context
            await cleanup_browser(context, browser, chrome_profile_path)
            logging.info("Đã đóng trình duyệt.")

            # Kết thúc kiểm tra nếu phản hồi không được đánh dấu là hoàn thành
            if not result["is_logged_in"]:
                result["status"] = "error"
                result["message"] = "Đã thiết lập Chrome profile nhưng chưa đăng nhập được. Vui lòng thử lại thủ công."
            return result

    except Exception as e:
        logging.error(f"Lỗi khi chạy test: {str(e)}")
        # Đóng browser và context nếu có lỗi
        try:
            await cleanup_browser(context, browser, chrome_profile_path)
        except Exception as close_error:
            logging.error(f"Lỗi khi đóng trình duyệt: {str(close_error)}")

        result["message"] = f"Lỗi khi chạy test: {str(e)}"
        return result

@app.post("/setup-chrome-profile")
async def setup_chrome_profile(data: dict):
    """API để thiết lập Chrome profile mới và đăng nhập vào Manus"""
    logging.info(f"Nhận yêu cầu tạo profile với dữ liệu: {data}")

    profile_name = data.get("profile_name", f"manus_profile_{random.randint(1000, 9999)}")
    chrome_profile_path = os.path.join(CUSTOM_PROFILE_DIR, profile_name)

    # Đảm bảo thư mục profile tồn tại
    try:
        os.makedirs(chrome_profile_path, exist_ok=True)
        logging.info(f"Đã tạo thư mục profile: {chrome_profile_path}")
        # Đảm bảo quyền truy cập đúng
        os.system(f"chmod -R 777 {chrome_profile_path}")
        logging.info(f"Đã thiết lập quyền cho thư mục profile")
    except Exception as e:
        logging.error(f"Lỗi khi tạo thư mục profile: {e}")
        return {
            "status": "error",
            "message": f"Không thể tạo thư mục profile: {str(e)}",
            "profile_path": chrome_profile_path
        }

    # Trả về kết quả thành công
    return {
        "status": "success",
        "message": f"Đã tạo thư mục profile Chrome thành công",
        "profile_path": chrome_profile_path,
        "profile_name": profile_name
    }

@app.get("/proxy/list")
async def list_proxies():
    """Lấy danh sách tất cả proxy"""
    proxies = proxy_manager.get_all_proxies()
    return {"proxies": proxies}

@app.get("/proxy/{proxy_id}")
async def get_proxy(proxy_id: str):
    """Lấy thông tin chi tiết của một proxy theo ID"""
    proxy = proxy_manager.get_proxy(proxy_id)
    if not proxy:
        raise HTTPException(status_code=404, detail="Proxy không tồn tại")
    return {"proxy": proxy}

@app.post("/proxy/create")
async def create_proxy(proxy: ProxyCreateModel):
    """Tạo mới một proxy"""
    proxy_id = proxy_manager.add_proxy(
        name=proxy.name,
        username=proxy.username,
        password=proxy.password,
        host=proxy.host,
        port=proxy.port,
        country=proxy.country,
        status=proxy.status,
        is_active=proxy.is_active,
        valid_from=proxy.valid_from,
        valid_to=proxy.valid_to
    )
    return {"status": "success", "proxy_id": proxy_id}

@app.put("/proxy/{proxy_id}")
async def update_proxy(proxy_id: str, proxy: ProxyUpdateModel):
    """Cập nhật thông tin proxy"""
    success = proxy_manager.update_proxy(
        proxy_id=proxy_id,
        name=proxy.name,
        username=proxy.username,
        password=proxy.password,
        host=proxy.host,
        port=proxy.port,
        country=proxy.country,
        status=proxy.status,
        is_active=proxy.is_active,
        valid_from=proxy.valid_from,
        valid_to=proxy.valid_to
    )
    if not success:
        raise HTTPException(status_code=404, detail="Proxy không tồn tại")
    return {"status": "success"}

@app.delete("/proxy/{proxy_id}")
async def delete_proxy(proxy_id: str):
    """Xóa proxy"""
    success = proxy_manager.delete_proxy(proxy_id)
    if not success:
        raise HTTPException(status_code=404, detail="Proxy không tồn tại")
    return {"status": "success"}

@app.websocket("/ws/minus-chat")
async def websocket_minus_chat(websocket: WebSocket):
    await websocket.accept()
    logging.info("WebSocket kết nối thành công")

    # Tạo task cho ping/pong để giữ kết nối
    ping_task = None

    async def send_ping():
        """Send periodic pings to keep the WebSocket connection alive"""
        # Flag to track if the WebSocket is closed
        websocket_closed = False

        try:
            while not websocket_closed:
                await asyncio.sleep(15)  # Send ping every 15 seconds
                try:
                    # Check if the WebSocket is still connected before sending ping
                    if websocket.client_state == starlette.websockets.WebSocketState.CONNECTED:
                        try:
                            await websocket.send_json({"type": "ping", "timestamp": time.time()})
                            logging.debug("Ping sent successfully")
                        except Exception as send_error:
                            if "Cannot call 'send' once a close message has been sent" in str(send_error):
                                logging.info("WebSocket is closing, stopping ping")
                                websocket_closed = True
                                break
                            else:
                                logging.error(f"Error sending ping: {str(send_error)}")
                                websocket_closed = True
                                break
                    else:
                        logging.warning("WebSocket is no longer connected, stopping ping")
                        websocket_closed = True
                        break
                except Exception as e:
                    logging.error(f"Error in ping loop: {str(e)}")
                    websocket_closed = True
                    break
        except asyncio.CancelledError:
            # Task cancelled, no further processing needed
            logging.debug("Ping task cancelled")
            pass

    try:
        # Bắt đầu task ping
        ping_task = asyncio.create_task(send_ping())

        # Gửi trạng thái kết nối thành công
        await websocket.send_json({
            "status": "idle",
            "message": "Đã kết nối WebSocket thành công",
            "progress": 0,
            "completed": False,
            "response": None
        })

        # Xử lý các tin nhắn từ client
        while True:
            try:
                # Nhận dữ liệu từ client
                data = await websocket.receive_json()

                # Xử lý ping/pong
                if data.get("type") == "pong":
                    logging.debug("Nhận pong từ client")
                    continue

                logging.info(f"Nhận thông điệp từ client: {data}")

                # Khởi tạo biến theo dõi trạng thái
                status_updates = {
                    "status": "processing",
                    "message": "Đang kết nối với Manus...",
                    "progress": 0,
                    "completed": False,
                    "response": None
                }

                # Lấy lịch sử cuộc trò chuyện nếu có
                chat_history = data.get("chat_history", [])
                logging.info(f"Nhận được lịch sử cuộc trò chuyện: {len(chat_history)} tin nhắn")

                # Gửi trạng thái ban đầu
                await websocket.send_json(status_updates)

                # Xử lý thông tin profile và proxy
                chrome_profile_path = data.get("chrome_profile")
                if data.get("profile_name") and not chrome_profile_path:
                    profile_base_dir = DEFAULT_PROFILE_DIR if data.get("use_system_profile_dir", False) else data.get("profile_dir", CUSTOM_PROFILE_DIR)
                    chrome_profile_path = os.path.join(profile_base_dir, data.get("profile_name"))

                # Xử lý proxy
                proxy_config = None
                if data.get("proxy_id") or data.get("proxy_country"):
                    proxy_config = get_proxy_config(proxy_id=data.get("proxy_id"), country=data.get("proxy_country"))
                elif data.get("proxy_host") and data.get("proxy_port"):
                    proxy_config = {
                        'host': data.get("proxy_host"),
                        'port': data.get("proxy_port"),
                        'username': data.get("proxy_username"),
                        'password': data.get("proxy_password")
                    }

                # Thiết lập X server
                status_updates["message"] = "Đang chuẩn bị môi trường..."
                status_updates["progress"] = 5
                await websocket.send_json(status_updates)
                await setup_xserver()

                # Kiểm tra profile hợp lệ
                status_updates["message"] = "Đang kiểm tra Chrome profile..."
                status_updates["progress"] = 10
                await websocket.send_json(status_updates)

                if not chrome_profile_path or not check_chrome_profile(chrome_profile_path):
                    try:
                        # Tạo thư mục profile nếu chưa tồn tại
                        os.makedirs(chrome_profile_path, exist_ok=True)
                        status_updates["message"] = "Đã tạo thư mục Chrome profile, nhưng cần thiết lập ban đầu..."
                        status_updates["progress"] = 10
                        await websocket.send_json(status_updates)
                    except Exception as e:
                        status_updates["status"] = "error"
                        status_updates["message"] = f"Không thể tạo Chrome profile: {str(e)}"
                        status_updates["completed"] = True
                        await websocket.send_json(status_updates)
                        continue

                # Khởi động Playwright
                status_updates["message"] = "Đang khởi động trình duyệt..."
                status_updates["progress"] = 15
                await websocket.send_json(status_updates)

                browser = None
                context = None

                async with async_playwright() as p:
                    # Thiết lập tham số trình duyệt
                    browser_args = [
                        '--disable-gpu',
                        '--disable-dev-shm-usage',
                        '--no-sandbox',
                        '--disable-setuid-sandbox',
                    ]

                    # Thiết lập context options
                    context_options = {}
                    if data.get("use_stealth", True):
                        stealth_config = get_stealth_config()
                        context_options.update({
                            "viewport": stealth_config["viewport"],
                            "device_scale_factor": stealth_config["device_scale_factor"],
                            "locale": stealth_config["locale"],
                            "timezone_id": stealth_config["timezone_id"],
                            "user_agent": stealth_config["user_agent"]
                        })

                    # Thiết lập proxy
                    playwright_proxy = None
                    if proxy_config:
                        playwright_proxy = {
                            "server": f"http://{proxy_config.get('host')}:{proxy_config.get('port')}"
                        }
                        if proxy_config.get('username') and proxy_config.get('password'):
                            playwright_proxy["username"] = proxy_config.get('username')
                            playwright_proxy["password"] = proxy_config.get('password')

                    # Slow_mo để làm chậm các thao tác
                    slow_mo = random.randint(50, 150) if data.get("headless", True) else 0

                    # Khởi động trình duyệt với profile
                    status_updates["message"] = "Đang mở trình duyệt với Chrome profile..."
                    status_updates["progress"] = 20
                    await websocket.send_json(status_updates)

                    context = await p.chromium.launch_persistent_context(
                        user_data_dir=chrome_profile_path,
                        headless=data.get("headless", True),
                        args=browser_args,
                        proxy=playwright_proxy,
                        slow_mo=slow_mo,
                        **context_options
                    )
                    browser = context.browser

                    # Thêm JavaScript stealth
                    if data.get("use_stealth", True):
                        await context.add_init_script("""
                        // Ẩn thuộc tính webdriver và các đoạn mã stealth khác
                        Object.defineProperty(navigator, 'webdriver', {
                            get: () => false,
                        });
                        """)

                    # Mở trang mới
                    status_updates["message"] = "Đang truy cập trang Manus..."
                    status_updates["progress"] = 25
                    await websocket.send_json(status_updates)

                    page = await context.new_page()
                    try:
                        # Tăng timeout và thêm xử lý lỗi tốt hơn
                        logging.info("Đang truy cập trang Manus.im...")
                        try:
                            # Thử truy cập với timeout dài hơn
                            await page.goto("https://manus.im/app", timeout=120000)
                        except Exception as nav_error:
                            logging.warning(f"Lỗi khi truy cập trang chính: {str(nav_error)}")
                            logging.info("Thử truy cập trang đăng nhập thay thế...")
                            await page.goto("https://manus.im/login?type=signIn", timeout=120000)

                        # Đợi trang tải với timeout dài hơn và thử nhiều trạng thái khác nhau
                        try:
                            await page.wait_for_load_state('domcontentloaded', timeout=30000)
                            logging.info("Trang đã tải DOM content")
                        except Exception as load_error:
                            logging.warning(f"Lỗi khi đợi DOM content: {str(load_error)}")

                        try:
                            await page.wait_for_load_state('networkidle', timeout=60000)
                            logging.info("Trang đã tải xong (networkidle)")
                        except Exception as net_error:
                            logging.warning(f"Lỗi khi đợi networkidle: {str(net_error)}")
                            # Tiếp tục xử lý ngay cả khi không đạt được networkidle

                        # Kiểm tra trạng thái đăng nhập
                        status_updates["message"] = "Đang kiểm tra trạng thái đăng nhập..."
                        status_updates["progress"] = 30
                        await websocket.send_json(status_updates)

                        current_url = page.url
                        is_logged = False

                        if '/app' in current_url or 'dashboard' in current_url:
                            is_logged = True
                        else:
                            # Thử quy trình đăng nhập
                            status_updates["message"] = "Đang thử đăng nhập..."
                            status_updates["progress"] = 35
                            await websocket.send_json(status_updates)

                            # Quy trình đăng nhập tương tự như trong minus-chat-profile
                            await page.wait_for_load_state('networkidle', timeout=60000)
                            is_logged = await is_logged_in(page)

                            if not is_logged:
                                # Thử đăng nhập bằng Google
                                status_updates["message"] = "Đang thử đăng nhập bằng Google..."
                                status_updates["progress"] = 40
                                await websocket.send_json(status_updates)

                                google_login_attempted = await try_google_login(page)
                                if google_login_attempted:
                                    await asyncio.sleep(5)
                                    is_logged = await is_logged_in(page)

                                    if not is_logged:
                                        # Thử truy cập lại trang chính
                                        status_updates["message"] = "Đang thử truy cập lại trang chính..."
                                        status_updates["progress"] = 45
                                        await websocket.send_json(status_updates)

                                        await page.goto("https://manus.im/app", timeout=60000)
                                        await page.wait_for_load_state('networkidle', timeout=60000)
                                        if '/app' in page.url or 'dashboard' in page.url:
                                            is_logged = True

                        if not is_logged:
                            status_updates["status"] = "error"
                            status_updates["message"] = "Không thể đăng nhập vào Manus"
                            status_updates["completed"] = True
                            await websocket.send_json(status_updates)
                            await cleanup_browser(context, browser, chrome_profile_path)
                            continue

                        # Gửi tin nhắn đến Manus
                        status_updates["message"] = "Đã đăng nhập thành công, đang gửi tin nhắn đến Manus..."
                        status_updates["progress"] = 50
                        await websocket.send_json(status_updates)

                        # Chuẩn bị nội dung tin nhắn với lịch sử cuộc trò chuyện
                        chat_content = data.get("chat_content", "")

                        # Nếu có lịch sử cuộc trò chuyện, hiển thị lên trang trước khi gửi tin nhắn mới
                        if chat_history and len(chat_history) > 0:
                            status_updates["message"] = "Đang tải lịch sử cuộc trò chuyện..."
                            status_updates["progress"] = 45
                            await websocket.send_json(status_updates)

                            logging.info(f"Chuẩn bị hiển thị {len(chat_history)} tin nhắn từ lịch sử")

                            # Thực hiện script để hiển thị lịch sử cuộc trò chuyện
                            try:
                                # Tạo script để thêm lịch sử cuộc trò chuyện vào trang
                                chat_history_script = """
                                (chatHistory) => {
                                    // Tạo một div container để chứa lịch sử cuộc trò chuyện
                                    const historyContainer = document.createElement('div');
                                    historyContainer.id = 'chat-history-container';
                                    historyContainer.style.display = 'none';

                                    // Thêm lịch sử cuộc trò chuyện vào container
                                    chatHistory.forEach(msg => {
                                        console.log('Adding message to history:', msg);
                                        const msgDiv = document.createElement('div');
                                        msgDiv.className = msg.sender === 'user' ? 'user-message' : 'assistant-message';
                                        msgDiv.textContent = msg.content;
                                        historyContainer.appendChild(msgDiv);
                                    });

                                    // Thêm container vào trang
                                    document.body.appendChild(historyContainer);

                                    // Lưu lịch sử cuộc trò chuyện vào localStorage để có thể truy cập sau này
                                    localStorage.setItem('manus_chat_history', JSON.stringify(chatHistory));

                                    return true;
                                }
                                """

                                # Thực thi script để thêm lịch sử cuộc trò chuyện
                                result = await page.evaluate(chat_history_script, chat_history)
                                logging.info(f"Kết quả thêm lịch sử cuộc trò chuyện: {result}")
                            except Exception as history_error:
                                logging.error(f"Lỗi khi thêm lịch sử cuộc trò chuyện: {str(history_error)}")

                        # Gửi tin nhắn mới với lịch sử cuộc trò chuyện
                        success = await send_message(page, chat_content, chat_history)

                        if not success:
                            status_updates["status"] = "error"
                            status_updates["message"] = "Không thể gửi tin nhắn đến Manus"
                            status_updates["completed"] = True
                            await websocket.send_json(status_updates)
                            await cleanup_browser(context, browser, chrome_profile_path)
                            continue

                        # Bắt đầu theo dõi phản hồi từ Manus
                        status_updates["message"] = "Đã gửi tin nhắn thành công, đang đợi Manus phản hồi..."
                        status_updates["progress"] = 60
                        await websocket.send_json(status_updates)

                        # Đợi và theo dõi phản hồi từ Manus
                        # Xác định selector cho vùng chat
                        response_text = ""
                        last_response = ""
                        max_wait_time = 25 * 60  # 25 phút
                        start_time = asyncio.get_event_loop().time()

                        # Biến để theo dõi trạng thái browser
                        browser_closed = False

                        # Theo dõi phản hồi từ Manus theo thời gian thực
                        # Biến để theo dõi lịch sử đã gửi để tránh duplicate
                        last_sent_history_length = 0
                        last_sent_content_hash = None

                        try:
                            while True:
                                # Kiểm tra xem browser có còn mở không
                                if browser_closed:
                                    logging.warning("Browser đã đóng, dừng theo dõi phản hồi")
                                    status_updates["status"] = "warning"
                                    status_updates["message"] = "Browser đã đóng, không thể tiếp tục theo dõi phản hồi"
                                    status_updates["completed"] = True
                                    await websocket.send_json(status_updates)
                                    break

                                # Kiểm tra thời gian đã trôi qua
                                elapsed = asyncio.get_event_loop().time() - start_time
                                if elapsed > max_wait_time:
                                    status_updates["status"] = "warning"
                                    status_updates["message"] = "Đã vượt quá thời gian đợi tối đa"
                                    status_updates["completed"] = True
                                    await websocket.send_json(status_updates)
                                    break

                                # Tìm phần tử chứa tin nhắn mới nhất từ Manus
                                # Cần điều chỉnh selector dựa trên cấu trúc trang Manus thực tế
                                try:
                                    # Kiểm tra xem browser và page còn hoạt động không
                                    try:
                                        # Thử truy cập một thuộc tính của page để kiểm tra nó còn hoạt động không
                                        current_url = page.url
                                    except Exception as page_error:
                                        logging.warning(f"Page không còn hoạt động: {str(page_error)}")
                                        browser_closed = True
                                        continue

                                    # Kiểm tra xem có thông báo hoàn thành không
                                    try:
                                        # Tìm thông báo hoàn thành "Manus has completed the current task"
                                        # Thử nhiều selector khác nhau để tìm thông báo hoàn thành
                                        completion_selectors = [
                                            "div[style*='background: rgba(37, 186, 59, 0.12)']",
                                            "div[class*='text-[var(--function-success)]']",
                                            "div[class*='rounded-full'][class*='text-[13px]']",
                                            "div.px-3.py-\\[5px\\].rounded-full",
                                            "div:has(svg[color='var(--function-success)'])"
                                        ]

                                        completion_found = False
                                        # Phương pháp 1: Tìm theo selector cụ thể
                                        for selector in completion_selectors:
                                            try:
                                                completion_notification = await page.query_selector(selector)
                                                if completion_notification:
                                                    completion_text = await completion_notification.inner_text()
                                                    logging.info(f"Tìm thấy thông báo có thể là hoàn thành ({selector}): {completion_text}")

                                                    if "Manus has completed the current task" in completion_text:
                                                        completion_found = True
                                                        break
                                            except Exception as selector_error:
                                                logging.warning(f"Lỗi khi tìm kiếm với selector {selector}: {str(selector_error)}")
                                                continue

                                        # Phương pháp 2: Tìm kiếm văn bản trên toàn bộ trang
                                        if not completion_found:
                                            try:
                                                # Lấy tất cả văn bản trên trang
                                                page_content = await page.content()
                                                if "Manus has completed the current task" in page_content:
                                                    logging.info("Phát hiện chuỗi 'Manus has completed the current task' trong HTML của trang")
                                                    completion_found = True

                                                # Tìm tất cả các phần tử có văn bản
                                                all_text_elements = await page.query_selector_all("div, p, span, h1, h2, h3, h4, h5, h6")
                                                for element in all_text_elements:
                                                    try:
                                                        element_text = await element.inner_text()
                                                        if "Manus has completed the current task" in element_text:
                                                            logging.info(f"Phát hiện thông báo hoàn thành trong phần tử: {element_text}")
                                                            completion_found = True
                                                            break
                                                    except Exception:
                                                        continue
                                            except Exception as text_search_error:
                                                logging.warning(f"Lỗi khi tìm kiếm văn bản trên trang: {str(text_search_error)}")

                                        if completion_found:
                                                logging.info("Phát hiện thông báo hoàn thành: 'Manus has completed the current task'")

                                                # Đánh dấu là đã hoàn thành
                                                status_updates["message"] = "Manus đã hoàn thành nhiệm vụ!"
                                                status_updates["progress"] = 100
                                                status_updates["completed"] = True
                                                status_updates["response"] = "Manus has completed the current task"

                                                # Gửi thông báo hoàn thành
                                                if websocket.client_state == starlette.websockets.WebSocketState.CONNECTED:
                                                    await websocket.send_json(status_updates)

                                                # Đánh dấu browser cần đóng
                                                browser_closed = True

                                                # Đóng browser ngay lập tức
                                                try:
                                                    logging.info("Đóng browser sau khi phát hiện thông báo hoàn thành...")
                                                    await cleanup_browser(context, browser, chrome_profile_path)
                                                    logging.info("Đã đóng browser thành công sau khi hoàn thành")
                                                    context = None
                                                    browser = None
                                                    break
                                                except Exception as close_error:
                                                    logging.error(f"Lỗi khi đóng browser sau khi hoàn thành: {str(close_error)}")
                                                    break
                                    except Exception as completion_error:
                                        logging.error(f"Lỗi khi kiểm tra thông báo hoàn thành: {str(completion_error)}")

                                    # Sử dụng nhiều selector khác nhau để tìm tin nhắn từ Manus
                                    try:
                                        # Selector 1: Thử tìm theo cấu trúc thông thường của chat UI
                                        messages = await page.query_selector_all(".message-container .ai-message, .chat-message.ai, .message.ai, [data-role='assistant'], .assistant-message")

                                        # Selector 2: Nếu không tìm thấy, thử tìm theo các thuộc tính phổ biến khác
                                        if not messages or len(messages) == 0:
                                            messages = await page.query_selector_all("div[class*='message'][class*='ai'], div[class*='assistant'], div[class*='bot-message']")

                                        # Selector 3: Nếu vẫn không tìm thấy, thử tìm theo nội dung
                                        if not messages or len(messages) == 0:
                                            # Tìm tất cả các phần tử div có thể chứa văn bản
                                            all_divs = await page.query_selector_all("div:not(:empty)")
                                            # Lọc các div có nội dung dài hơn 50 ký tự (có khả năng là phản hồi)
                                            messages = []
                                            for div in all_divs:
                                                try:
                                                    text = await div.inner_text()
                                                    if len(text) > 50:
                                                        messages.append(div)
                                                except Exception as div_error:
                                                    # Bỏ qua lỗi với div cụ thể
                                                    continue
                                    except Exception as selector_error:
                                        if "Target page, context or browser has been closed" in str(selector_error):
                                            logging.warning("Browser đã đóng trong quá trình tìm kiếm phần tử")
                                            browser_closed = True
                                            continue
                                        else:
                                            logging.error(f"Lỗi khi tìm kiếm phần tử: {str(selector_error)}")
                                            continue

                                    if messages and len(messages) > 0:
                                        try:
                                            # Lấy tin nhắn mới nhất (thường là phần tử cuối cùng)
                                            latest_message = messages[-1]
                                            current_text = await latest_message.inner_text()

                                            # Kiểm tra nội dung có thay đổi thực sự không
                                            content_hash = hash(current_text.strip())
                                            if current_text != last_response and content_hash != last_sent_content_hash:
                                                last_response = current_text
                                                response_text = current_text
                                                last_sent_content_hash = content_hash

                                                # Ghi log để debug
                                                logging.info(f"Phát hiện phản hồi mới từ Manus: {response_text[:100]}...")

                                                # Cập nhật tiến trình
                                                progress = min(95, 60 + int((elapsed / 20) * 35))  # Giả sử 20 phút là thời gian hoàn thành
                                                status_updates["message"] = "Đang nhận phản hồi từ Manus..."
                                                status_updates["progress"] = progress
                                                status_updates["response"] = response_text

                                                # Check if WebSocket is still connected before sending
                                                try:
                                                    if websocket.client_state == starlette.websockets.WebSocketState.CONNECTED:
                                                        await websocket.send_json(status_updates)
                                                        logging.info(f"Đã gửi cập nhật phản hồi qua WebSocket (hash: {content_hash})")
                                                    else:
                                                        logging.warning("WebSocket is no longer connected, cannot send updates")
                                                        browser_closed = True
                                                        break
                                                except Exception as ws_error:
                                                    if "Cannot call 'send' once a close message has been sent" in str(ws_error):
                                                        logging.info("WebSocket is closing, stopping updates")
                                                        browser_closed = True
                                                        break
                                                    else:
                                                        logging.error(f"Error sending WebSocket update: {str(ws_error)}")
                                                        browser_closed = True
                                                        break
                                            else:
                                                # Nội dung không thay đổi, bỏ qua để tránh duplicate
                                                if content_hash == last_sent_content_hash:
                                                    logging.debug("Bỏ qua phản hồi trùng lặp")

                                            # Kiểm tra nếu phản hồi đã hoàn thành (cho cả trường hợp mới và cũ)
                                            # Sử dụng nhiều từ khóa để phát hiện kết thúc
                                            completion_keywords = ["kết luận", "hoàn thành", "tóm lại", "tổng kết",
                                                                  "conclusion", "complete", "finished", "done",
                                                                  "summary", "in summary", "to summarize", "waiting for user",
                                                                  "await user instructions", "manus đã kết thúc chat",
                                                                  "manus has completed the current task", "completed the current task"]

                                            is_completed = any(keyword in current_text.lower() for keyword in completion_keywords)
                                            # Kiểm tra thêm độ dài của phản hồi hoặc nếu có từ khóa kết thúc rõ ràng
                                            if (len(current_text) > 500 and (is_completed or elapsed > 60)) or \
                                               "await user instructions" in current_text.lower() or \
                                               "manus đã kết thúc chat" in current_text.lower() or \
                                               "manus has completed the current task" in current_text.lower() or \
                                               "completed the current task" in current_text.lower():
                                                status_updates["message"] = "Manus đã hoàn thành phản hồi!"
                                                status_updates["progress"] = 100
                                                status_updates["completed"] = True

                                                # Check if WebSocket is still connected before sending
                                                try:
                                                    if websocket.client_state == starlette.websockets.WebSocketState.CONNECTED:
                                                        await websocket.send_json(status_updates)
                                                    else:
                                                        logging.warning("WebSocket is no longer connected, cannot send completion message")
                                                        browser_closed = True
                                                except Exception as ws_error:
                                                    if "Cannot call 'send' once a close message has been sent" in str(ws_error):
                                                        logging.info("WebSocket is closing, cannot send completion message")
                                                    else:
                                                        logging.error(f"Error sending completion message: {str(ws_error)}")
                                                    browser_closed = True

                                                # Đợi thêm một khoảng thời gian trước khi đóng browser
                                                # để đảm bảo đã đọc hết phản hồi
                                                logging.info("Đợi thêm 5 giây trước khi đóng browser...")
                                                await asyncio.sleep(5)

                                                # Đánh dấu browser cần đóng
                                                browser_closed = True

                                                # Đóng browser ngay lập tức
                                                try:
                                                    logging.info("Đóng browser sau khi phát hiện hoàn thành...")
                                                    await cleanup_browser(context, browser, chrome_profile_path)
                                                    logging.info("Đã đóng browser thành công sau khi hoàn thành")
                                                    context = None
                                                    browser = None
                                                except Exception as close_error:
                                                    logging.error(f"Lỗi khi đóng browser sau khi hoàn thành: {str(close_error)}")

                                                break
                                        except Exception as text_error:
                                            if "Target page, context or browser has been closed" in str(text_error):
                                                logging.warning("Browser đã đóng trong quá trình đọc nội dung")
                                                browser_closed = True
                                                continue
                                            else:
                                                logging.error(f"Lỗi khi đọc nội dung: {str(text_error)}")
                                                continue
                                except Exception as e:
                                    if "Target page, context or browser has been closed" in str(e):
                                        logging.warning("Browser đã đóng, không thể đọc phản hồi từ Manus")
                                        browser_closed = True
                                    else:
                                        logging.error(f"Lỗi khi đọc phản hồi từ Manus: {str(e)}")

                                # Đợi một khoảng thời gian ngắn trước khi kiểm tra lại
                                await asyncio.sleep(2)

                        except Exception as tracking_error:
                            logging.error(f"Lỗi khi theo dõi phản hồi từ Manus: {str(tracking_error)}")
                            status_updates["status"] = "error"
                            status_updates["message"] = f"Lỗi khi theo dõi phản hồi: {str(tracking_error)}"
                            status_updates["completed"] = True
                            await websocket.send_json(status_updates)

                        # Đánh dấu browser đang được đóng để tránh các thao tác tiếp theo
                        browser_closed = True

                        # Dọn dẹp và đóng trình duyệt sau khi đã hoàn thành việc đọc phản hồi
                        logging.info("Đã hoàn thành việc đọc phản hồi, tiến hành đóng trình duyệt...")
                        try:
                            await cleanup_browser(context, browser, chrome_profile_path)
                            logging.info("Đã đóng trình duyệt thành công")
                            # Đặt các biến thành None để tránh sử dụng lại
                            context = None
                            browser = None
                        except Exception as cleanup_error:
                            logging.error(f"Lỗi khi đóng trình duyệt: {str(cleanup_error)}")

                        # Kết thúc kiểm tra nếu phản hồi không được đánh dấu là hoàn thành
                        if not status_updates["completed"]:
                            status_updates["status"] = "success"
                            status_updates["message"] = "Đã hoàn thành kiểm tra"
                            status_updates["progress"] = 100
                            status_updates["completed"] = True

                            # Check if WebSocket is still connected before sending
                            try:
                                if websocket.client_state == starlette.websockets.WebSocketState.CONNECTED:
                                    await websocket.send_json(status_updates)
                                else:
                                    logging.warning("WebSocket is no longer connected, cannot send final status")
                            except Exception as ws_error:
                                if "Cannot call 'send' once a close message has been sent" in str(ws_error):
                                    logging.info("WebSocket is closing, cannot send final status")
                                else:
                                    logging.error(f"Error sending final status: {str(ws_error)}")

                    except Exception as page_error:
                        logging.error(f"Lỗi khi truy cập trang: {str(page_error)}")
                        status_updates["status"] = "error"
                        status_updates["message"] = f"Lỗi khi truy cập trang: {str(page_error)}"
                        status_updates["completed"] = True

                        # Check if WebSocket is still connected before sending
                        try:
                            if websocket.client_state == starlette.websockets.WebSocketState.CONNECTED:
                                await websocket.send_json(status_updates)
                            else:
                                logging.warning("WebSocket is no longer connected, cannot send error status")
                        except Exception as ws_error:
                            if "Cannot call 'send' once a close message has been sent" in str(ws_error):
                                logging.info("WebSocket is closing, cannot send error status")
                            else:
                                logging.error(f"Error sending error status: {str(ws_error)}")

                    finally:
                        # Dọn dẹp tài nguyên
                        try:
                            if context:
                                try:
                                    await context.close()
                                    logging.info("Đã đóng context thành công")
                                except Exception as context_error:
                                    logging.error(f"Lỗi khi đóng context: {str(context_error)}")

                            if browser:
                                try:
                                    await browser.close()
                                    logging.info("Đã đóng browser thành công")
                                except Exception as browser_error:
                                    logging.error(f"Lỗi khi đóng browser: {str(browser_error)}")
                        except Exception as e:
                            logging.error(f"Lỗi không xác định khi dọn dẹp tài nguyên: {str(e)}")

            except starlette.websockets.WebSocketDisconnect as disconnect_error:
                code = getattr(disconnect_error, 'code', 'unknown')
                reason = getattr(disconnect_error, 'reason', '')

                # Chỉ log lỗi nếu không phải đóng bình thường (code 1000, 1001)
                if code not in [1000, 1001]:
                    logging.error(f"WebSocket bị ngắt kết nối: code={code}, reason={reason}")
                else:
                    logging.info(f"WebSocket đóng bình thường: code={code}, reason={reason}")
                break

            except Exception as e:
                logging.error(f"Lỗi xử lý tin nhắn WebSocket: {str(e)}")
                try:
                    # Check if WebSocket is still connected before sending
                    if websocket.client_state == starlette.websockets.WebSocketState.CONNECTED:
                        await websocket.send_json({
                            "status": "error",
                            "message": f"Lỗi xử lý: {str(e)}",
                            "progress": 0,
                            "completed": True,
                            "response": None
                        })
                    else:
                        logging.warning("WebSocket is no longer connected, cannot send error message")
                except Exception as send_error:
                    if "Cannot call 'send' once a close message has been sent" in str(send_error):
                        logging.info("WebSocket is closing, cannot send error message")
                    else:
                        logging.error(f"Error sending error message: {str(send_error)}")
                    break

    except Exception as e:
        logging.error(f"Lỗi trong WebSocket: {str(e)}")
        try:
            # Check if WebSocket is still connected before sending
            if websocket.client_state == starlette.websockets.WebSocketState.CONNECTED:
                await websocket.send_json({
                    "status": "error",
                    "message": f"Lỗi xử lý: {str(e)}",
                    "completed": True
                })
            else:
                logging.warning("WebSocket is no longer connected, cannot send outer error message")
        except Exception as send_error:
            if "Cannot call 'send' once a close message has been sent" in str(send_error):
                logging.info("WebSocket is closing, cannot send outer error message")
            else:
                logging.error(f"Error sending outer error message: {str(send_error)}")

    finally:
        # Hủy task ping nếu đang chạy
        if ping_task and not ping_task.done():
            ping_task.cancel()
            try:
                await ping_task
            except asyncio.CancelledError:
                pass

        logging.info("WebSocket đã đóng và dọn dẹp tài nguyên")

@app.post("/open-browser-login")
async def open_browser_login(data: dict):
    """API để mở trình duyệt để người dùng đăng nhập"""
    logging.info(f"Nhận yêu cầu mở trình duyệt với dữ liệu: {data}")

    profile_name = data.get("profile_name", f"manus_profile_{random.randint(1000, 9999)}")

    # Đường dẫn đến thư mục Chrome profile
    chrome_profile_path = os.path.join(CUSTOM_PROFILE_DIR, profile_name)

    # Đảm bảo thư mục profile tồn tại
    try:
        os.makedirs(chrome_profile_path, exist_ok=True)
        logging.info(f"Đã tạo thư mục profile: {chrome_profile_path}")
        # Đảm bảo quyền truy cập đúng
        os.system(f"chmod -R 777 {chrome_profile_path}")
        logging.info(f"Đã thiết lập quyền cho thư mục profile")

        return {
            "status": "success",
            "message": "Đã chuẩn bị sẵn sàng Chrome profile và VNC",
            "profile_path": chrome_profile_path,
            "vnc_access": "Truy cập VNC qua http://localhost:6080/vnc.html hoặc VNC client kết nối tới localhost:5900 với mật khẩu 'playwright'",
            "next_steps": "Vui lòng đăng nhập vào tài khoản Google sử dụng trình duyệt từ xa"
        }
    except Exception as e:
        logging.error(f"Lỗi khi thiết lập chrome profile: {str(e)}")
        return {
            "status": "error",
            "message": f"Không thể thiết lập Chrome profile: {str(e)}"
        }

@app.websocket("/ws/profile-setup")
async def websocket_profile_setup(websocket: WebSocket):
    """WebSocket endpoint để thiết lập Chrome profile với cập nhật trạng thái thời gian thực"""
    await websocket.accept()
    logging.info("WebSocket kết nối thành công cho thiết lập profile")

    # Tạo task cho ping/pong để giữ kết nối
    ping_task = None
    setup_task = None

    async def send_ping():
        """Gửi ping định kỳ để giữ kết nối WebSocket"""
        try:
            while True:
                await asyncio.sleep(15)  # Gửi ping mỗi 15 giây
                try:
                    if websocket.client_state == starlette.websockets.WebSocketState.CONNECTED:
                        await websocket.send_json({"type": "ping", "timestamp": time.time()})
                        logging.debug("Đã gửi ping")
                    else:
                        logging.warning("WebSocket không còn kết nối, dừng gửi ping")
                        break
                except Exception as e:
                    logging.error(f"Lỗi khi gửi ping: {str(e)}")
                    break
        except asyncio.CancelledError:
            logging.debug("Task ping đã bị hủy")
            pass

    try:
        # Bắt đầu task ping
        ping_task = asyncio.create_task(send_ping())

        # Gửi trạng thái kết nối thành công
        await websocket.send_json({
            "status": "idle",
            "message": "Đã kết nối WebSocket thành công cho thiết lập profile",
            "progress": 0,
            "completed": False,
            "response": None
        })

        # Xử lý các tin nhắn từ client
        while True:
            try:
                # Nhận dữ liệu từ client
                data = await websocket.receive_json()

                # Xử lý ping/pong
                if data.get("type") == "pong":
                    logging.debug("Nhận pong từ client")
                    continue

                # Xử lý yêu cầu hủy thiết lập
                if data.get("action") == "cancel" and setup_task and not setup_task.done():
                    logging.info("Nhận yêu cầu hủy thiết lập profile")
                    setup_task.cancel()
                    await websocket.send_json({
                        "status": "warning",
                        "message": "Thiết lập profile đã bị hủy theo yêu cầu",
                        "progress": 0,
                        "completed": True,
                        "response": None
                    })
                    continue

                # Xử lý yêu cầu hoàn thành thiết lập
                if data.get("action") == "complete" and setup_task and not setup_task.done():
                    logging.info("Nhận yêu cầu hoàn thành thiết lập profile")
                    setup_task.cancel()

                    profile_name = data.get("profile_name")
                    if profile_name:
                        chrome_profile_path = os.path.join(CUSTOM_PROFILE_DIR, profile_name)
                        await websocket.send_json({
                            "status": "success",
                            "message": f"Thiết lập profile {profile_name} đã hoàn thành thành công",
                            "progress": 100,
                            "completed": True,
                            "response": None,
                            "profile_path": chrome_profile_path,
                            "profile_name": profile_name
                        })
                    continue

                logging.info(f"Nhận thông điệp thiết lập profile từ client: {data}")

                # Khởi tạo biến theo dõi trạng thái
                status_updates = {
                    "status": "processing",
                    "message": "Đang chuẩn bị thiết lập Chrome profile...",
                    "progress": 5,
                    "completed": False,
                    "response": None
                }

                # Thêm thông tin VNC vào trạng thái
                status_updates["vnc_details"] = {
                    "url": "http://localhost:6080/vnc.html?autoconnect=true&resize=scale&reconnect=true",
                    "password": "playwright",
                    "host": "localhost",
                    "port": "5900"
                }

                # Gửi trạng thái ban đầu
                await websocket.send_json(status_updates)

                # Lấy thông tin profile
                profile_name = data.get("profile_name", f"profile_{random.randint(1000, 9999)}")
                headless = data.get("headless", False)
                url = data.get("url", "https://accounts.google.com")
                use_stealth = data.get("use_stealth", True)
                force_reset = data.get("force_reset", False)

                # Tạo task thiết lập profile
                setup_task = asyncio.create_task(
                    setup_chrome_profile_with_websocket(
                        websocket=websocket,
                        profile_name=profile_name,
                        headless=headless,
                        url=url,
                        use_stealth=use_stealth,
                        force_reset=force_reset
                    )
                )

                # Đợi task hoàn thành
                result = await setup_task

                # Gửi kết quả cuối cùng
                if result["status"] == "success":
                    await websocket.send_json({
                        "status": "success",
                        "message": "Thiết lập Chrome profile thành công",
                        "progress": 100,
                        "completed": True,
                        "response": None,
                        "profile_path": result["profile_path"],
                        "profile_name": result["profile_name"]
                    })
                else:
                    await websocket.send_json({
                        "status": "error",
                        "message": result["message"],
                        "progress": 0,
                        "completed": True,
                        "response": None
                    })

            except starlette.websockets.WebSocketDisconnect as disconnect_error:
                code = getattr(disconnect_error, 'code', 'unknown')
                reason = getattr(disconnect_error, 'reason', '')

                # Chỉ log lỗi nếu không phải đóng bình thường (code 1000, 1001)
                if code not in [1000, 1001]:
                    logging.error(f"WebSocket bị ngắt kết nối: code={code}, reason={reason}")
                else:
                    logging.info(f"WebSocket đóng bình thường: code={code}, reason={reason}")

                # Hủy task thiết lập nếu đang chạy
                if setup_task and not setup_task.done():
                    setup_task.cancel()
                    try:
                        await setup_task
                    except asyncio.CancelledError:
                        logging.info("Task thiết lập profile đã bị hủy do ngắt kết nối WebSocket")

                break

            except Exception as e:
                logging.error(f"Lỗi xử lý tin nhắn WebSocket: {str(e)}")
                try:
                    # Check if WebSocket is still connected before sending
                    if websocket.client_state == starlette.websockets.WebSocketState.CONNECTED:
                        await websocket.send_json({
                            "status": "error",
                            "message": f"Lỗi xử lý: {str(e)}",
                            "progress": 0,
                            "completed": True,
                            "response": None
                        })
                    else:
                        logging.warning("WebSocket is no longer connected, cannot send error message")
                except Exception as send_error:
                    if "Cannot call 'send' once a close message has been sent" in str(send_error):
                        logging.info("WebSocket is closing, cannot send error message")
                    else:
                        logging.error(f"Error sending error message: {str(send_error)}")
                    break

    except Exception as e:
        logging.error(f"Lỗi trong WebSocket: {str(e)}")
        try:
            # Check if WebSocket is still connected before sending
            if websocket.client_state == starlette.websockets.WebSocketState.CONNECTED:
                await websocket.send_json({
                    "status": "error",
                    "message": f"Lỗi xử lý: {str(e)}",
                    "completed": True
                })
            else:
                logging.warning("WebSocket is no longer connected, cannot send outer error message")
        except Exception as send_error:
            if "Cannot call 'send' once a close message has been sent" in str(send_error):
                logging.info("WebSocket is closing, cannot send outer error message")
            else:
                logging.error(f"Error sending outer error message: {str(send_error)}")

    finally:
        # Hủy task ping nếu đang chạy
        if ping_task and not ping_task.done():
            ping_task.cancel()
            try:
                await ping_task
            except asyncio.CancelledError:
                pass

        # Hủy task thiết lập nếu đang chạy
        if setup_task and not setup_task.done():
            setup_task.cancel()
            try:
                await setup_task
            except asyncio.CancelledError:
                pass

        logging.info("WebSocket đã đóng và dọn dẹp tài nguyên thiết lập profile")

@app.post("/complete-setup")
async def complete_setup(data: dict):
    """API để xác nhận hoàn thành thiết lập profile"""
    logging.info(f"Nhận yêu cầu hoàn thành thiết lập: {data}")

    profile_name = data.get("profile_name")
    if not profile_name:
        return {
            "status": "error",
            "message": "Thiếu thông tin profile_name"
        }

    # Đường dẫn đến thư mục Chrome profile
    chrome_profile_path = os.path.join(CUSTOM_PROFILE_DIR, profile_name)

    # Kiểm tra thư mục profile
    if not os.path.exists(chrome_profile_path):
        return {
            "status": "error",
            "message": f"Không tìm thấy profile: {chrome_profile_path}"
        }

    # Cập nhật thông tin hoàn thành (có thể lưu vào file hoặc database)
    status_file = os.path.join(chrome_profile_path, "setup_status.json")
    status_data = {
        "status": data.get("status", "completed"),
        "completed_at": datetime.datetime.now().isoformat(),
        "message": "Đã hoàn thành thiết lập profile"
    }

    try:
        with open(status_file, "w") as f:
            json.dump(status_data, f)

        # Phản hồi thành công
        return {
            "status": "success",
            "message": "Đã hoàn thành thiết lập profile",
            "profile_path": chrome_profile_path,
            "setup_details": status_data
        }
    except Exception as e:
        logging.error(f"Lỗi khi lưu trạng thái hoàn thành: {str(e)}")
        return {
            "status": "error",
            "message": f"Lỗi khi lưu trạng thái: {str(e)}"
        }

def check_chrome_profile(profile_path: str) -> bool:
    """
    Kiểm tra xem Chrome profile có hợp lệ không

    Args:
        profile_path: Đường dẫn đến thư mục Chrome profile

    Returns:
        bool: True nếu profile hợp lệ, False nếu không
    """
    if not os.path.exists(profile_path):
        logging.info(f"Thư mục profile không tồn tại: {profile_path}")
        return False

    # Kiểm tra các thư mục con
    profile_dirs = []

    # Kiểm tra thư mục Default
    default_dir = os.path.join(profile_path, "Default")
    if os.path.exists(default_dir) and os.path.isdir(default_dir):
        profile_dirs.append(default_dir)

    # Kiểm tra các thư mục Profile_*
    for item in os.listdir(profile_path):
        if item.startswith("Profile_") and os.path.isdir(os.path.join(profile_path, item)):
            profile_dirs.append(os.path.join(profile_path, item))

    if not profile_dirs:
        logging.info(f"Không tìm thấy thư mục profile con trong: {profile_path}")
        return False

    # Kiểm tra các file cần thiết trong ít nhất một thư mục profile
    for profile_dir in profile_dirs:
        # Kiểm tra file Preferences
        preferences_path = os.path.join(profile_dir, "Preferences")
        if os.path.exists(preferences_path):
            logging.info(f"Tìm thấy file Preferences trong: {preferences_path}")
            return True

        # Kiểm tra file Cookies
        cookies_path = os.path.join(profile_dir, "Cookies")
        if os.path.exists(cookies_path):
            logging.info(f"Tìm thấy file Cookies trong: {cookies_path}")
            return True

        # Kiểm tra file Login Data
        login_data_path = os.path.join(profile_dir, "Login Data")
        if os.path.exists(login_data_path):
            logging.info(f"Tìm thấy file Login Data trong: {login_data_path}")
            return True

    logging.info(f"Không tìm thấy các file cần thiết trong profile: {profile_path}")
    return False

async def setup_xserver():
    """Thiết lập X server nếu cần"""
    # Kiểm tra X server đã chạy chưa
    x_server_running = os.system("ps aux | grep Xvfb | grep -v grep > /dev/null") == 0
    if not x_server_running:
        # Khởi động X server
        os.system("Xvfb :1 -screen 0 1280x720x16 &")
        # Đợi X server khởi động
        await asyncio.sleep(2)
        logging.info("Đã khởi động X server")
    else:
        logging.info("X server đã chạy")

@app.post("/restart-vnc-service")
async def restart_vnc_service():
    """Khởi động lại dịch vụ VNC và X server"""
    try:
        # Tắt các dịch vụ hiện tại
        os.system("pkill x11vnc")
        os.system("pkill Xvfb")
        os.system("pkill fluxbox")
        os.system("pkill novnc_proxy")

        # Đợi các quá trình đóng hẳn
        time.sleep(2)

        # Khởi động lại các dịch vụ
        os.system("Xvfb :1 -screen 0 1280x720x16 &")
        time.sleep(2)
        os.system("x11vnc -display :1 -forever -shared -rfbport 5900 -passwd playwright &")
        os.system("fluxbox -display :1 &")

        # Khởi động lại noVNC
        os.system("cd /usr/share/novnc && ./utils/novnc_proxy --vnc localhost:5900 --listen 6080 &")

        return {"status": "success", "message": "Đã khởi động lại dịch vụ VNC thành công"}
    except Exception as e:
        return {"status": "error", "message": f"Lỗi khi khởi động lại VNC: {str(e)}"}
