from playwright.async_api import async_playwright
import asyncio
import os
import json
import time
import random
from pathlib import Path

MINUS_URL = "https://manus.im/login?type=signIn"  # URL đăng nhập Manus
COOKIES_DIR = "/app/cookies"  # Th<PERSON> mục lưu cookies

def get_proxy_config(proxy_host=None, proxy_port=None, proxy_username=None, proxy_password=None):
    if proxy_host and proxy_port:
        proxy = {
            "server": f"http://{proxy_host}:{proxy_port}"
        }
        if proxy_username and proxy_password:
            proxy["username"] = proxy_username
            proxy["password"] = proxy_password
        return proxy
    return None

def get_cookies_path(username):
    """
    Tạo đường dẫn đến file cookies dựa trên tên người dùng.
    """
    # Tạo thư mục cookies nếu chưa tồn tại
    Path(COOKIES_DIR).mkdir(parents=True, exist_ok=True)

    # Tạo tên file cookies dựa trên tên người dùng
    cookies_file = f"{username.replace('@', '_at_').replace('.', '_dot_')}.json"
    return os.path.join(COOKIES_DIR, cookies_file)

def save_cookies(context, username):
    """
    Lưu cookies từ context vào file.
    """
    cookies_path = get_cookies_path(username)
    cookies = context.cookies()
    with open(cookies_path, 'w') as f:
        json.dump(cookies, f)
    print(f"Đã lưu cookies vào {cookies_path}")
    return cookies_path

def load_cookies(context, username):
    """
    Tải cookies từ file và thêm vào context.
    Trả về True nếu thành công, False nếu không tìm thấy file cookies.
    """
    cookies_path = get_cookies_path(username)
    if os.path.exists(cookies_path):
        try:
            with open(cookies_path, 'r') as f:
                cookies = json.load(f)
            context.add_cookies(cookies)
            print(f"Đã tải cookies từ {cookies_path}")
            return True
        except Exception as e:
            print(f"Lỗi khi tải cookies: {str(e)}")
            return False
    return False

def get_browser_args():
    """
    Trả về danh sách các tham số cho trình duyệt để tránh bị phát hiện là bot
    """
    return [
        '--disable-gpu',
        '--disable-dev-shm-usage',
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-blink-features=AutomationControlled',
        '--disable-web-security',
        '--disable-features=IsolateOrigins,site-per-process',
        '--ignore-certificate-errors',
        '--disable-extensions',
        '--disable-infobars',
        '--start-maximized',
        '--window-size=1280,720',
        '--disable-automation',
        '--disable-blink-features',
        '--disable-domain-reliability',
        '--disable-breakpad',
        '--disable-sync',
        '--disable-translate',
        '--metrics-recording-only',
        '--disable-hang-monitor',
        '--disable-component-update',
        '--disable-popup-blocking',
        '--disable-default-apps',
        '--disable-prompt-on-repost',
        '--disable-client-side-phishing-detection',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-background-timer-throttling',
        '--disable-ipc-flooding-protection',
        '--password-store=basic',
        '--use-mock-keychain',
        '--force-color-profile=srgb',
        '--enable-features=NetworkService,NetworkServiceInProcess',
        '--allow-pre-commit-input',
        '--disable-speech-api',
        '--autoplay-policy=user-gesture-required',
        '--disable-session-crashed-bubble',
        '--disable-remote-fonts',
        '--enable-automation=false',
        '--lang=en-US,en',
        '--enable-webgl',
        '--use-angle=gl',
        '--use-gl=desktop',
        '--disable-notifications',
        '--mute-audio',
        '--no-first-run',
        '--no-default-browser-check'
    ]

def get_stealth_script():
    """
    Trả về JavaScript để tránh bị phát hiện là trình duyệt tự động
    """
    return """
    // Ẩn thuộc tính webdriver
    Object.defineProperty(navigator, 'webdriver', {
        get: () => false,
    });

    // Tạo plugins giả
    const mockPlugins = [
        { description: "PDF Viewer", filename: "internal-pdf-viewer", name: "Chrome PDF Viewer" },
        { description: "Portable Document Format", filename: "internal-pdf-viewer", name: "Chrome PDF Plugin" },
        { description: "Chromium PDF Viewer", filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai", name: "Chromium PDF Viewer" },
        { description: "Native Client", filename: "internal-nacl-plugin", name: "Native Client" }
    ];

    // Overwrite the `plugins` property to use a custom getter.
    Object.defineProperty(navigator, 'plugins', {
        get: () => Object.defineProperties({}, {
            length: { value: mockPlugins.length },
            item: { value: (index) => mockPlugins[index] },
            namedItem: { value: (name) => mockPlugins.find(plugin => plugin.name === name) },
            refresh: { value: () => {} },
            [Symbol.iterator]: { value: function* () { for (let i = 0; i < mockPlugins.length; i++) yield mockPlugins[i]; } }
        })
    });

    // Overwrite the `languages` property to use a custom getter.
    Object.defineProperty(navigator, 'languages', {
        get: () => ['en-US', 'en', 'ja'],
    });

    // Tạo mimeTypes giả
    const mockMimeTypes = [
        { description: "Portable Document Format", suffixes: "pdf", type: "application/pdf" },
        { description: "Portable Document Format", suffixes: "pdf", type: "text/pdf" },
        { description: "Native Client Executable", suffixes: "", type: "application/x-nacl" },
        { description: "Portable Native Client Executable", suffixes: "", type: "application/x-pnacl" }
    ];

    Object.defineProperty(navigator, 'mimeTypes', {
        get: () => Object.defineProperties({}, {
            length: { value: mockMimeTypes.length },
            item: { value: (index) => mockMimeTypes[index] },
            namedItem: { value: (name) => mockMimeTypes.find(mimeType => mimeType.type === name) },
            [Symbol.iterator]: { value: function* () { for (let i = 0; i < mockMimeTypes.length; i++) yield mockMimeTypes[i]; } }
        })
    });

    // Tạo giá trị ngẫu nhiên cho các thuộc tính phần cứng
    const getRandomInt = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;

    // Ghi đè thuộc tính hardwareConcurrency
    Object.defineProperty(navigator, 'hardwareConcurrency', {
        get: () => getRandomInt(4, 16)
    });

    // Ghi đè thuộc tính deviceMemory
    Object.defineProperty(navigator, 'deviceMemory', {
        get: () => getRandomInt(4, 32)
    });

    // Ghi đè thuộc tính connection
    Object.defineProperty(navigator, 'connection', {
        get: () => ({
            effectiveType: ['3g', '4g'][getRandomInt(0, 1)],
            rtt: getRandomInt(50, 150),
            downlink: getRandomInt(5, 30),
            saveData: false
        })
    });

    // Ghi đè thuộc tính permissions
    if (navigator.permissions) {
        const originalQuery = navigator.permissions.query;
        navigator.permissions.query = function(parameters) {
            if (parameters.name === 'notifications' || parameters.name === 'geolocation') {
                return Promise.resolve({ state: 'prompt', onchange: null });
            }
            return originalQuery.call(this, parameters);
        };
    }
    """

def get_stealth_config():
    """
    Trả về cấu hình stealth mode để tránh bị phát hiện là trình duyệt tự động.
    """
    # Danh sách các user agent phổ biến
    user_agents = [
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.1.15",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 Edg/122.0.0.0"
    ]

    # Danh sách các kích thước màn hình phổ biến
    viewports = [
        {"width": 1280, "height": 800},
        {"width": 1366, "height": 768},
        {"width": 1440, "height": 900},
        {"width": 1920, "height": 1080}
    ]

    # Danh sách các múi giờ
    timezones = [
        "America/New_York",
        "America/Los_Angeles",
        "Europe/London",
        "Asia/Tokyo",
        "Asia/Singapore"
    ]

    # Chọn ngẫu nhiên các giá trị
    selected_user_agent = random.choice(user_agents)
    selected_viewport = random.choice(viewports)
    selected_timezone = random.choice(timezones)

    return {
        "user_agent": selected_user_agent,
        "viewport": selected_viewport,
        "device_scale_factor": random.choice([1, 1.5, 2]),
        "is_mobile": False,
        "has_touch": False,
        "locale": "en-US",
        "timezone_id": selected_timezone,
        "color_scheme": "light",
        "permissions": ["geolocation", "notifications"],
        "java_script_enabled": True,
        "extra_http_headers": {
            "Accept-Language": "en-US,en;q=0.9",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "sec-ch-ua": '"Chromium";v="122", "Google Chrome";v="122", "Not:A-Brand";v="99"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"macOS"',
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Upgrade-Insecure-Requests": "1"
        }
    }

async def minus_login_and_chat(username: str, password: str, chat_content: str, proxy_host=None, proxy_port=None, proxy_username=None, proxy_password=None, use_cookies=True, use_google_login=True, chrome_profile_path=None):
    """
    Tự động đăng nhập vào Minus và gửi tin nhắn bằng Playwright.
    Sử dụng phương pháp động để tìm các selector, tăng độ tin cậy khi giao diện thay đổi.

    Args:
        username (str): Email đăng nhập
        password (str): Mật khẩu đăng nhập
        chat_content (str): Nội dung tin nhắn muốn gửi
        proxy_host (str, optional): Host của proxy. Defaults to None.
        proxy_port (str, optional): Port của proxy. Defaults to None.
        proxy_username (str, optional): Username của proxy. Defaults to None.
        proxy_password (str, optional): Password của proxy. Defaults to None.
        use_cookies (bool, optional): Sử dụng cookies đã lưu. Defaults to True.
        use_google_login (bool, optional): Sử dụng đăng nhập bằng Google. Defaults to True.
        chrome_profile_path (str, optional): Đường dẫn đến thư mục profile Chrome. Defaults to None.
    """
    proxy = get_proxy_config(proxy_host, proxy_port, proxy_username, proxy_password)
    stealth_config = get_stealth_config()

    # Thêm các tham số cho trình duyệt để tránh bị phát hiện là bot
    window_width = stealth_config["viewport"]["width"]
    window_height = stealth_config["viewport"]["height"]

    browser_args = [
        '--disable-gpu',
        '--disable-dev-shm-usage',
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-blink-features=AutomationControlled',
        '--disable-web-security',
        '--disable-features=IsolateOrigins,site-per-process',
        '--ignore-certificate-errors',
        '--disable-extensions',
        '--disable-infobars',
        f'--window-size={window_width},{window_height}',
        f'--user-agent={stealth_config["user_agent"]}',
        '--disable-automation',
        '--disable-blink-features',
        '--disable-domain-reliability',
        '--disable-breakpad',
        '--disable-sync',
        '--disable-translate',
        '--metrics-recording-only',
        '--disable-hang-monitor',
        '--disable-component-update',
        '--disable-popup-blocking',
        '--disable-default-apps',
        '--disable-prompt-on-repost',
        '--disable-client-side-phishing-detection',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-background-timer-throttling',
        '--disable-ipc-flooding-protection',
        '--password-store=basic',
        '--use-mock-keychain',
        '--force-color-profile=srgb',
        '--enable-features=NetworkService,NetworkServiceInProcess',
        '--allow-pre-commit-input',
        '--disable-speech-api',
        '--autoplay-policy=user-gesture-required',
        '--disable-session-crashed-bubble',
        '--disable-remote-fonts',
        '--enable-automation=false',
        '--lang=en-US,en',
        '--enable-webgl',
        '--use-angle=gl',
        '--use-gl=desktop',
        '--disable-notifications',
        '--mute-audio',
        '--no-first-run',
        '--no-default-browser-check',
        '--no-pings',
        '--dns-prefetch-disable',
        '--disable-background-networking',
        '--disable-component-extensions-with-background-pages',
        '--disable-site-isolation-trials',
        '--disable-print-preview',
        '--disable-voice-input',
        '--disable-wake-on-wifi',
        '--disable-reading-from-canvas',
        '--hide-scrollbars',
        '--disable-bundled-ppapi-flash',
        '--disable-logging',
        '--disable-permissions-api',
        '--disable-audio-output',
        '--disable-file-system',
        '--disable-databases',
        '--disable-webgl-image-chromium',
        '--disable-accelerated-2d-canvas',
        '--disable-3d-apis',
        '--disable-motion-input',
        '--disable-signin-scoped-device-id',
        '--disable-offer-store-unmasked-wallet-cards',
        '--disable-offer-upload-credit-cards',
        '--disable-webrtc-hw-encoding',
        '--disable-webrtc-hw-decoding',
        '--disable-webrtc-multiple-routes',
        '--disable-webrtc-hw-vp8-encoding',
        '--disable-webrtc-hw-h264-encoding',
        '--disable-webrtc-software-h264-encoding',
        '--disable-webrtc-encryption',
        '--disable-webrtc-encryption-dtls',
        '--disable-webrtc-encryption-srtp',
        '--disable-webrtc-stun-origin',
        '--disable-webrtc-stun-origin-header',
        '--disable-webrtc-stun-additional-ice-servers',
        '--disable-webrtc-stun-ice-server-override',
        '--disable-webrtc-stun-ice-server-override-udp',
        '--disable-webrtc-stun-ice-server-override-tcp',
        '--disable-webrtc-stun-ice-server-override-tls',
        '--disable-webrtc-stun-ice-server-override-dtls',
        '--disable-webrtc-stun-ice-server-override-srtp',
        '--disable-webrtc-stun-ice-server-override-quic',
        '--disable-webrtc-stun-ice-server-override-sctp',
        '--disable-webrtc-stun-ice-server-override-udp-turn',
        '--disable-webrtc-stun-ice-server-override-tcp-turn',
        '--disable-webrtc-stun-ice-server-override-tls-turn',
        '--disable-webrtc-stun-ice-server-override-dtls-turn',
        '--disable-webrtc-stun-ice-server-override-srtp-turn',
        '--disable-webrtc-stun-ice-server-override-quic-turn',
        '--disable-webrtc-stun-ice-server-override-sctp-turn'
    ]

    if proxy_host:
        print(f"Sử dụng proxy: {proxy_host}:{proxy_port}")

    async with async_playwright() as p:
        # Sử dụng biến môi trường để kiểm soát chế độ headless
        headless_mode = os.environ.get("PLAYWRIGHT_HEADLESS", "true").lower() == "true"

        # Thêm tham số slow_mo để làm chậm các thao tác, giúp tránh bị phát hiện là bot
        slow_mo = random.randint(50, 150) if headless_mode else 0

        # Thiết lập các tùy chọn context
        context_options = {
            "viewport": stealth_config["viewport"],
            "device_scale_factor": stealth_config["device_scale_factor"],
            "is_mobile": stealth_config["is_mobile"],
            "has_touch": stealth_config["has_touch"],
            "locale": stealth_config["locale"],
            "timezone_id": stealth_config["timezone_id"],
            "color_scheme": stealth_config["color_scheme"],
            "extra_http_headers": stealth_config["extra_http_headers"]
        }

        # Kiểm tra xem có sử dụng Chrome profile hay không
        if chrome_profile_path:
            print(f"Sử dụng Chrome profile tại: {chrome_profile_path}")
            # Sử dụng launch_persistent_context khi có Chrome profile
            # Tạo bản sao của browser_args để tránh thay đổi tham số gốc
            profile_args = browser_args.copy()

            # Loại bỏ tham số --user-data-dir nếu có
            profile_args = [arg for arg in profile_args if not arg.startswith('--user-data-dir=')]

            # Thêm tham số profile-directory để tránh xung đột
            session_id = int(time.time() * 1000)
            profile_args.append(f"--profile-directory=Profile_{session_id}")

            context = await p.chromium.launch_persistent_context(
                user_data_dir=chrome_profile_path,
                headless=headless_mode,
                proxy=proxy,
                args=profile_args,
                slow_mo=slow_mo,
                **context_options
            )
            # Trong trường hợp này, context đã bao gồm cả browser
            browser = context.browser
        else:
            # Thiết lập tham số khởi động trình duyệt thông thường
            launch_options = {
                "headless": headless_mode,
                "proxy": proxy,
                "args": browser_args,
                "slow_mo": slow_mo,
                "chromium_sandbox": False
            }

            # Khởi động trình duyệt với các tùy chọn đã thiết lập
            browser = await p.chromium.launch(**launch_options)

            # Tạo context với cấu hình stealth mode
            context = await browser.new_context(**context_options)

        # Thêm JavaScript để tránh bị phát hiện là trình duyệt tự động
        await context.add_init_script("""
        // Ẩn thuộc tính webdriver
        Object.defineProperty(navigator, 'webdriver', {
            get: () => false,
        });

        // Tạo plugins giả
        const mockPlugins = [
            { description: "PDF Viewer", filename: "internal-pdf-viewer", name: "Chrome PDF Viewer" },
            { description: "Portable Document Format", filename: "internal-pdf-viewer", name: "Chrome PDF Plugin" },
            { description: "Chromium PDF Viewer", filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai", name: "Chromium PDF Viewer" },
            { description: "Native Client", filename: "internal-nacl-plugin", name: "Native Client" }
        ];

        // Overwrite the `plugins` property to use a custom getter.
        Object.defineProperty(navigator, 'plugins', {
            get: () => Object.defineProperties({}, {
                length: { value: mockPlugins.length },
                item: { value: (index) => mockPlugins[index] },
                namedItem: { value: (name) => mockPlugins.find(plugin => plugin.name === name) },
                refresh: { value: () => {} },
                [Symbol.iterator]: { value: function* () { for (let i = 0; i < mockPlugins.length; i++) yield mockPlugins[i]; } }
            })
        });

        // Overwrite the `languages` property to use a custom getter.
        Object.defineProperty(navigator, 'languages', {
            get: () => ['en-US', 'en', 'ja'],
        });

        // Tạo mimeTypes giả
        const mockMimeTypes = [
            { description: "Portable Document Format", suffixes: "pdf", type: "application/pdf" },
            { description: "Portable Document Format", suffixes: "pdf", type: "text/pdf" },
            { description: "Native Client Executable", suffixes: "", type: "application/x-nacl" },
            { description: "Portable Native Client Executable", suffixes: "", type: "application/x-pnacl" }
        ];

        Object.defineProperty(navigator, 'mimeTypes', {
            get: () => Object.defineProperties({}, {
                length: { value: mockMimeTypes.length },
                item: { value: (index) => mockMimeTypes[index] },
                namedItem: { value: (name) => mockMimeTypes.find(mimeType => mimeType.type === name) },
                [Symbol.iterator]: { value: function* () { for (let i = 0; i < mockMimeTypes.length; i++) yield mockMimeTypes[i]; } }
            })
        });

        // Tạo giá trị ngẫu nhiên cho các thuộc tính phần cứng
        const getRandomInt = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;

        // Ghi đè thuộc tính hardwareConcurrency
        Object.defineProperty(navigator, 'hardwareConcurrency', {
            get: () => getRandomInt(4, 16)
        });

        // Ghi đè thuộc tính deviceMemory
        Object.defineProperty(navigator, 'deviceMemory', {
            get: () => getRandomInt(4, 32)
        });

        // Ghi đè thuộc tính connection
        Object.defineProperty(navigator, 'connection', {
            get: () => ({
                effectiveType: ['3g', '4g'][getRandomInt(0, 1)],
                rtt: getRandomInt(50, 150),
                downlink: getRandomInt(5, 30),
                saveData: false
            })
        });

        // Ghi đè thuộc tính permissions
        if (navigator.permissions) {
            const originalQuery = navigator.permissions.query;
            navigator.permissions.query = function(parameters) {
                if (parameters.name === 'notifications' || parameters.name === 'geolocation') {
                    return Promise.resolve({ state: 'prompt', onchange: null });
                }
                return originalQuery.call(this, parameters);
            };
        }

        // Ghi đè thuộc tính userAgent để đảm bảo nhất quán
        Object.defineProperty(navigator, 'userAgent', {
            get: () => window.navigator.userAgent
        });

        // Ghi đè thuộc tính platform để đảm bảo nhất quán
        Object.defineProperty(navigator, 'platform', {
            get: () => window.navigator.platform || 'MacIntel'
        });

        // Ghi đè thuộc tính vendor để đảm bảo nhất quán
        Object.defineProperty(navigator, 'vendor', {
            get: () => 'Google Inc.'
        });

        // Ghi đè thuộc tính appVersion để đảm bảo nhất quán
        Object.defineProperty(navigator, 'appVersion', {
            get: () => window.navigator.appVersion
        });

        // Ghi đè thuộc tính productSub để đảm bảo nhất quán
        Object.defineProperty(navigator, 'productSub', {
            get: () => '20030107'
        });

        // Ghi đè thuộc tính maxTouchPoints để đảm bảo nhất quán
        Object.defineProperty(navigator, 'maxTouchPoints', {
            get: () => getRandomInt(0, 5)
        });

        // Ghi đè thuộc tính doNotTrack để đảm bảo nhất quán
        Object.defineProperty(navigator, 'doNotTrack', {
            get: () => null
        });

        // Ghi đè thuộc tính cookieEnabled để đảm bảo nhất quán
        Object.defineProperty(navigator, 'cookieEnabled', {
            get: () => true
        });

        // Ghi đè thuộc tính appName để đảm bảo nhất quán
        Object.defineProperty(navigator, 'appName', {
            get: () => 'Netscape'
        });

        // Ghi đè thuộc tính appCodeName để đảm bảo nhất quán
        Object.defineProperty(navigator, 'appCodeName', {
            get: () => 'Mozilla'
        });

        // Ghi đè thuộc tính onLine để đảm bảo nhất quán
        Object.defineProperty(navigator, 'onLine', {
            get: () => true
        });

        // Ghi đè thuộc tính pdfViewerEnabled để đảm bảo nhất quán
        Object.defineProperty(navigator, 'pdfViewerEnabled', {
            get: () => true
        });
        """)

        # Tải cookies nếu có
        cookies_loaded = False
        if use_cookies:
            cookies_loaded = load_cookies(context, username)

        page = await context.new_page()
        print("Đang mở trang đăng nhập...")
        await page.goto(MINUS_URL)

        # Tăng thời gian chờ để đảm bảo trang đã tải hoàn toàn
        try:
            await page.wait_for_selector('h1:has-text("Sign in to Manus")', timeout=60000)
            print("Đã tìm thấy tiêu đề trang đăng nhập")
        except Exception as e:
            print(f"Không tìm thấy tiêu đề trang đăng nhập: {str(e)}")
            # Chụp ảnh màn hình để debug
            await page.screenshot(path="/app/screenshots/login_page_error.png")
            print("Đã chụp ảnh màn hình trang lỗi")
            # Tiếp tục thực hiện, có thể trang đã tải nhưng không tìm thấy tiêu đề chính xác

        # Chụp ảnh màn hình trang đăng nhập
        await page.screenshot(path="/app/screenshots/login_page.png")
        print("Đã chụp ảnh màn hình trang đăng nhập")

        # Thực hiện đăng nhập trực tiếp bằng email và mật khẩu
        print("Thực hiện đăng nhập trực tiếp...")
        try:
            # Tìm và điền trường email và mật khẩu
            print("Tìm và điền form đăng nhập...")
            try:
                email_input = page.locator('input[type="email"]').or_(
                    page.locator('input[placeholder*="mail"]')
                ).first

                password_input = page.locator('input[type="password"]').or_(
                    page.locator('input[placeholder*="password"]')
                ).first

                await email_input.wait_for(state="visible", timeout=30000)
                await email_input.fill(username, timeout=30000)
                print("Đã điền email")

                await password_input.wait_for(state="visible", timeout=30000)
                await password_input.fill(password, timeout=30000)
                print("Đã điền mật khẩu")

                # Thử nhấn Enter sau khi điền mật khẩu
                await password_input.press("Enter")
                print("Đã nhấn Enter sau khi điền mật khẩu")

                # Chụp ảnh màn hình sau khi điền thông tin
                await page.screenshot(path="/app/screenshots/login_filled.png")
                print("Đã chụp ảnh màn hình sau khi điền thông tin đăng nhập")

            except Exception as e:
                print(f"Lỗi khi tìm hoặc điền trường đăng nhập: {str(e)}")
                await page.screenshot(path="/app/screenshots/login_form_error.png")
                print("Đã chụp ảnh màn hình lỗi form đăng nhập")
                raise

            # Kiểm tra xem có hCaptcha không
            captcha_frame = page.locator('iframe[src*="hcaptcha"]')
            if await captcha_frame.count() > 0:
                print("Phát hiện hCaptcha, đợi người dùng xác thực...")
                # Chụp ảnh màn hình captcha để debug
                await page.screenshot(path="/app/screenshots/captcha_detected.png")
                print("Đã chụp ảnh màn hình captcha")

                # Đợi một khoảng thời gian để người dùng có thể xác thực captcha thủ công
                # hoặc để captcha tự động được giải quyết (nếu có)
                print("Đợi 20 giây để xác thực captcha...")
                await asyncio.sleep(20)

                # Kiểm tra lại xem captcha đã được giải quyết chưa
                captcha_frame = page.locator('iframe[src*="hcaptcha"]')
                if await captcha_frame.count() > 0:
                    print("Captcha vẫn hiển thị sau khi đợi, có thể cần xác thực thủ công")

            # Đợi một khoảng thời gian sau khi điền thông tin và nhấn Enter
            print("Đợi 10 giây sau khi điền thông tin đăng nhập...")
            await asyncio.sleep(10)

            # Kiểm tra xem có hCaptcha không
            captcha_frame = page.locator('iframe[src*="hcaptcha"]')
            if await captcha_frame.count() > 0:
                print("Phát hiện hCaptcha, đợi thêm thời gian để xác thực...")
                # Chụp ảnh màn hình captcha để debug
                await page.screenshot(path="/app/screenshots/captcha_after_enter.png")
                print("Đã chụp ảnh màn hình captcha sau khi nhấn Enter")

                # Đợi thêm thời gian để captcha được xác thực
                print("Đợi thêm 20 giây để captcha được xác thực...")
                await asyncio.sleep(20)

            # Chờ đăng nhập hoàn tất
            await page.wait_for_load_state('networkidle', timeout=30000)
            await page.screenshot(path="/app/screenshots/after_login_attempt.png")
            print("Đã chụp ảnh màn hình sau khi thử đăng nhập")

        except Exception as e:
            print(f"Lỗi trong quá trình đăng nhập trực tiếp: {str(e)}")
            await page.screenshot(path="/app/screenshots/direct_login_error.png")
            print("Đã chụp ảnh màn hình lỗi đăng nhập trực tiếp")
            raise

        # Chờ đăng nhập thành công (chuyển hướng hoặc thay đổi URL)
        try:
            # Chờ URL thay đổi hoặc có dấu hiệu đăng nhập thành công
            print("Đang chờ đăng nhập...")
            await page.wait_for_load_state('networkidle', timeout=30000)

            # Kiểm tra URL để biết đã đăng nhập thành công chưa
            current_url = page.url
            print(f"URL sau khi đăng nhập: {current_url}")

            # Nếu URL chứa '/app' hoặc dấu hiệu đăng nhập thành công
            if '/app' in current_url or 'dashboard' in current_url:
                print("Đăng nhập thành công!")

                # Chụp ảnh màn hình sau khi đăng nhập thành công
                await page.screenshot(path="/app/screenshots/login_success.png")
                print("Đã chụp ảnh màn hình sau khi đăng nhập thành công")

                # Tìm trường nhập chat
                await asyncio.sleep(2)  # Đợi giao diện chat load

                # Tìm các trường nhập liệu có thể là chat input
                print("Tìm trường nhập chat...")
                # Tìm trường nhập chat bằng locator
                print("Tìm trường nhập chat...")
                chat_input = page.locator('textarea').or_(
                    page.locator('[contenteditable="true"]')
                ).or_(
                    page.locator('div[role="textbox"]')
                ).or_(
                    page.locator('input[placeholder*="message"]')
                ).first

                if await chat_input.count() > 0:
                    await chat_input.fill(chat_content)
                    print(f"Đã nhập nội dung chat: {chat_content}")

                    # Chụp ảnh màn hình sau khi nhập nội dung chat
                    await page.screenshot(path="/app/screenshots/chat_input_filled.png")
                    print("Đã chụp ảnh màn hình sau khi nhập nội dung chat")

                    # Tìm nút gửi bằng locator
                    print("Tìm nút gửi...")
                    send_button = page.locator('button', has_text='send').or_(
                        page.locator('button', has_text='gửi')
                    ).or_(
                        page.locator('button:has(svg)') # Tìm nút có chứa icon SVG
                    ).or_(
                        page.locator('button[aria-label="Send"]') # Tìm nút có aria-label là "Send"
                    ).or_(
                        page.locator('button.send-button')
                    ).first

                    if await send_button.count() > 0:
                        await send_button.click()
                        print("Đã nhấp nút gửi")
                    else:
                        print("Không tìm thấy nút gửi rõ ràng, thử nhấn Enter...")
                        await chat_input.press("Enter")
                        print("Đã thử nhấn Enter để gửi")

                    # Đợi tin nhắn được xử lý
                    await page.wait_for_load_state('networkidle', timeout=5000)
                    print("Đã gửi tin nhắn thành công")

                    # Chụp ảnh màn hình sau khi gửi tin nhắn
                    await page.screenshot(path="/app/screenshots/chat_sent.png")
                    print("Đã chụp ảnh màn hình sau khi gửi tin nhắn")

                    # Lấy URL hiện tại làm kết quả
                    result_url = page.url
                else:
                    print("Không tìm thấy trường nhập chat")
                    # Chụp ảnh màn hình để debug
                    await page.screenshot(path="/app/screenshots/chat_input_error.png")
                    print("Đã chụp ảnh màn hình lỗi trường nhập chat")
                    result_url = current_url

                # Lưu cookies nếu đăng nhập thành công
                if use_cookies:
                    save_cookies(context, username)

                await browser.close()
                return result_url
            else:
                print("Đăng nhập không thành công hoặc không xác định được trang đích")
                # Chụp ảnh màn hình để debug
                await page.screenshot(path="/app/screenshots/login_failed.png")
                print("Đã chụp ảnh màn hình đăng nhập không thành công")
                await browser.close()
                return {"status": "error", "message": "Đăng nhập không thành công"}

        except Exception as e:
            print(f"Lỗi trong quá trình đăng nhập hoặc gửi chat: {str(e)}")
            # Chụp ảnh màn hình để debug
            await page.screenshot(path="/app/screenshots/general_error.png")
            print("Đã chụp ảnh màn hình lỗi chung")
            await browser.close()
            return {"status": "error", "message": str(e)}
