#!/usr/bin/env python3
import os
import sys
import asyncio
import argparse
import random
import json
from pathlib import Path
from playwright.async_api import async_playwright

# <PERSON><PERSON><PERSON> mục mặc định cho Chrome profile
DEFAULT_PROFILE_DIR = os.path.join(os.path.expanduser("~"), "chrome_profiles")

def get_stealth_config():
    """
    Tạo cấu hình stealth mode để tránh bị phát hiện là trình duyệt tự động.

    Returns:
        dict: <PERSON><PERSON>u hình stealth mode
    """
    # Danh sách User-Agent phổ biến
    user_agents = [
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.1.15",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 Edg/122.0.0.0"
    ]

    # Danh sách viewport phổ biến
    viewports = [
        {"width": 1280, "height": 720},
        {"width": 1366, "height": 768},
        {"width": 1440, "height": 900},
        {"width": 1536, "height": 864},
        {"width": 1680, "height": 1050},
        {"width": 1920, "height": 1080},
        {"width": 2560, "height": 1440}
    ]

    # Danh sách múi giờ phổ biến
    timezones = [
        "America/New_York",
        "America/Los_Angeles",
        "America/Chicago",
        "Europe/London",
        "Europe/Paris",
        "Asia/Tokyo",
        "Asia/Singapore",
        "Australia/Sydney",
        "Asia/Ho_Chi_Minh"
    ]

    # Chọn ngẫu nhiên các giá trị
    selected_user_agent = random.choice(user_agents)
    selected_viewport = random.choice(viewports)
    selected_timezone = random.choice(timezones)

    return {
        "user_agent": selected_user_agent,
        "viewport": selected_viewport,
        "device_scale_factor": random.choice([1, 1.5, 2]),
        "is_mobile": False,
        "has_touch": False,
        "locale": "en-US",
        "timezone_id": selected_timezone,
        "color_scheme": "light",
        "permissions": ["geolocation", "notifications"],
        "java_script_enabled": True,
        "extra_http_headers": {
            "Accept-Language": "en-US,en;q=0.9",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "sec-ch-ua": '"Chromium";v="122", "Google Chrome";v="122", "Not:A-Brand";v="99"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"macOS"',
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Upgrade-Insecure-Requests": "1"
        }
    }

def get_browser_args():
    """
    Tạo danh sách các tham số khởi động cho trình duyệt Chrome.

    Returns:
        list: Danh sách các tham số khởi động
    """
    return [
        '--disable-gpu',
        '--disable-dev-shm-usage',
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-blink-features=AutomationControlled',
        '--disable-web-security',
        '--disable-features=IsolateOrigins,site-per-process',
        '--ignore-certificate-errors',
        '--disable-extensions',
        '--disable-infobars',
        '--start-maximized',
        '--window-size=1280,720',
        '--disable-automation',
        '--disable-blink-features',
        '--disable-domain-reliability',
        '--disable-breakpad',
        '--disable-sync',
        '--disable-translate',
        '--metrics-recording-only',
        '--disable-hang-monitor',
        '--disable-component-update',
        '--disable-popup-blocking',
        '--disable-default-apps',
        '--disable-prompt-on-repost',
        '--disable-client-side-phishing-detection',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-background-timer-throttling',
        '--disable-ipc-flooding-protection',
        '--password-store=basic',
        '--use-mock-keychain',
        '--force-color-profile=srgb',
        '--enable-features=NetworkService,NetworkServiceInProcess',
        '--allow-pre-commit-input',
        '--disable-speech-api',
        '--autoplay-policy=user-gesture-required',
        '--disable-session-crashed-bubble',
        '--disable-remote-fonts',
        '--enable-automation=false',
        '--lang=en-US,en',
        '--enable-webgl',
        '--use-angle=gl',
        '--use-gl=desktop',
        '--disable-notifications',
        '--mute-audio',
        '--no-first-run',
        '--no-default-browser-check',
        '--no-pings',
        '--dns-prefetch-disable'
    ]

def get_stealth_script():
    """
    Tạo script JavaScript để tránh bị phát hiện là trình duyệt tự động.

    Returns:
        str: Script JavaScript
    """
    return """
    // Ẩn thuộc tính webdriver
    Object.defineProperty(navigator, 'webdriver', {
        get: () => false,
    });

    // Tạo plugins giả
    const mockPlugins = [
        { description: "PDF Viewer", filename: "internal-pdf-viewer", name: "Chrome PDF Viewer" },
        { description: "Portable Document Format", filename: "internal-pdf-viewer", name: "Chrome PDF Plugin" },
        { description: "Chromium PDF Viewer", filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai", name: "Chromium PDF Viewer" },
        { description: "Native Client", filename: "internal-nacl-plugin", name: "Native Client" }
    ];

    // Overwrite the `plugins` property to use a custom getter.
    Object.defineProperty(navigator, 'plugins', {
        get: () => Object.defineProperties({}, {
            length: { value: mockPlugins.length },
            item: { value: (index) => mockPlugins[index] },
            namedItem: { value: (name) => mockPlugins.find(plugin => plugin.name === name) },
            refresh: { value: () => {} },
            [Symbol.iterator]: { value: function* () { for (let i = 0; i < mockPlugins.length; i++) yield mockPlugins[i]; } }
        })
    });

    // Ẩn thuộc tính chrome
    if (window.chrome) {
        // Tạo chrome runtime giả
        window.chrome.runtime = {
            ...window.chrome.runtime,
            connect: () => ({
                onMessage: {
                    addListener: () => {},
                    removeListener: () => {},
                },
                onDisconnect: {
                    addListener: () => {},
                    removeListener: () => {},
                },
                postMessage: () => {},
                disconnect: () => {},
            }),
        };
    }

    // Tạo ngẫu nhiên các thuộc tính phần cứng
    const getRandomInt = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;

    // Ghi đè thuộc tính hardwareConcurrency
    Object.defineProperty(navigator, 'hardwareConcurrency', {
        get: () => getRandomInt(4, 16)
    });

    // Ghi đè thuộc tính deviceMemory
    Object.defineProperty(navigator, 'deviceMemory', {
        get: () => getRandomInt(4, 32)
    });

    // Ghi đè thuộc tính platform
    const platforms = ['MacIntel', 'Win32', 'Linux x86_64'];
    Object.defineProperty(navigator, 'platform', {
        get: () => platforms[getRandomInt(0, platforms.length - 1)]
    });
    """

async def handle_google_login(page, username=None, password=None):
    """
    Hướng dẫn người dùng đăng nhập vào Google.

    Args:
        page: Trang Playwright
        username (str, optional): Email đăng nhập. Nếu None, người dùng sẽ tự nhập.
        password (str, optional): Mật khẩu đăng nhập. Nếu None, người dùng sẽ tự nhập.
    """
    print("\n=== HƯỚNG DẪN ĐĂNG NHẬP GOOGLE ===")
    print("1. Nhập email Google của bạn và nhấn 'Next'")
    print("2. Nhập mật khẩu và nhấn 'Next'")
    print("3. Nếu được yêu cầu xác minh, hãy làm theo hướng dẫn")
    print("4. Sau khi đăng nhập thành công, nhấn Ctrl+C để lưu profile và thoát")
    print("===============================\n")

    # Nếu có username và password, có thể tự động điền (tùy chọn)
    if username:
        print(f"Tự động điền email: {username}")
        # Đợi form đăng nhập xuất hiện
        await page.wait_for_selector('input[type="email"]', state="visible", timeout=30000)
        # Điền email
        await page.fill('input[type="email"]', username)
        # Nhấn Next
        await page.click('button:has-text("Next")')

        if password:
            print("Đợi trang nhập mật khẩu...")
            # Đợi form mật khẩu xuất hiện (có thể mất thời gian do chuyển trang)
            await page.wait_for_selector('input[type="password"]', state="visible", timeout=30000)
            print("Tự động điền mật khẩu")
            # Điền mật khẩu
            await page.fill('input[type="password"]', password)
            # Nhấn Next
            await page.click('button:has-text("Next")')

            print("Đã điền thông tin đăng nhập. Vui lòng kiểm tra và hoàn tất quá trình đăng nhập nếu cần.")

async def setup_chrome_profile(profile_name, headless=False, url="https://accounts.google.com",
                              username=None, password=None, use_stealth=True):
    """
    Thiết lập một Chrome profile mới và mở trang đăng nhập Google.

    Args:
        profile_name (str): Tên của profile
        headless (bool): Chạy ở chế độ headless hay không
        url (str): URL để mở khi khởi động trình duyệt
        username (str, optional): Email đăng nhập Google. Nếu None, người dùng sẽ tự nhập.
        password (str, optional): Mật khẩu đăng nhập Google. Nếu None, người dùng sẽ tự nhập.
        use_stealth (bool): Sử dụng chế độ stealth để tránh bị phát hiện là bot
    """
    # Tạo thư mục profile nếu chưa tồn tại
    profile_dir = os.path.join(DEFAULT_PROFILE_DIR, profile_name)
    os.makedirs(profile_dir, exist_ok=True)

    print(f"Thiết lập Chrome profile tại: {profile_dir}")

    # Lấy cấu hình stealth
    stealth_config = get_stealth_config() if use_stealth else {}

    # Lấy tham số khởi động trình duyệt
    browser_args = get_browser_args()

    # Lấy script stealth
    stealth_script = get_stealth_script() if use_stealth else ""

    async with async_playwright() as p:
        # Sử dụng persistent_context để lưu trữ profile
        browser_context = await p.chromium.launch_persistent_context(
            user_data_dir=profile_dir,
            headless=headless,
            args=browser_args,
            viewport=stealth_config.get("viewport", {"width": 1280, "height": 720}),
            device_scale_factor=stealth_config.get("device_scale_factor", 1),
            locale=stealth_config.get("locale", "en-US"),
            timezone_id=stealth_config.get("timezone_id", "America/New_York"),
            color_scheme=stealth_config.get("color_scheme", "light"),
            user_agent=stealth_config.get("user_agent", None),
            ignore_https_errors=True
        )

        # Thêm JavaScript để tránh bị phát hiện là trình duyệt tự động
        if use_stealth:
            await browser_context.add_init_script(stealth_script)

        # Mở trang đăng nhập Google
        page = await browser_context.new_page()

        # Thêm extra HTTP headers nếu sử dụng stealth mode
        if use_stealth and "extra_http_headers" in stealth_config:
            await page.set_extra_http_headers(stealth_config["extra_http_headers"])

        # Điều hướng đến URL
        await page.goto(url)

        print(f"Đã mở trang {url}")

        # Nếu URL là trang đăng nhập Google, hiển thị hướng dẫn đăng nhập
        if "accounts.google.com" in url:
            await handle_google_login(page, username, password)
        else:
            print("Vui lòng tương tác với trang web.")
            print("Sau khi hoàn tất, nhấn Ctrl+C để thoát và lưu profile.")

        try:
            # Đợi người dùng tương tác thủ công
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("\nĐang lưu profile và thoát...")
        finally:
            # Lưu cookies nếu cần
            cookies = await browser_context.cookies()
            cookies_path = os.path.join(profile_dir, "cookies.json")
            with open(cookies_path, "w") as f:
                json.dump(cookies, f)
            print(f"Đã lưu cookies tại: {cookies_path}")

            # Đóng trình duyệt
            await browser_context.close()

            print(f"Đã lưu profile tại: {profile_dir}")
            print(f"Để sử dụng profile này với test_playwright.py, hãy chạy lệnh:")
            print(f"python fastapi/app/test_playwright.py --chrome-profile {profile_dir}")

def parse_arguments():
    parser = argparse.ArgumentParser(description="Thiết lập Chrome profile cho Playwright")

    parser.add_argument("--profile-name", default="default",
                        help="Tên của profile (mặc định: 'default')")

    parser.add_argument("--profile-dir", default=DEFAULT_PROFILE_DIR,
                        help=f"Thư mục chứa các profile (mặc định: {DEFAULT_PROFILE_DIR})")

    parser.add_argument("--headless", action="store_true",
                        help="Chạy ở chế độ headless (không hiển thị giao diện)")

    parser.add_argument("--url", default="https://accounts.google.com",
                        help="URL để mở khi khởi động trình duyệt (mặc định: https://accounts.google.com)")

    parser.add_argument("--username", default=None,
                        help="Email đăng nhập Google (tùy chọn)")

    parser.add_argument("--password", default=None,
                        help="Mật khẩu đăng nhập Google (tùy chọn)")

    parser.add_argument("--no-stealth", action="store_true",
                        help="Không sử dụng chế độ stealth")

    return parser.parse_args()

if __name__ == "__main__":
    args = parse_arguments()

    # Cập nhật thư mục profile mặc định nếu được chỉ định
    if args.profile_dir != DEFAULT_PROFILE_DIR:
        DEFAULT_PROFILE_DIR = args.profile_dir

    # Chạy hàm thiết lập profile
    asyncio.run(setup_chrome_profile(
        args.profile_name,
        args.headless,
        args.url,
        args.username,
        args.password,
        not args.no_stealth
    ))
