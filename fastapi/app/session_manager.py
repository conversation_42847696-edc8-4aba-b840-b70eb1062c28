"""
<PERSON><PERSON><PERSON> qu<PERSON><PERSON> lý phiên Playwright v<PERSON> lịch sử cuộc trò chuy<PERSON>n.
"""
import asyncio
import logging
import os
import time
from typing import Dict, List, Optional, Any
from playwright.async_api import async_playwright, <PERSON>, <PERSON>rowser<PERSON>ontex<PERSON>, Browser

# C<PERSON><PERSON> trúc lưu trữ phiên
class PlaywrightSession:
    def __init__(self, 
                 session_id: str, 
                 context: BrowserContext, 
                 browser: Browser, 
                 page: Page, 
                 profile_path: str = None):
        self.session_id = session_id
        self.context = context
        self.browser = browser
        self.page = page
        self.profile_path = profile_path
        self.last_activity = time.time()
        self.chat_history = []
        self.is_active = True
        self.created_at = time.time()

# Quản lý phiên Playwright
class SessionManager:
    def __init__(self):
        self.sessions: Dict[str, PlaywrightSession] = {}
        self.cleanup_task = None
        self.playwright = None
        self.is_initialized = False
        
    async def initialize(self):
        """Khởi t<PERSON>o Playwright."""
        if not self.is_initialized:
            self.playwright = await async_playwright().start()
            self.is_initialized = True
            # Bắt đầu task dọn dẹp phiên không hoạt động
            self.cleanup_task = asyncio.create_task(self._cleanup_inactive_sessions())
            logging.info("SessionManager đã được khởi tạo")
        return self.playwright
    
    async def create_session(self, 
                            session_id: str, 
                            chrome_profile_path: str = None, 
                            headless: bool = True,
                            proxy_config: Dict = None,
                            browser_args: List[str] = None,
                            context_options: Dict = None,
                            slow_mo: int = 0) -> PlaywrightSession:
        """Tạo phiên Playwright mới."""
        if not self.is_initialized:
            await self.initialize()
            
        # Kiểm tra nếu phiên đã tồn tại
        if session_id in self.sessions and self.sessions[session_id].is_active:
            logging.info(f"Phiên {session_id} đã tồn tại, trả về phiên hiện có")
            self.sessions[session_id].last_activity = time.time()
            return self.sessions[session_id]
            
        # Thiết lập tham số mặc định
        if browser_args is None:
            browser_args = [
                '--disable-gpu',
                '--disable-dev-shm-usage',
                '--no-sandbox',
                '--disable-setuid-sandbox',
            ]
            
        if context_options is None:
            context_options = {}
            
        # Thiết lập proxy
        playwright_proxy = None
        if proxy_config:
            playwright_proxy = {
                "server": f"http://{proxy_config.get('host')}:{proxy_config.get('port')}"
            }
            if proxy_config.get('username') and proxy_config.get('password'):
                playwright_proxy["username"] = proxy_config.get('username')
                playwright_proxy["password"] = proxy_config.get('password')
        
        try:
            # Khởi động trình duyệt với profile
            if chrome_profile_path:
                # Đảm bảo thư mục profile tồn tại
                os.makedirs(chrome_profile_path, exist_ok=True)
                
                context = await self.playwright.chromium.launch_persistent_context(
                    user_data_dir=chrome_profile_path,
                    headless=headless,
                    args=browser_args,
                    proxy=playwright_proxy,
                    slow_mo=slow_mo,
                    **context_options
                )
                browser = context.browser
            else:
                # Khởi động trình duyệt thông thường
                browser = await self.playwright.chromium.launch(
                    headless=headless,
                    args=browser_args,
                    proxy=playwright_proxy,
                    slow_mo=slow_mo
                )
                context = await browser.new_context(**context_options)
                
            # Mở trang mới
            page = await context.new_page()
            
            # Tạo phiên mới
            session = PlaywrightSession(
                session_id=session_id,
                context=context,
                browser=browser,
                page=page,
                profile_path=chrome_profile_path
            )
            
            # Lưu phiên
            self.sessions[session_id] = session
            logging.info(f"Đã tạo phiên mới với ID: {session_id}")
            
            return session
        except Exception as e:
            logging.error(f"Lỗi khi tạo phiên Playwright: {str(e)}")
            raise
    
    def get_session(self, session_id: str) -> Optional[PlaywrightSession]:
        """Lấy phiên theo ID."""
        if session_id in self.sessions and self.sessions[session_id].is_active:
            self.sessions[session_id].last_activity = time.time()
            return self.sessions[session_id]
        return None
    
    async def close_session(self, session_id: str):
        """Đóng phiên theo ID."""
        if session_id in self.sessions:
            session = self.sessions[session_id]
            try:
                if session.context:
                    await session.context.close()
                # Chỉ đóng browser nếu không dùng persistent context
                if session.browser and not session.profile_path:
                    await session.browser.close()
                session.is_active = False
                logging.info(f"Đã đóng phiên: {session_id}")
            except Exception as e:
                logging.error(f"Lỗi khi đóng phiên {session_id}: {str(e)}")
            finally:
                # Xóa phiên khỏi dictionary
                del self.sessions[session_id]
    
    async def update_chat_history(self, session_id: str, message: Dict[str, Any]):
        """Cập nhật lịch sử cuộc trò chuyện cho phiên."""
        session = self.get_session(session_id)
        if session:
            session.chat_history.append(message)
            session.last_activity = time.time()
            logging.info(f"Đã cập nhật lịch sử cuộc trò chuyện cho phiên {session_id}")
            return True
        return False
    
    async def get_chat_history(self, session_id: str) -> List[Dict[str, Any]]:
        """Lấy lịch sử cuộc trò chuyện của phiên."""
        session = self.get_session(session_id)
        if session:
            return session.chat_history
        return []
    
    async def _cleanup_inactive_sessions(self):
        """Dọn dẹp các phiên không hoạt động."""
        try:
            while True:
                await asyncio.sleep(300)  # Kiểm tra mỗi 5 phút
                current_time = time.time()
                sessions_to_close = []
                
                for session_id, session in self.sessions.items():
                    # Đóng phiên nếu không hoạt động trong 30 phút
                    if current_time - session.last_activity > 1800:  # 30 phút
                        sessions_to_close.append(session_id)
                
                for session_id in sessions_to_close:
                    logging.info(f"Đóng phiên không hoạt động: {session_id}")
                    await self.close_session(session_id)
        except asyncio.CancelledError:
            logging.info("Task dọn dẹp phiên đã bị hủy")
        except Exception as e:
            logging.error(f"Lỗi trong task dọn dẹp phiên: {str(e)}")
    
    async def shutdown(self):
        """Đóng tất cả các phiên và dọn dẹp tài nguyên."""
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
        
        # Đóng tất cả các phiên
        for session_id in list(self.sessions.keys()):
            await self.close_session(session_id)
        
        # Đóng Playwright
        if self.playwright:
            await self.playwright.stop()
            self.is_initialized = False
            logging.info("SessionManager đã được đóng")

# Tạo instance toàn cục
session_manager = SessionManager()
