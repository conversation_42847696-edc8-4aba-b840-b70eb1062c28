"""
Module qu<PERSON><PERSON> lý kết nối WebSocket.
"""
import logging
import json
from typing import Dict, List, Set, Any, Optional
from fastapi import WebSocket, WebSocketDisconnect
import asyncio
from .session_manager import session_manager

class ConnectionManager:
    def __init__(self):
        # <PERSON><PERSON><PERSON> trữ kết nối WebSocket theo session_id
        self.active_connections: Dict[str, WebSocket] = {}
        # Lưu trữ task đang chạy theo session_id
        self.active_tasks: Dict[str, asyncio.Task] = {}
        
    async def connect(self, websocket: WebSocket, session_id: str):
        """Kết nối WebSocket mới."""
        await websocket.accept()
        self.active_connections[session_id] = websocket
        logging.info(f"WebSocket kết nối mới với session_id: {session_id}")
        
    def disconnect(self, session_id: str):
        """Ngắt kết nối WebSocket."""
        if session_id in self.active_connections:
            del self.active_connections[session_id]
            logging.info(f"WebSocket ngắt kết nối: {session_id}")
        
        # Hủy task nếu đang chạy
        if session_id in self.active_tasks:
            self.active_tasks[session_id].cancel()
            del self.active_tasks[session_id]
            logging.info(f"Đã hủy task cho session_id: {session_id}")
            
    async def send_message(self, session_id: str, message: Any):
        """Gửi tin nhắn đến WebSocket cụ thể."""
        if session_id in self.active_connections:
            websocket = self.active_connections[session_id]
            try:
                if isinstance(message, str):
                    await websocket.send_text(message)
                elif isinstance(message, dict):
                    await websocket.send_json(message)
                else:
                    await websocket.send_text(str(message))
                return True
            except Exception as e:
                logging.error(f"Lỗi khi gửi tin nhắn đến {session_id}: {str(e)}")
                return False
        else:
            logging.warning(f"Không tìm thấy kết nối WebSocket cho session_id: {session_id}")
            return False
            
    async def broadcast(self, message: Any):
        """Gửi tin nhắn đến tất cả các kết nối."""
        disconnected_sessions = []
        for session_id, websocket in self.active_connections.items():
            try:
                if isinstance(message, str):
                    await websocket.send_text(message)
                elif isinstance(message, dict):
                    await websocket.send_json(message)
                else:
                    await websocket.send_text(str(message))
            except Exception as e:
                logging.error(f"Lỗi khi gửi tin nhắn đến {session_id}: {str(e)}")
                disconnected_sessions.append(session_id)
                
        # Xóa các kết nối bị ngắt
        for session_id in disconnected_sessions:
            self.disconnect(session_id)
            
    async def start_task(self, session_id: str, coro):
        """Bắt đầu một task mới cho session."""
        # Hủy task cũ nếu có
        if session_id in self.active_tasks:
            self.active_tasks[session_id].cancel()
            try:
                await self.active_tasks[session_id]
            except asyncio.CancelledError:
                pass
            
        # Tạo task mới
        task = asyncio.create_task(coro)
        self.active_tasks[session_id] = task
        
        # Xử lý khi task hoàn thành
        task.add_done_callback(lambda t: self._task_done_callback(t, session_id))
        
        return task
    
    def _task_done_callback(self, task, session_id):
        """Callback khi task hoàn thành."""
        if session_id in self.active_tasks:
            del self.active_tasks[session_id]
            
        # Xử lý ngoại lệ nếu có
        if not task.cancelled():
            exception = task.exception()
            if exception:
                logging.error(f"Task cho session {session_id} gặp lỗi: {str(exception)}")
                
    async def handle_websocket_disconnect(self, session_id: str):
        """Xử lý khi WebSocket bị ngắt kết nối."""
        # Ngắt kết nối WebSocket
        self.disconnect(session_id)
        
        # Đóng phiên Playwright
        await session_manager.close_session(session_id)
        
        logging.info(f"Đã xử lý ngắt kết nối WebSocket cho session_id: {session_id}")

# Tạo instance toàn cục
connection_manager = ConnectionManager()
