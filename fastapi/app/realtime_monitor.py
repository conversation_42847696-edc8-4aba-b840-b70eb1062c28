"""
Realtime Monitoring Module for Manus Chat

This module provides functionality for realtime monitoring and data extraction
from Manus.im chat interface using Playwright.
"""

import asyncio
import json
import logging
import time
from typing import Dict, Any, Optional, List, Callable, Union

from playwright.async_api import Page, TimeoutError, Error as PlaywrightError

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('realtime_monitor')

async def extract_chat_messages(page: Page) -> List[Dict[str, Any]]:
    """
    Extracts all messages with complete metadata including HTML structure from the current page.
    
    Args:
        page: Playwright Page object
        
    Returns:
        List of message objects with content, html, and metadata
    """
    try:
        # Trích xuất tất cả message containers từ DOM
        messages = await page.query_selector_all("div[data-event-id]")
        
        results = []
        for msg in messages:
            # Trích xuất message ID
            msg_id = await msg.get_attribute("data-event-id")
            
            # Trích xuất HTML nội dung
            html_content = await msg.inner_html()
            
            # Trích xuất text nội dung
            text_content = await msg.inner_text()
            
            # Trích xuất timestamp nếu có
            timestamp_elem = await msg.query_selector(".text-\\[var\\(--text-tertiary\\)\\]")
            timestamp = await timestamp_elem.inner_text() if timestamp_elem else None
            
            # Kiểm tra xem đây có phải là tin nhắn hoàn thành không
            completion_indicator = await msg.query_selector(".lucide-check")
            is_completed = completion_indicator is not None
            
            results.append({
                "id": msg_id,
                "html": html_content,
                "text": text_content,
                "timestamp": timestamp,
                "is_completed": is_completed
            })
        
        return results
    
    except Exception as e:
        logger.error(f"Error extracting chat messages: {str(e)}")
        return []

async def extract_complete_chat_context(page: Page) -> Dict[str, Any]:
    """
    Extracts complete chat context including HTML structure and metadata.
    
    Args:
        page: Playwright Page object
        
    Returns:
        Dictionary containing complete chat context
    """
    try:
        # Trích xuất toàn bộ HTML của trang
        full_html = await page.content()
        
        # Trích xuất các tin nhắn
        messages = await extract_chat_messages(page)
        
        # Trích xuất container chính
        main_container = await page.query_selector(".simplebar-content-wrapper")
        main_html = await main_container.inner_html() if main_container else None
        
        # Trích xuất sidebar nếu có
        sidebar = await page.query_selector('div[class*="overflow-hidden sidebar"]')
        sidebar_html = await sidebar.inner_html() if sidebar else None
        
        # Kiểm tra các yếu tố active
        typing_indicator = await page.query_selector(".typing-indicator")
        is_typing = typing_indicator is not None
        
        # Kiểm tra completion notification
        completion_notification = await page.query_selector('div[class*="rounded-full"] svg.lucide-check')
        task_completed = completion_notification is not None
        
        return {
            "timestamp": time.time(),
            "full_html": full_html[:500000],  # Giới hạn kích thước để tránh lỗi
            "main_container_html": main_html,
            "sidebar_html": sidebar_html,
            "messages": messages,
            "is_typing": is_typing,
            "task_completed": task_completed,
            "url": page.url
        }
    
    except Exception as e:
        logger.error(f"Error extracting chat context: {str(e)}")
        return {
            "timestamp": time.time(),
            "error": str(e)
        }

async def monitor_manus_realtime(
    page: Page, 
    session_id: str,
    websocket_send_callback: Callable[[str], None],
    interval: float = 2.0,
    max_duration: int = 900  # 15 minutes
) -> None:
    """
    Monitors Manus interface in real-time and streams data via WebSocket.
    
    Args:
        page: Playwright Page object
        session_id: Unique session identifier
        websocket_send_callback: Callback function to send data through WebSocket
        interval: Polling interval in seconds
        max_duration: Maximum monitoring duration in seconds
    """
    start_time = time.time()
    last_message_count = 0
    last_context = None
    heartbeat_interval = 30  # Send heartbeat every 30 seconds
    last_heartbeat = start_time
    
    try:
        logger.info(f"Starting realtime monitoring for session {session_id}")
        
        # Gửi trạng thái khởi đầu
        initial_context = await extract_complete_chat_context(page)
        initial_message_count = len(initial_context.get("messages", []))
        
        await websocket_send_callback(json.dumps({
            "type": "realtime_update",
            "data": {
                "chat_context": initial_context,
                "elapsed_time": 0,
                "new_messages": 0,
                "monitoring_status": "active"
            }
        }))
        
        # Loop giám sát liên tục
        while time.time() - start_time < max_duration:
            # Chờ đến lần polling tiếp theo
            await asyncio.sleep(interval)
            
            # Kiểm tra trạng thái trang
            if page.is_closed():
                logger.warning(f"Page closed for session {session_id}")
                break
                
            # Trích xuất context hiện tại
            current_context = await extract_complete_chat_context(page)
            current_message_count = len(current_context.get("messages", []))
            elapsed_time = time.time() - start_time
            
            # Phát hiện thay đổi
            new_messages = current_message_count - last_message_count
            has_changes = new_messages > 0
            task_completed = current_context.get("task_completed", False)
            
            # Gửi cập nhật nếu có thay đổi
            if has_changes:
                await websocket_send_callback(json.dumps({
                    "type": "realtime_update",
                    "data": {
                        "chat_context": current_context,
                        "elapsed_time": elapsed_time,
                        "new_messages": new_messages,
                        "monitoring_status": "active"
                    }
                }))
                last_message_count = current_message_count
                last_context = current_context
            
            # Kiểm tra task đã hoàn thành chưa
            if task_completed:
                await websocket_send_callback(json.dumps({
                    "type": "task_completed",
                    "data": {
                        "chat_context": current_context,
                        "elapsed_time": elapsed_time,
                        "completion_detected": True,
                        "monitoring_status": "completed"
                    }
                }))
                logger.info(f"Task completion detected for session {session_id}")
                break
                
            # Gửi heartbeat định kỳ
            current_time = time.time()
            if current_time - last_heartbeat >= heartbeat_interval:
                await websocket_send_callback(json.dumps({
                    "type": "monitoring_heartbeat",
                    "data": {
                        "elapsed_time": elapsed_time,
                        "message_count": current_message_count,
                        "monitoring_status": "active",
                        "remaining_time": max_duration - elapsed_time
                    }
                }))
                last_heartbeat = current_time
        
        # Gửi thông báo kết thúc nếu hết thời gian
        if time.time() - start_time >= max_duration:
            final_context = await extract_complete_chat_context(page)
            await websocket_send_callback(json.dumps({
                "type": "monitoring_timeout",
                "data": {
                    "chat_context": final_context,
                    "elapsed_time": max_duration,
                    "monitoring_status": "timeout"
                }
            }))
            logger.info(f"Monitoring timeout for session {session_id}")
    
    except Exception as e:
        logger.error(f"Error during realtime monitoring for session {session_id}: {str(e)}")
        await websocket_send_callback(json.dumps({
            "type": "monitoring_error",
            "data": {
                "error": str(e),
                "elapsed_time": time.time() - start_time,
                "monitoring_status": "error"
            }
        }))
    
    finally:
        logger.info(f"Realtime monitoring ended for session {session_id}")