import os
import json
import asyncio
import logging
import subprocess
import shutil
from playwright.async_api import async_playwright
from typing import Dict, Any, Optional
import time

# Import stealth utilities
from .playwright_utils import get_stealth_config, get_stealth_script, get_browser_args

# Default profile directory
DEFAULT_PROFILE_DIR = "/root/chrome_profiles"

def unlock_chrome_profile(profile_path: str) -> bool:
    """
    Unlocks a Chrome profile that might be locked by another process.

    Args:
        profile_path: Path to the Chrome profile directory

    Returns:
        bool: True if successfully unlocked or no lock found, False otherwise
    """
    try:
        # Check for lock files and other profile state files
        lock_files = [
            "SingletonLock",
            "SingletonCookie",
            "SingletonSocket",
            ".com.google.Chrome.yLU8m4",
            "lockfile",
            "Lock",
            "lock",
            ".parentlock"
        ]

        # Remove all potential lock files
        for lock_file_name in lock_files:
            lock_file = os.path.join(profile_path, lock_file_name)
            if os.path.exists(lock_file):
                try:
                    os.remove(lock_file)
                    logging.info(f"Removed lock file: {lock_file}")
                except Exception as e:
                    logging.warning(f"Failed to remove lock file {lock_file}: {str(e)}")

        # Kill all Chrome processes in the container
        try:
            # More aggressive approach to find and kill Chrome processes
            kill_commands = [
                "pkill -9 -f chrome",
                "pkill -9 -f chromium",
                "killall -9 chrome",
                "killall -9 chromium"
            ]

            for cmd in kill_commands:
                try:
                    subprocess.run(cmd, shell=True, stderr=subprocess.DEVNULL)
                    logging.info(f"Executed kill command: {cmd}")
                except Exception as e:
                    logging.warning(f"Failed to execute kill command {cmd}: {str(e)}")

            # Specific check for processes using this profile
            cmd = f"ps aux | grep -i 'chrome.*{profile_path}' | grep -v grep | awk '{{print $2}}'"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            pids = result.stdout.strip().split('\n')

            for pid in pids:
                if pid:
                    try:
                        subprocess.run(f"kill -9 {pid}", shell=True)
                        logging.info(f"Killed Chrome process with PID {pid}")
                    except Exception as e:
                        logging.warning(f"Failed to kill process {pid}: {str(e)}")
        except Exception as e:
            logging.warning(f"Error checking for running Chrome processes: {str(e)}")

        # Check if any lock files still exist
        for lock_file_name in lock_files:
            lock_file = os.path.join(profile_path, lock_file_name)
            if os.path.exists(lock_file):
                logging.warning(f"Lock file still exists after cleanup: {lock_file}")
                return False

        return True
    except Exception as e:
        logging.error(f"Error unlocking Chrome profile: {str(e)}")
        return False

def reset_chrome_profile(profile_path: str) -> bool:
    """
    Resets a Chrome profile by removing it and recreating the directory.

    Args:
        profile_path: Path to the Chrome profile directory

    Returns:
        bool: True if successfully reset, False otherwise
    """
    try:
        # First, make sure all Chrome processes are killed
        try:
            # Kill all Chrome processes in the container
            kill_commands = [
                "pkill -9 -f chrome",
                "pkill -9 -f chromium",
                "killall -9 chrome",
                "killall -9 chromium"
            ]

            for cmd in kill_commands:
                try:
                    subprocess.run(cmd, shell=True, stderr=subprocess.DEVNULL)
                    logging.info(f"Executed kill command: {cmd}")
                except Exception as e:
                    logging.warning(f"Failed to execute kill command {cmd}: {str(e)}")
        except Exception as e:
            logging.warning(f"Error killing Chrome processes: {str(e)}")

        if os.path.exists(profile_path):
            # Backup important files if needed
            cookies_path = os.path.join(profile_path, "cookies.json")
            cookies_data = None
            if os.path.exists(cookies_path):
                try:
                    with open(cookies_path, 'r') as f:
                        cookies_data = f.read()
                except Exception as e:
                    logging.warning(f"Failed to backup cookies: {str(e)}")

            # Try to remove lock files first
            unlock_chrome_profile(profile_path)

            # Remove the entire profile directory
            try:
                # First try with shutil.rmtree
                shutil.rmtree(profile_path, ignore_errors=True)
                logging.info(f"Removed profile directory with shutil.rmtree: {profile_path}")
            except Exception as e:
                logging.warning(f"Failed to remove directory with shutil.rmtree: {str(e)}")
                try:
                    # If that fails, try with rm -rf
                    subprocess.run(f"rm -rf {profile_path}", shell=True)
                    logging.info(f"Removed profile directory with rm -rf: {profile_path}")
                except Exception as e:
                    logging.error(f"Failed to remove directory with rm -rf: {str(e)}")
                    return False

            # Wait a moment to ensure the directory is fully removed
            time.sleep(1)

            # Recreate the directory
            os.makedirs(profile_path, exist_ok=True)
            logging.info(f"Recreated profile directory: {profile_path}")

            # Set proper permissions
            try:
                os.system(f"chmod -R 777 {profile_path}")
                logging.info(f"Set permissions on profile directory: {profile_path}")
            except Exception as e:
                logging.warning(f"Failed to set permissions: {str(e)}")

            # Restore cookies if we had them
            if cookies_data:
                try:
                    with open(cookies_path, 'w') as f:
                        f.write(cookies_data)
                    logging.info("Restored cookies data")
                except Exception as e:
                    logging.warning(f"Failed to restore cookies: {str(e)}")

            return True
        else:
            # Directory doesn't exist, create it
            os.makedirs(profile_path, exist_ok=True)
            logging.info(f"Created new profile directory: {profile_path}")

            # Set proper permissions
            try:
                os.system(f"chmod -R 777 {profile_path}")
                logging.info(f"Set permissions on profile directory: {profile_path}")
            except Exception as e:
                logging.warning(f"Failed to set permissions: {str(e)}")

            return True
    except Exception as e:
        logging.error(f"Error resetting Chrome profile: {str(e)}")
        return False

async def setup_chrome_profile_with_websocket(
    websocket,
    profile_name: str,
    headless: bool = False,
    url: str = "https://accounts.google.com",
    use_stealth: bool = True,
    timeout: int = 3600,  # 1 hour timeout by default
    force_reset: bool = False  # Whether to force reset the profile
) -> Dict[str, Any]:
    """
    Set up a Chrome profile with WebSocket status updates.

    Args:
        websocket: The WebSocket connection to send status updates
        profile_name: Name of the Chrome profile
        headless: Whether to run in headless mode
        url: URL to open in the browser
        use_stealth: Whether to use stealth mode
        timeout: Maximum time to wait for setup completion (in seconds)
        force_reset: Whether to force reset the profile

    Returns:
        Dict with setup results
    """
    # Create profile directory if it doesn't exist
    profile_dir = os.path.join(DEFAULT_PROFILE_DIR, profile_name)

    # Send initial status update
    await websocket.send_json({
        "status": "processing",
        "message": "Đang chuẩn bị thiết lập Chrome profile...",
        "progress": 5,
        "completed": False,
        "response": None
    })

    # Always try to kill any existing Chrome processes first
    await websocket.send_json({
        "status": "processing",
        "message": "Đang kiểm tra và dọn dẹp các tiến trình Chrome...",
        "progress": 7,
        "completed": False,
        "response": None
    })

    # Kill all Chrome processes
    try:
        kill_commands = [
            "pkill -9 -f chrome",
            "pkill -9 -f chromium",
            "killall -9 chrome",
            "killall -9 chromium"
        ]

        for cmd in kill_commands:
            try:
                subprocess.run(cmd, shell=True, stderr=subprocess.DEVNULL)
                logging.info(f"Executed kill command: {cmd}")
            except Exception as e:
                logging.warning(f"Failed to execute kill command {cmd}: {str(e)}")
    except Exception as e:
        logging.warning(f"Error killing Chrome processes: {str(e)}")

    # Wait a moment for processes to be killed
    await asyncio.sleep(1)

    # Check if we need to reset the profile
    if force_reset:
        await websocket.send_json({
            "status": "processing",
            "message": "Đang xóa profile cũ (nếu có)...",
            "progress": 8,
            "completed": False,
            "response": None
        })

        reset_success = reset_chrome_profile(profile_dir)
        if not reset_success:
            await websocket.send_json({
                "status": "warning",
                "message": "Không thể xóa profile cũ. Đang thử phương pháp khác...",
                "progress": 9,
                "completed": False,
                "response": None
            })

            # Try a more aggressive approach
            try:
                # Use rm -rf directly
                subprocess.run(f"rm -rf {profile_dir}", shell=True)
                await asyncio.sleep(1)  # Wait for the command to complete
                os.makedirs(profile_dir, exist_ok=True)
                os.system(f"chmod -R 777 {profile_dir}")
                logging.info(f"Used rm -rf to reset profile directory: {profile_dir}")
            except Exception as e:
                logging.error(f"Failed aggressive profile reset: {str(e)}")
                await websocket.send_json({
                    "status": "error",
                    "message": "Không thể thiết lập profile. Vui lòng thử lại với tên profile khác.",
                    "progress": 0,
                    "completed": True,
                    "response": None
                })
                return {
                    "status": "error",
                    "message": "Không thể thiết lập profile",
                    "profile_path": profile_dir,
                    "profile_name": profile_name
                }
    else:
        # Try to unlock the profile if it exists
        if os.path.exists(profile_dir):
            await websocket.send_json({
                "status": "processing",
                "message": "Đang kiểm tra và mở khóa profile (nếu cần)...",
                "progress": 8,
                "completed": False,
                "response": None
            })

            unlock_success = unlock_chrome_profile(profile_dir)
            if not unlock_success:
                # If unlocking fails, try to reset the profile
                await websocket.send_json({
                    "status": "warning",
                    "message": "Không thể mở khóa profile. Đang thử xóa và tạo lại...",
                    "progress": 9,
                    "completed": False,
                    "response": None
                })

                reset_success = reset_chrome_profile(profile_dir)
                if not reset_success:
                    await websocket.send_json({
                        "status": "warning",
                        "message": "Đang thử phương pháp xóa profile mạnh hơn...",
                        "progress": 9,
                        "completed": False,
                        "response": None
                    })

                    # Try a more aggressive approach
                    try:
                        # Use rm -rf directly
                        subprocess.run(f"rm -rf {profile_dir}", shell=True)
                        await asyncio.sleep(1)  # Wait for the command to complete
                        os.makedirs(profile_dir, exist_ok=True)
                        os.system(f"chmod -R 777 {profile_dir}")
                        logging.info(f"Used rm -rf to reset profile directory: {profile_dir}")
                    except Exception as e:
                        logging.error(f"Failed aggressive profile reset: {str(e)}")
                        await websocket.send_json({
                            "status": "error",
                            "message": "Không thể thiết lập profile. Vui lòng thử lại với tên profile khác.",
                            "progress": 0,
                            "completed": True,
                            "response": None
                        })
                        return {
                            "status": "error",
                            "message": "Không thể thiết lập profile",
                            "profile_path": profile_dir,
                            "profile_name": profile_name
                        }

    # Create profile directory if it doesn't exist (after potential reset)
    os.makedirs(profile_dir, exist_ok=True)

    # Ensure proper permissions
    os.system(f"chmod -R 777 {profile_dir}")

    # Double-check that no lock files exist
    lock_files = [
        "SingletonLock",
        "SingletonCookie",
        "SingletonSocket",
        ".com.google.Chrome.yLU8m4",
        "lockfile",
        "Lock",
        "lock",
        ".parentlock"
    ]

    for lock_file_name in lock_files:
        lock_file = os.path.join(profile_dir, lock_file_name)
        if os.path.exists(lock_file):
            try:
                os.remove(lock_file)
                logging.info(f"Removed lock file during final check: {lock_file}")
            except Exception as e:
                logging.warning(f"Failed to remove lock file during final check {lock_file}: {str(e)}")

    # Send status update
    await websocket.send_json({
        "status": "processing",
        "message": "Đang khởi tạo trình duyệt...",
        "progress": 10,
        "completed": False,
        "response": None
    })

    # Get stealth configuration
    stealth_config = get_stealth_config() if use_stealth else {}
    browser_args = get_browser_args()
    stealth_script = get_stealth_script() if use_stealth else ""

    # Setup X server for VNC access
    await websocket.send_json({
        "status": "processing",
        "message": "Đang thiết lập X server và VNC...",
        "progress": 20,
        "completed": False,
        "response": None
    })

    # Send VNC connection details
    await websocket.send_json({
        "status": "processing",
        "message": "X server và VNC đã sẵn sàng. Vui lòng sử dụng VNC để truy cập trình duyệt.",
        "progress": 30,
        "completed": False,
        "response": None,
        "vnc_details": {
            "host": "localhost",
            "port": 6080,
            "password": "playwright",
            "url": "http://localhost:6080/vnc.html?autoconnect=true&resize=scale&reconnect=true"
        }
    })

    try:
        async with async_playwright() as p:
            # Launch browser with persistent context
            await websocket.send_json({
                "status": "processing",
                "message": "Đang mở trình duyệt...",
                "progress": 40,
                "completed": False,
                "response": None
            })

            # Generate a unique session ID to avoid conflicts
            session_id = int(time.time() * 1000)

            # Add unique arguments to avoid profile conflicts
            unique_args = browser_args.copy()
            unique_args.extend([
                f"--profile-directory=Profile_{session_id}",
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-sync",
                "--disable-extensions"
            ])

            # Log the full command for debugging
            logging.info(f"Launching browser with profile directory: {profile_dir}")
            logging.info(f"Session ID: {session_id}")

            browser_context = await p.chromium.launch_persistent_context(
                user_data_dir=profile_dir,
                headless=headless,
                args=unique_args,
                viewport=stealth_config.get("viewport", {"width": 1280, "height": 720}),
                device_scale_factor=stealth_config.get("device_scale_factor", 1),
                locale=stealth_config.get("locale", "en-US"),
                timezone_id=stealth_config.get("timezone_id", "America/New_York"),
                color_scheme=stealth_config.get("color_scheme", "light"),
                user_agent=stealth_config.get("user_agent", None),
                ignore_https_errors=True
            )

            # Add stealth script if needed
            if use_stealth:
                await browser_context.add_init_script(stealth_script)

            # Open a new page
            page = await browser_context.new_page()

            # Add extra HTTP headers if using stealth mode
            if use_stealth and "extra_http_headers" in stealth_config:
                await page.set_extra_http_headers(stealth_config["extra_http_headers"])

            # Navigate to the URL
            await websocket.send_json({
                "status": "processing",
                "message": f"Đang mở trang {url}...",
                "progress": 50,
                "completed": False,
                "response": None
            })

            await page.goto(url)

            # Send instructions to the user
            await websocket.send_json({
                "status": "processing",
                "message": "Vui lòng đăng nhập vào tài khoản Google của bạn qua giao diện VNC.",
                "progress": 60,
                "completed": False,
                "response": None,
                "instructions": [
                    "1. Nhập email Google của bạn và nhấn 'Next'",
                    "2. Nhập mật khẩu và nhấn 'Next'",
                    "3. Nếu được yêu cầu xác minh, hãy làm theo hướng dẫn",
                    "4. Sau khi đăng nhập thành công, nhấn nút 'Đã xong' trên giao diện"
                ]
            })

            # Wait for user to complete the login process
            start_time = time.time()
            login_detected = False
            warning_sent = False

            try:
                while True:
                    # Check if timeout has been reached but only send warning once
                    if time.time() - start_time > timeout and not warning_sent:
                        warning_sent = True
                        await websocket.send_json({
                            "status": "warning",
                            "message": "Đã vượt quá thời gian chờ tối đa. Vui lòng hoàn thành thiết lập hoặc nhấn 'Đã xong' khi đã đăng nhập xong.",
                            "progress": 70,
                            "completed": False,
                            "response": None
                        })

                    # Wait a bit before checking again
                    await asyncio.sleep(2)

                    try:
                        # Check if we're on a page that indicates successful login
                        current_url = page.url
                        logging.info(f"Current URL: {current_url}")

                        # Check for successful login indicators
                        if (not login_detected and
                            ("myaccount.google.com" in current_url or
                             "accounts.google.com/signin/v2/challenge/selection" in current_url or
                             "accounts.google.com/signin/v2/challenge/ipp" in current_url or
                             "accounts.google.com/signin/v2/challenge/selection" in current_url or
                             "accounts.google.com/ServiceLogin/signinchooser" in current_url or
                             "mail.google.com" in current_url)):

                            login_detected = True
                            await websocket.send_json({
                                "status": "success",
                                "message": "Đăng nhập Google thành công! Vui lòng nhấn 'Đã xong' để lưu profile.",
                                "progress": 90,
                                "completed": False,
                                "response": None
                            })
                    except Exception as e:
                        logging.warning(f"Error checking page URL: {str(e)}")
                        # Continue waiting even if there's an error checking the URL
                        pass

                    # This is an infinite loop that will be broken when the user
                    # explicitly completes the setup via the frontend
            except asyncio.CancelledError:
                # This will be triggered when the setup is completed
                logging.info("Profile setup process was cancelled (likely completed)")

            # Save cookies
            cookies = await browser_context.cookies()
            cookies_path = os.path.join(profile_dir, "cookies.json")
            with open(cookies_path, "w") as f:
                json.dump(cookies, f)

            # Close the browser
            await browser_context.close()

            return {
                "status": "success",
                "message": "Thiết lập Chrome profile thành công",
                "profile_path": profile_dir,
                "profile_name": profile_name
            }

    except Exception as e:
        logging.error(f"Lỗi khi thiết lập Chrome profile: {str(e)}")
        await websocket.send_json({
            "status": "error",
            "message": f"Lỗi khi thiết lập Chrome profile: {str(e)}",
            "progress": 0,
            "completed": True,
            "response": None
        })
        return {
            "status": "error",
            "message": f"Lỗi khi thiết lập Chrome profile: {str(e)}",
            "profile_path": profile_dir,
            "profile_name": profile_name
        }
