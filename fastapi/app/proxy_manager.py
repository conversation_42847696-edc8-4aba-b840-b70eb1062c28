"""
Modu<PERSON> qu<PERSON><PERSON> lý proxy cho ứng dụng.
"""

class ProxyManager:
    """
    <PERSON><PERSON><PERSON> quản lý danh sách proxy và cung cấp các phương thức để thêm, x<PERSON><PERSON>, cập nhật proxy.
    """
    
    def __init__(self):
        """
        Khởi tạo danh sách proxy trống.
        """
        self.proxies = []
    
    def add_proxy(self, proxy_id, proxy_name, username, password, host, port, country, status, is_active, valid_from, valid_to):
        """
        Thêm một proxy mới vào danh sách.
        
        Args:
            proxy_id (str): ID của proxy
            proxy_name (str): <PERSON><PERSON><PERSON> của proxy
            username (str): Tên đăng nhập proxy
            password (str): Mật khẩu proxy
            host (str): Địa chỉ host của proxy
            port (str): Cổng của proxy
            country (str): Mã quốc gia của proxy
            status (str): Trạng thái của proxy
            is_active (str): Proxy c<PERSON> đang hoạt động không
            valid_from (str): <PERSON><PERSON><PERSON><PERSON> gian bắt đầu hiệu lực
            valid_to (str): Thời gian kết thúc hiệu lực
        
        Returns:
            dict: Thông tin proxy đã thêm
        """
        proxy = {
            'id': proxy_id,
            'name': proxy_name,
            'username': username,
            'password': password,
            'host': host,
            'port': port,
            'country': country,
            'status': status,
            'is_active': is_active,
            'valid_from': valid_from,
            'valid_to': valid_to
        }
        
        self.proxies.append(proxy)
        return proxy
    
    def get_proxy_by_id(self, proxy_id):
        """
        Lấy thông tin proxy theo ID.
        
        Args:
            proxy_id (str): ID của proxy cần tìm
        
        Returns:
            dict: Thông tin proxy nếu tìm thấy, None nếu không tìm thấy
        """
        for proxy in self.proxies:
            if proxy['id'] == proxy_id:
                return proxy
        return None
    
    def get_proxy_by_country(self, country_code):
        """
        Lấy danh sách proxy theo mã quốc gia.
        
        Args:
            country_code (str): Mã quốc gia cần tìm
        
        Returns:
            list: Danh sách proxy thuộc quốc gia đó
        """
        return [proxy for proxy in self.proxies if proxy['country'] == country_code]
    
    def get_active_proxies(self):
        """
        Lấy danh sách proxy đang hoạt động.
        
        Returns:
            list: Danh sách proxy đang hoạt động
        """
        return [proxy for proxy in self.proxies if proxy['is_active'] == '1']
    
    def update_proxy(self, proxy_id, **kwargs):
        """
        Cập nhật thông tin proxy.
        
        Args:
            proxy_id (str): ID của proxy cần cập nhật
            **kwargs: Các thông tin cần cập nhật
        
        Returns:
            dict: Thông tin proxy sau khi cập nhật, None nếu không tìm thấy
        """
        proxy = self.get_proxy_by_id(proxy_id)
        if proxy:
            for key, value in kwargs.items():
                if key in proxy:
                    proxy[key] = value
            return proxy
        return None
    
    def delete_proxy(self, proxy_id):
        """
        Xóa proxy khỏi danh sách.
        
        Args:
            proxy_id (str): ID của proxy cần xóa
        
        Returns:
            bool: True nếu xóa thành công, False nếu không tìm thấy
        """
        proxy = self.get_proxy_by_id(proxy_id)
        if proxy:
            self.proxies.remove(proxy)
            return True
        return False
    
    def get_all_proxies(self):
        """
        Lấy tất cả proxy.
        
        Returns:
            list: Danh sách tất cả proxy
        """
        return self.proxies

# Khởi tạo proxy manager
proxy_manager = ProxyManager()

# Thêm proxy mặc định
proxy_manager.add_proxy(
    '1', 'd-16402519052', 'etvwhglf', 'wyfqyl21f1q6', 
    '161.123.209.229', '6729', 'JP', '1', '1', 
    '2025-05-14 03:06:15', '2025-05-14 03:13:33'
)

def get_proxy_config(proxy_id=None, country=None):
    """
    Lấy cấu hình proxy để sử dụng với Playwright.
    
    Args:
        proxy_id (str, optional): ID của proxy cần lấy. Mặc định là None.
        country (str, optional): Mã quốc gia của proxy cần lấy. Mặc định là None.
    
    Returns:
        dict: Cấu hình proxy cho Playwright
    """
    proxy = None
    
    if proxy_id:
        proxy = proxy_manager.get_proxy_by_id(proxy_id)
    elif country:
        proxies = proxy_manager.get_proxy_by_country(country)
        if proxies:
            proxy = proxies[0]  # Lấy proxy đầu tiên trong danh sách
    else:
        # Nếu không chỉ định, lấy proxy đầu tiên trong danh sách
        proxies = proxy_manager.get_active_proxies()
        if proxies:
            proxy = proxies[0]
    
    if proxy:
        return {
            'host': proxy['host'],
            'port': proxy['port'],
            'username': proxy['username'],
            'password': proxy['password']
        }
    
    return None
