"""
WebSocket Manager for Realtime Monitoring

This module manages WebSocket connections for realtime monitoring of Manus Chat.
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, List, Set
import uuid

from fastapi import WebSocket, WebSocketDisconnect

logger = logging.getLogger("realtime_websocket_manager")

class RealtimeConnectionManager:
    """
    Manages active WebSocket connections for realtime monitoring.
    """
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.monitoring_sessions: Dict[str, bool] = {}
        
    async def connect(self, websocket: WebSocket, session_id: str) -> None:
        """
        Connect a new WebSocket client with the given session ID.
        
        Args:
            websocket: The WebSocket connection
            session_id: Unique session identifier
        """
        await websocket.accept()
        self.active_connections[session_id] = websocket
        self.monitoring_sessions[session_id] = True
        logger.info(f"WebSocket connected for session {session_id}")
        
    def disconnect(self, session_id: str) -> None:
        """
        Disconnect a WebSocket client.
        
        Args:
            session_id: Session ID to disconnect
        """
        if session_id in self.active_connections:
            del self.active_connections[session_id]
        
        if session_id in self.monitoring_sessions:
            self.monitoring_sessions[session_id] = False
            
        logger.info(f"WebSocket disconnected for session {session_id}")
        
    async def send_message(self, session_id: str, message: str) -> bool:
        """
        Send a message to a specific client.
        
        Args:
            session_id: Target session ID
            message: Message to send (JSON string)
            
        Returns:
            True if message was sent successfully, False otherwise
        """
        if session_id in self.active_connections:
            try:
                websocket = self.active_connections[session_id]
                await websocket.send_text(message)
                return True
            except Exception as e:
                logger.error(f"Error sending message to session {session_id}: {str(e)}")
                self.disconnect(session_id)
                return False
        return False
    
    async def broadcast(self, message: str) -> None:
        """
        Broadcast a message to all connected clients.
        
        Args:
            message: Message to broadcast (JSON string)
        """
        disconnected_sessions = []
        
        for session_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting to session {session_id}: {str(e)}")
                disconnected_sessions.append(session_id)
                
        # Clean up disconnected sessions
        for session_id in disconnected_sessions:
            self.disconnect(session_id)
    
    def is_active(self, session_id: str) -> bool:
        """
        Check if a session is active.
        
        Args:
            session_id: Session ID to check
            
        Returns:
            True if session is active, False otherwise
        """
        return session_id in self.active_connections and self.monitoring_sessions.get(session_id, False)
    
    def stop_monitoring(self, session_id: str) -> None:
        """
        Stop monitoring for a session.
        
        Args:
            session_id: Session ID to stop monitoring
        """
        if session_id in self.monitoring_sessions:
            self.monitoring_sessions[session_id] = False
            logger.info(f"Monitoring stopped for session {session_id}")
    
    def get_active_sessions(self) -> List[str]:
        """
        Get list of all active session IDs.
        
        Returns:
            List of active session IDs
        """
        return [
            session_id for session_id, active in self.monitoring_sessions.items() 
            if active and session_id in self.active_connections
        ]
    
    def generate_session_id(self) -> str:
        """
        Generate a unique session ID.
        
        Returns:
            Unique session ID string
        """
        return str(uuid.uuid4())

# Singleton instance
realtime_manager = RealtimeConnectionManager()