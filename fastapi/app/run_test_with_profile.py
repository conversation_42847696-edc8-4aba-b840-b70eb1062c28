#!/usr/bin/env python3
import os
import sys
import argparse
import subprocess
from pathlib import Path

# Thư mục mặc định cho Chrome profile
DEFAULT_PROFILE_DIR = os.path.join(os.path.expanduser("~"), "chrome_profiles")

def run_test_with_profile(profile_name, username, password, chat_content, wait_time=30, headless=False, no_record=False, no_cookies=False, no_stealth=False, no_google=False):
    """
    Chạy test_playwright.py với Chrome profile đã thiết lập.
    
    Args:
        profile_name (str): <PERSON><PERSON><PERSON> của profile
        username (str): <PERSON><PERSON> đăng nhập
        password (str): <PERSON><PERSON><PERSON> khẩu đăng nhập
        chat_content (str): Nội dung tin nhắn muốn gửi
        wait_time (int): Thời gian chờ sau khi hoàn thành
        headless (bool): Chạy ở chế độ headless hay không
        no_record (bool): <PERSON>hông ghi video quá trình test
        no_cookies (bool): <PERSON><PERSON>ông sử dụng cookies đã lưu
        no_stealth (bool): <PERSON><PERSON><PERSON>ng sử dụng chế độ stealth
        no_google (bool): Không sử dụng đăng nhập bằng Google
    """
    # Tạo đường dẫn đến profile
    profile_dir = os.path.join(DEFAULT_PROFILE_DIR, profile_name)
    
    # Kiểm tra xem profile có tồn tại không
    if not os.path.exists(profile_dir):
        print(f"Lỗi: Profile '{profile_name}' không tồn tại tại {profile_dir}")
        print("Vui lòng chạy setup_chrome_profile.py trước để thiết lập profile.")
        return
    
    # Tạo lệnh để chạy test_playwright.py
    cmd = [
        "python",
        "test_playwright.py",
        f"--username={username}",
        f"--password={password}",
        f"--chat={chat_content}",
        f"--wait={wait_time}",
        f"--chrome-profile={profile_dir}"
    ]
    
    # Thêm các tùy chọn khác nếu được chỉ định
    if headless:
        cmd.append("--headless")
    if no_record:
        cmd.append("--no-record")
    if no_cookies:
        cmd.append("--no-cookies")
    if no_stealth:
        cmd.append("--no-stealth")
    if no_google:
        cmd.append("--no-google")
    
    # In lệnh sẽ chạy
    print("Chạy lệnh:", " ".join(cmd))
    
    # Chạy lệnh
    subprocess.run(cmd)

def parse_arguments():
    parser = argparse.ArgumentParser(description="Chạy test với Chrome profile đã thiết lập")
    
    parser.add_argument("--profile-name", default="my_profile",
                        help="Tên của profile (mặc định: 'default')")
    
    parser.add_argument("--profile-dir", default=DEFAULT_PROFILE_DIR,
                        help=f"Thư mục chứa các profile (mặc định: {DEFAULT_PROFILE_DIR})")
    
    parser.add_argument("--username", default="<EMAIL>",
                        help="Email đăng nhập")
    
    parser.add_argument("--password", default="Matkhau@1234",
                        help="Mật khẩu đăng nhập")
    
    parser.add_argument("--chat", default="Hello từ Playwright!",
                        help="Nội dung tin nhắn muốn gửi")
    
    parser.add_argument("--wait", type=int, default=30,
                        help="Thời gian chờ (giây) sau khi thực hiện xong các thao tác")
    
    parser.add_argument("--headless", action="store_true",
                        help="Chạy ở chế độ headless (không hiển thị giao diện)")
    
    parser.add_argument("--no-record", action="store_true",
                        help="Không ghi video quá trình test")
    
    parser.add_argument("--no-cookies", action="store_true",
                        help="Không sử dụng cookies đã lưu")
    
    parser.add_argument("--no-stealth", action="store_true",
                        help="Không sử dụng chế độ stealth để tránh phát hiện bot")
    
    parser.add_argument("--no-google", action="store_true",
                        help="Không sử dụng đăng nhập bằng Google (sử dụng đăng nhập trực tiếp)")
    
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_arguments()
    
    # Cập nhật thư mục profile mặc định nếu được chỉ định
    if args.profile_dir != DEFAULT_PROFILE_DIR:
        DEFAULT_PROFILE_DIR = args.profile_dir
    
    # Chạy test với profile đã chỉ định
    run_test_with_profile(
        args.profile_name,
        args.username,
        args.password,
        args.chat,
        args.wait,
        args.headless,
        args.no_record,
        args.no_cookies,
        args.no_stealth,
        args.no_google
    )
