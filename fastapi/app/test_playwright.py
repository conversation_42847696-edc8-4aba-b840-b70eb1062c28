import asyncio
import argparse
import os
import subprocess
import json
import random
from pathlib import Path
from playwright.async_api import async_playwright

# Thư mục mặc định cho Chrome profile
DEFAULT_PROFILE_DIR = os.path.join(os.path.expanduser("~"), "chrome_profiles")

# Import hàm từ playwright_utils nếu cần
try:
    from .playwright_utils import get_stealth_config
    from .proxy_manager import get_proxy_config
except ImportError:
    try:
        from playwright_utils import get_stealth_config
        from proxy_manager import get_proxy_config
    except ImportError:
        print("Không thể import hàm từ playwright_utils. Vui lòng kiểm tra đường dẫn.")

# Thông tin mặc định
DEFAULT_CHAT_CONTENT = "Hello từ Playwright!"
DEFAULT_WAIT_TIME = 30

def check_chrome_profile(profile_path):
    """Kiểm tra Chrome profile có tồn tại và hợp lệ không."""
    if not profile_path:
        return False

    profile = Path(profile_path)
    if not profile.exists():
        print(f"Profile không tồn tại: {profile_path}")
        return False

    # Kiểm tra file Preferences trong thư mục Default
    preference_file = profile / "Default" / "Preferences"
    if not preference_file.exists():
        print(f"File Preferences không tồn tại trong profile: {preference_file}")
        return False

    print(f"Chrome profile hợp lệ tại: {profile_path}")
    return True

async def is_logged_in(page):
    """Kiểm tra xem đã đăng nhập vào Minus hay chưa."""
    try:
        # Kiểm tra URL hiện tại
        current_url = page.url
        if '/app' in current_url or 'dashboard' in current_url:
            print("Đã đăng nhập thành công (kiểm tra bằng URL)!")
            return True

        # Kiểm tra có phải trang đăng nhập không
        if 'login' in current_url or 'signin' in current_url:
            print("Đang ở trang đăng nhập, chưa đăng nhập thành công!")
            return False

        # Kiểm tra các element trên trang
        try:
            # Kiểm tra xem có textarea nhập tin nhắn không
            has_chat_input = await page.locator('textarea').or_(page.locator('[contenteditable="true"]')).count() > 0
            if has_chat_input:
                print("Phát hiện trường nhập chat, đã đăng nhập!")
                return True

            # Kiểm tra nút Sign in
            sign_in_button = await page.locator('button:has-text("Sign in")').count() > 0
            if sign_in_button:
                print("Phát hiện nút Sign in, chưa đăng nhập!")
                return False

        except Exception as e:
            print(f"Lỗi khi kiểm tra các element trên trang: {str(e)}")

        # Mặc định
        return False
    except Exception as e:
        print(f"Lỗi khi kiểm tra trạng thái đăng nhập: {str(e)}")
        return False

async def try_google_login(page):
    """Thử đăng nhập bằng Google bằng cách nhấp vào nút đăng nhập Google."""
    try:
        print("Thử đăng nhập bằng Google...")

        # Tìm nút đăng nhập bằng Google với nhiều selector khác nhau
        google_button = page.locator('button:has-text("Sign in with Google")').or_(
            page.locator('button:has-text("Continue with Google")')
        ).or_(
            page.locator('button[data-provider="google"]')
        ).or_(
            page.locator('div.google-login-button')
        ).first

        # Kiểm tra xem nút Google có tồn tại không
        button_count = await google_button.count()
        if button_count == 0:
            print("Không tìm thấy nút đăng nhập bằng Google!")
            return False

        # Click nút đăng nhập Google
        print("Tìm thấy nút đăng nhập Google, tiến hành nhấp...")
        await google_button.click(timeout=10000)
        print("Đã nhấp vào nút đăng nhập bằng Google")

        # Đợi trang tải xong
        await page.wait_for_load_state('networkidle', timeout=30000)
        print("Trang đã tải xong sau khi nhấp nút Google")

        # Chờ thêm thời gian để quá trình đăng nhập hoàn tất
        await asyncio.sleep(5)

        return True

    except Exception as e:
        print(f"Lỗi khi thử đăng nhập bằng Google: {str(e)}")
        return False

async def setup_xserver():
    """Kiểm tra và thiết lập X server nếu cần thiết."""
    if not os.environ.get('DISPLAY'):
        if os.path.exists('/.dockerenv'):
            os.environ["DISPLAY"] = ":1"
            print("Đang chạy trong Docker, thiết lập DISPLAY=:1")
        else:
            print("Không phát hiện biến DISPLAY. Thiết lập mặc định DISPLAY=:0")
            os.environ["DISPLAY"] = ":0"

    # Kiểm tra nếu X server đang chạy
    try:
        result = subprocess.run(["xdpyinfo"], capture_output=True, text=True)
        print("X server đang hoạt động")
    except Exception as e:
        print(f"Lỗi khi kiểm tra X server: {e}")
        print("Cố gắng khởi động X server...")
        try:
            subprocess.run(["Xvfb", ":1", "-screen", "0", "1280x720x24", "-ac"], start_new_session=True)
            os.environ["DISPLAY"] = ":1"
            print("Đã khởi động Xvfb trên DISPLAY=:1")
        except Exception as e2:
            print(f"Không thể khởi động Xvfb: {e2}")

async def test_with_gui(chat_content, wait_time=DEFAULT_WAIT_TIME, headless=None, proxy_config=None, use_stealth=True, chrome_profile_path=None):
    """Test trực tiếp với Playwright sử dụng Chrome profile."""
    # Kiểm tra Chrome profile
    if chrome_profile_path and not check_chrome_profile(chrome_profile_path):
        print(f"CẢNH BÁO: Chrome profile không hợp lệ hoặc không tồn tại: {chrome_profile_path}")
        print("Tiếp tục với trình duyệt thông thường...")
        chrome_profile_path = None

    # Thiết lập X server
    await setup_xserver()

    # Sử dụng biến môi trường để kiểm soát chế độ headless
    if headless is None:
        headless_env = os.environ.get("PLAYWRIGHT_HEADLESS", "false").lower()
        headless = headless_env == "true"

    print(f"Khởi động trình duyệt với {'headless mode' if headless else 'GUI mode'}")

    browser = None
    context = None

    try:
        async with async_playwright() as p:
            # Tham số cần thiết cho browser
            browser_args = [
                '--disable-gpu',
                '--disable-dev-shm-usage',
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=IsolateOrigins,site-per-process',
                '--ignore-certificate-errors',
                '--disable-extensions',
                '--disable-infobars',
                '--start-maximized',
                '--window-size=1280,720',
                '--disable-automation',
                '--disable-blink-features',
                '--disable-sync',
                '--disable-translate',
                '--disable-notifications',
                '--mute-audio',
                '--no-first-run',
                '--no-default-browser-check',
            ]

            # Thiết lập context options
            context_options = {}

            # Thiết lập proxy nếu có
            proxy = None
            if proxy_config:
                proxy = {
                    "server": f"http://{proxy_config.get('host')}:{proxy_config.get('port')}"
                }
                if proxy_config.get('username') and proxy_config.get('password'):
                    proxy["username"] = proxy_config.get('username')
                    proxy["password"] = proxy_config.get('password')
                print(f"Sử dụng proxy: {proxy_config.get('host')}:{proxy_config.get('port')}")

            # Slow_mo để làm chậm các thao tác trong mode headless
            slow_mo = random.randint(50, 150) if headless else 0

            # Áp dụng cấu hình stealth mode
            if use_stealth:
                stealth_config = get_stealth_config()
                context_options.update({
                    "viewport": stealth_config["viewport"],
                    "device_scale_factor": stealth_config["device_scale_factor"],
                    "locale": stealth_config["locale"],
                    "timezone_id": stealth_config["timezone_id"],
                    "user_agent": stealth_config["user_agent"]
                })

            # Khởi động browser với Chrome profile hoặc mặc định
            if chrome_profile_path:
                print(f"Sử dụng Chrome profile tại: {chrome_profile_path}")
                context = await p.chromium.launch_persistent_context(
                    user_data_dir=chrome_profile_path,
                    headless=headless,
                    args=browser_args,
                    proxy=proxy,
                    slow_mo=slow_mo,
                    **context_options
                )
                browser = context.browser
            else:
                browser = await p.chromium.launch(
                    headless=headless,
                    args=browser_args,
                    proxy=proxy,
                    slow_mo=slow_mo,
                    chromium_sandbox=False
                )
                context = await browser.new_context(**context_options)

            # Thêm JavaScript stealth
            if use_stealth:
                await context.add_init_script("""
                // Ẩn thuộc tính webdriver
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => false,
                });

                // Tạo plugins và mimeTypes giả
                const mockPlugins = [{ description: "PDF Viewer", filename: "internal-pdf-viewer", name: "Chrome PDF Viewer" }];
                Object.defineProperty(navigator, 'plugins', {
                    get: () => Object.defineProperties({}, {
                        length: { value: mockPlugins.length },
                        item: { value: (index) => mockPlugins[index] },
                        namedItem: { value: (name) => mockPlugins.find(plugin => plugin.name === name) }
                    })
                });

                // Thiết lập giá trị ngẫu nhiên cho thuộc tính phần cứng
                const getRandomInt = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;
                Object.defineProperty(navigator, 'hardwareConcurrency', { get: () => getRandomInt(4, 16) });
                Object.defineProperty(navigator, 'deviceMemory', { get: () => getRandomInt(4, 32) });
                """)

            # Mở trang và đăng nhập
            page = await context.new_page()
            print("Mở trang Minus...")
            await page.goto("https://manus.im/login?type=signIn")
            await page.wait_for_load_state('networkidle')

            # Kiểm tra trạng thái đăng nhập
            is_logged = await is_logged_in(page)

            if is_logged:
                print("Đã đăng nhập thành công bằng Chrome profile!")
            else:
                print("Chưa đăng nhập, thử đăng nhập tự động bằng Google...")

                # Thử đăng nhập bằng Google
                google_login_attempted = await try_google_login(page)

                if google_login_attempted:
                    print("Đã thử đăng nhập bằng Google, kiểm tra lại trạng thái...")
                    await asyncio.sleep(5)

                    # Kiểm tra lại trạng thái đăng nhập
                    is_logged = await is_logged_in(page)
                    if is_logged:
                        print("Đăng nhập thành công sau khi thử đăng nhập bằng Google!")
                    else:
                        print("Vẫn chưa đăng nhập sau khi thử đăng nhập bằng Google.")
                        print("Profile cần được cài đặt lại.")
                        print("Vui lòng setup lại Chrome profile và thử lại.")
                        # Đóng browser và context
                        await cleanup_browser(context, browser, chrome_profile_path)
                        return False
                else:
                    print("Không thể thử đăng nhập bằng Google.")
                    print("Profile cần được cài đặt lại.")
                    print("Vui lòng setup lại Chrome profile và thử lại.")
                    # Đóng browser và context
                    await cleanup_browser(context, browser, chrome_profile_path)
                    return False

            # Đã đăng nhập thành công, gửi tin nhắn
            try:
                # Tìm và gửi tin nhắn
                success = await send_message(page, chat_content)
                if not success:
                    print("Không thể gửi tin nhắn")
            except Exception as e:
                print(f"Lỗi khi gửi tin nhắn: {str(e)}")

            print(f"Giữ trình duyệt mở trong {wait_time} giây để quan sát...")
            await asyncio.sleep(wait_time)

            # Đóng browser và context
            await cleanup_browser(context, browser, chrome_profile_path)
            print("Đã đóng trình duyệt.")
            return True

    except Exception as e:
        print(f"Lỗi khi chạy test: {str(e)}")
        # Đóng browser và context nếu có lỗi
        try:
            await cleanup_browser(context, browser, chrome_profile_path)
        except Exception as close_error:
            print(f"Lỗi khi đóng trình duyệt: {str(close_error)}")
        return False

async def send_message(page, chat_content, chat_history=None):
    """Gửi tin nhắn vào Manus.

    Args:
        page: Trang Playwright đang mở
        chat_content: Nội dung tin nhắn cần gửi
        chat_history: Lịch sử cuộc trò chuyện (tùy chọn)
    """
    # Tìm trường nhập chat theo nhiều selector khác nhau
    print("Tìm trường nhập chat...")

    # Danh sách các selector phổ biến cho trường nhập chat
    input_selectors = [
        # Selector cụ thể cho Manus
        'textarea[placeholder*="Give Manus a task"]',
        'textarea[placeholder*="Ask Manus"]',
        'textarea[placeholder*="Message Manus"]',
        'textarea[placeholder*="Type your message"]',

        # Selector chung cho các ứng dụng chat
        'textarea[rows="2"]',
        'div.overflow-y-auto textarea',
        '[contenteditable="true"]',
        'div[role="textbox"]',
        'textarea.chat-input',
        'textarea.message-input',
        'textarea.input-box',

        # Selector dự phòng
        'form textarea',
        'textarea'
    ]

    # Tạo locator kết hợp tất cả các selector
    combined_selector = input_selectors[0]
    for selector in input_selectors[1:]:
        combined_selector = f"{combined_selector}, {selector}"

    chat_input = page.locator(combined_selector).first

    if await chat_input.count() > 0:
        try:
            # Click để focus và nhập nội dung
            await chat_input.click()
            await asyncio.sleep(0.5)  # Đợi một chút để đảm bảo focus

            # Xóa nội dung hiện tại nếu có
            await chat_input.fill("")
            await asyncio.sleep(0.2)

            # Nhập nội dung mới
            await chat_input.fill(chat_content)
            print(f"Đã nhập nội dung chat: {chat_content}")

            # Tìm nút gửi tin nhắn
            send_button = page.locator('button[type="submit"], button.send-button, button:has-text("Send"), button:has-text("Gửi"), button:has([aria-label="send"]), button:has(svg[class*="send"])').first

            if await send_button.count() > 0:
                # Gửi tin nhắn bằng nút
                print("Gửi tin nhắn bằng cách nhấn nút gửi...")
                await send_button.click()
                print("Đã gửi tin nhắn bằng nút")
            else:
                # Gửi tin nhắn bằng Enter
                print("Không tìm thấy nút gửi, gửi tin nhắn bằng cách nhấn Enter...")
                await chat_input.press("Enter")
                print("Đã gửi tin nhắn bằng Enter")

            # Chờ một chút để đảm bảo tin nhắn được gửi
            await asyncio.sleep(2)

            # Kiểm tra xem tin nhắn đã được gửi chưa
            try:
                # Tìm tin nhắn của người dùng trong giao diện chat
                user_message = page.locator('div:has-text("' + chat_content[:30] + '")').first
                if await user_message.count() > 0:
                    print("Đã xác nhận tin nhắn xuất hiện trong giao diện chat")
                else:
                    print("Không thể xác nhận tin nhắn đã xuất hiện, nhưng đã thực hiện thao tác gửi")
            except Exception as e:
                print(f"Lỗi khi kiểm tra tin nhắn đã gửi: {str(e)}")

            return True

        except Exception as e:
            print(f"Lỗi khi gửi tin nhắn: {str(e)}")

            # Thử phương pháp khác nếu có lỗi
            try:
                # Thử lại với cách nhập trực tiếp
                await page.keyboard.type(chat_content)
                await page.keyboard.press("Enter")
                print("Đã thử gửi tin nhắn bằng phương pháp nhập trực tiếp")
                await asyncio.sleep(2)
                return True
            except Exception as e2:
                print(f"Lỗi khi thử phương pháp nhập trực tiếp: {str(e2)}")
                return False
    else:
        print("Không tìm thấy trường nhập chat, thử tìm kiếm các phần tử tương tác khác...")

        # Thử tìm các phần tử tương tác khác
        try:
            # Tìm bất kỳ phần tử nào có thể nhập liệu
            input_elements = await page.query_selector_all('input[type="text"], [contenteditable], [role="textbox"]')

            if input_elements and len(input_elements) > 0:
                for input_el in input_elements:
                    try:
                        await input_el.click()
                        await input_el.fill(chat_content)
                        await input_el.press("Enter")
                        print("Đã gửi tin nhắn qua phần tử tương tác tìm thấy")
                        await asyncio.sleep(2)
                        return True
                    except:
                        continue

            # Nếu không tìm thấy phần tử nào, thử nhấn vào vùng nhập liệu tiềm năng
            await page.mouse.click(page.viewport_size["width"] // 2, page.viewport_size["height"] - 100)
            await page.keyboard.type(chat_content)
            await page.keyboard.press("Enter")
            print("Đã thử gửi tin nhắn bằng cách nhấp vào vùng nhập liệu tiềm năng")
            await asyncio.sleep(2)
            return True

        except Exception as e:
            print(f"Không thể tìm thấy bất kỳ phương thức nào để gửi tin nhắn: {str(e)}")
            return False

async def cleanup_browser(context, browser, chrome_profile_path):
    """Đóng browser và context an toàn."""
    try:
        if context:
            try:
                await context.close()
                print("Đã đóng context thành công")
            except Exception as context_error:
                print(f"Lỗi khi đóng context: {str(context_error)}")

        # Chỉ đóng browser nếu không dùng persistent context
        if browser and not chrome_profile_path:
            try:
                await browser.close()
                print("Đã đóng browser thành công")
            except Exception as browser_error:
                print(f"Lỗi khi đóng browser: {str(browser_error)}")
    except Exception as e:
        print(f"Lỗi không xác định khi dọn dẹp browser: {str(e)}")

def parse_arguments():
    parser = argparse.ArgumentParser(description="Test Playwright với Minus")

    parser.add_argument("--chat", default=DEFAULT_CHAT_CONTENT,
                        help="Nội dung tin nhắn muốn gửi")

    parser.add_argument("--wait", type=int, default=DEFAULT_WAIT_TIME,
                        help="Thời gian chờ (giây) sau khi thực hiện xong")

    parser.add_argument("--headless", action="store_true",
                        help="Chạy ở chế độ headless (không hiển thị giao diện)")

    parser.add_argument("--proxy-id", default="1",
                        help="ID của proxy muốn sử dụng")

    parser.add_argument("--proxy-country", default=None,
                        help="Mã quốc gia của proxy muốn sử dụng")

    parser.add_argument("--no-stealth", action="store_true",
                        help="Không sử dụng chế độ stealth")

    parser.add_argument("--chrome-profile", default=None,
                        help="Đường dẫn đến thư mục profile Chrome")

    parser.add_argument("--profile-name", default=None,
                        help="Tên profile Chrome (trong thư mục mặc định)")

    parser.add_argument("--profile-dir", default=DEFAULT_PROFILE_DIR,
                        help=f"Thư mục chứa các profile")

    return parser.parse_args()

if __name__ == "__main__":
    args = parse_arguments()

    # Xử lý đường dẫn Chrome profile
    chrome_profile_path = args.chrome_profile

    # Tạo đường dẫn từ profile_name nếu được chỉ định
    if args.profile_name and not chrome_profile_path:
        profile_dir = args.profile_dir if args.profile_dir else DEFAULT_PROFILE_DIR
        chrome_profile_path = os.path.join(profile_dir, args.profile_name)
        print(f"Sử dụng Chrome profile từ profile_name: {chrome_profile_path}")

        if not os.path.exists(chrome_profile_path):
            print(f"Cảnh báo: Profile '{args.profile_name}' không tồn tại tại {chrome_profile_path}")
            print("Sẽ tiếp tục mà không sử dụng Chrome profile.")
            chrome_profile_path = None

    # Lấy cấu hình proxy
    proxy_config = get_proxy_config(proxy_id=args.proxy_id, country=args.proxy_country)

    print(f"Chạy test với {'Chrome profile' if chrome_profile_path else 'trình duyệt mặc định'}...")

    # Khởi động trình duyệt và mở trang web
    asyncio.run(test_with_gui(
        args.chat,
        wait_time=args.wait,
        headless=args.headless,
        proxy_config=proxy_config,
        use_stealth=not args.no_stealth,
        chrome_profile_path=chrome_profile_path
    ))
