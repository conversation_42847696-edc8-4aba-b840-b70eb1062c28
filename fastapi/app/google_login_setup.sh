#!/bin/bash

# Thiết lập màu sắc
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Thư mục mặc định cho Chrome profile
DEFAULT_PROFILE_DIR="$HOME/chrome_profiles"
PROFILE_NAME="google_login"

# Hiển thị banner
echo -e "${GREEN}==================================================${NC}"
echo -e "${GREEN}  Thiết lập Chrome Profile và Đăng nhập Google    ${NC}"
echo -e "${GREEN}==================================================${NC}"

# Kiểm tra xem Playwright đã được cài đặt chưa
if ! python -c "import playwright" &> /dev/null; then
    echo -e "${RED}Playwright chưa được cài đặt.${NC}"
    echo -e "${YELLOW}Đang cài đặt Playwright...${NC}"
    pip install playwright
    playwright install chromium
fi

# Tạo thư mục profile nếu chưa tồn tại
mkdir -p "$DEFAULT_PROFILE_DIR"

# Hiển thị thông tin
echo -e "${YELLOW}Thông tin thiết lập:${NC}"
echo -e "- Thư mục profile: ${GREEN}$DEFAULT_PROFILE_DIR/$PROFILE_NAME${NC}"
echo -e "- Trình duyệt: ${GREEN}Chromium${NC}"
echo -e "- Trang đăng nhập: ${GREEN}https://accounts.google.com${NC}"

echo -e "\n${YELLOW}Hướng dẫn:${NC}"
echo -e "1. Một cửa sổ trình duyệt sẽ mở ra"
echo -e "2. Đăng nhập vào tài khoản Google của bạn"
echo -e "3. Sau khi đăng nhập xong, nhấn ${GREEN}Ctrl+C${NC} để thoát và lưu profile"
echo -e "4. Profile sẽ được lưu tại ${GREEN}$DEFAULT_PROFILE_DIR/$PROFILE_NAME${NC}"

echo -e "\n${YELLOW}Đang khởi động trình duyệt...${NC}"
echo -e "Nhấn Enter để tiếp tục..."
read

# Chạy script Python để thiết lập Chrome profile
python fastapi/app/setup_chrome_profile.py --profile-name "$PROFILE_NAME"

# Kiểm tra xem profile đã được tạo thành công chưa
if [ -d "$DEFAULT_PROFILE_DIR/$PROFILE_NAME" ]; then
    echo -e "\n${GREEN}Thiết lập thành công!${NC}"
    echo -e "Profile đã được lưu tại: ${GREEN}$DEFAULT_PROFILE_DIR/$PROFILE_NAME${NC}"
    echo -e "\nĐể sử dụng profile này với test_playwright.py, hãy chạy lệnh:"
    echo -e "${GREEN}python fastapi/app/test_playwright.py --chrome-profile $DEFAULT_PROFILE_DIR/$PROFILE_NAME${NC}"
    
    echo -e "\nHoặc sử dụng script run_test_with_profile.py:"
    echo -e "${GREEN}python fastapi/app/run_test_with_profile.py --profile-name $PROFILE_NAME${NC}"
else
    echo -e "\n${RED}Thiết lập thất bại!${NC}"
    echo -e "Không thể tạo profile tại: ${RED}$DEFAULT_PROFILE_DIR/$PROFILE_NAME${NC}"
fi
