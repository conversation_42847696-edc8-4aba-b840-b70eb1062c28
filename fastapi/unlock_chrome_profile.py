#!/usr/bin/env python3
import os
import sys
import subprocess
import logging
import argparse
import shutil

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Default profile directory
DEFAULT_PROFILE_DIR = "/root/chrome_profiles"

def unlock_chrome_profile(profile_path):
    """
    Unlocks a Chrome profile that might be locked by another process.

    Args:
        profile_path: Path to the Chrome profile directory

    Returns:
        bool: True if successfully unlocked or no lock found, False otherwise
    """
    try:
        logging.info(f"Attempting to unlock Chrome profile at: {profile_path}")
        
        # Check if the profile directory exists
        if not os.path.exists(profile_path):
            logging.error(f"Profile directory does not exist: {profile_path}")
            return False
            
        # Check for lock files and other profile state files
        lock_files = [
            "SingletonLock",
            "SingletonCookie",
            "SingletonSocket",
            ".com.google.Chrome.yLU8m4",
            "lockfile",
            "Lock",
            "lock",
            ".parentlock"
        ]

        # Remove all potential lock files
        for lock_file_name in lock_files:
            lock_file = os.path.join(profile_path, lock_file_name)
            if os.path.exists(lock_file):
                try:
                    os.remove(lock_file)
                    logging.info(f"Removed lock file: {lock_file}")
                except Exception as e:
                    logging.warning(f"Failed to remove lock file {lock_file}: {str(e)}")
        
        # Kill any Chrome processes that might be using this profile
        try:
            # Specific check for processes using this profile
            cmd = f"ps aux | grep -i 'chrome.*{profile_path}' | grep -v grep | awk '{{print $2}}'"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            pids = result.stdout.strip().split('\n')

            for pid in pids:
                if pid:
                    try:
                        subprocess.run(f"kill -9 {pid}", shell=True)
                        logging.info(f"Killed Chrome process with PID {pid}")
                    except Exception as e:
                        logging.warning(f"Failed to kill process {pid}: {str(e)}")
        except Exception as e:
            logging.warning(f"Error checking for running Chrome processes: {str(e)}")

        # Check if any lock files still exist
        for lock_file_name in lock_files:
            lock_file = os.path.join(profile_path, lock_file_name)
            if os.path.exists(lock_file):
                logging.warning(f"Lock file still exists after cleanup: {lock_file}")
                return False
                
        # Set proper permissions
        try:
            os.system(f"chmod -R 777 {profile_path}")
            logging.info(f"Set permissions on profile directory: {profile_path}")
        except Exception as e:
            logging.warning(f"Failed to set permissions: {str(e)}")

        logging.info(f"Successfully unlocked Chrome profile at: {profile_path}")
        return True
    except Exception as e:
        logging.error(f"Error unlocking Chrome profile: {str(e)}")
        return False

def reset_chrome_profile(profile_path):
    """
    Resets a Chrome profile by removing it and recreating the directory.

    Args:
        profile_path: Path to the Chrome profile directory

    Returns:
        bool: True if successfully reset, False otherwise
    """
    try:
        logging.info(f"Attempting to reset Chrome profile at: {profile_path}")
        
        # Try to remove lock files first
        unlock_chrome_profile(profile_path)

        # Remove the entire profile directory
        try:
            # First try with shutil.rmtree
            shutil.rmtree(profile_path, ignore_errors=True)
            logging.info(f"Removed profile directory with shutil.rmtree: {profile_path}")
        except Exception as e:
            logging.warning(f"Failed to remove directory with shutil.rmtree: {str(e)}")
            try:
                # If that fails, try with rm -rf
                subprocess.run(f"rm -rf {profile_path}", shell=True)
                logging.info(f"Removed profile directory with rm -rf: {profile_path}")
            except Exception as e:
                logging.error(f"Failed to remove directory with rm -rf: {str(e)}")
                return False

        # Create a new empty profile directory
        os.makedirs(profile_path, exist_ok=True)
        os.system(f"chmod -R 777 {profile_path}")
        logging.info(f"Created new profile directory: {profile_path}")
        
        return True
    except Exception as e:
        logging.error(f"Error resetting Chrome profile: {str(e)}")
        return False

def parse_arguments():
    parser = argparse.ArgumentParser(description="Unlock or reset Chrome profiles")
    
    parser.add_argument("--profile-name", 
                        help="Name of the Chrome profile to unlock")
    
    parser.add_argument("--profile-path",
                        help="Full path to the Chrome profile directory to unlock")
    
    parser.add_argument("--profile-dir", default=DEFAULT_PROFILE_DIR,
                        help=f"Base directory containing Chrome profiles (default: {DEFAULT_PROFILE_DIR})")
    
    parser.add_argument("--reset", action="store_true",
                        help="Reset the profile instead of just unlocking it")
    
    parser.add_argument("--all", action="store_true",
                        help="Unlock all profiles in the profile directory")
    
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_arguments()
    
    # Determine which profile(s) to unlock
    if args.all:
        # Unlock all profiles in the directory
        if not os.path.exists(args.profile_dir):
            logging.error(f"Profile directory does not exist: {args.profile_dir}")
            sys.exit(1)
            
        profiles = [d for d in os.listdir(args.profile_dir) 
                   if os.path.isdir(os.path.join(args.profile_dir, d))]
        
        if not profiles:
            logging.warning(f"No profiles found in directory: {args.profile_dir}")
            sys.exit(0)
            
        for profile in profiles:
            profile_path = os.path.join(args.profile_dir, profile)
            if args.reset:
                reset_chrome_profile(profile_path)
            else:
                unlock_chrome_profile(profile_path)
    else:
        # Unlock a specific profile
        if args.profile_path:
            profile_path = args.profile_path
        elif args.profile_name:
            profile_path = os.path.join(args.profile_dir, args.profile_name)
        else:
            logging.error("Either --profile-name, --profile-path, or --all must be specified")
            sys.exit(1)
            
        if args.reset:
            success = reset_chrome_profile(profile_path)
        else:
            success = unlock_chrome_profile(profile_path)
            
        if not success:
            sys.exit(1)
