FROM python:3.10

WORKDIR /app

# Cài đặt các thư viện cần thiết
RUN apt-get update && apt-get install -y \
    xvfb \
    fluxbox \
    x11vnc \
    novnc \
    xterm \
    wget \
    curl \
    gnupg \
    dbus-x11 \
    chromium \
    procps \
    net-tools

# Cài đặt Playwright với các dependencies
RUN pip install --no-cache-dir playwright
RUN playwright install chromium
RUN playwright install-deps chromium

# Cài đặt Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Cài đặt novnc
RUN mkdir -p /usr/share/novnc/utils/websockify && \
    wget -qO- https://github.com/novnc/noVNC/archive/v1.3.0.tar.gz | tar xz --strip 1 -C /usr/share/novnc && \
    wget -qO- https://github.com/novnc/websockify/archive/v0.10.0.tar.gz | tar xz --strip 1 -C /usr/share/novnc/utils/websockify

# Cài đặt mật khẩu VNC
RUN mkdir -p ~/.vnc && \
    x11vnc -storepasswd playwright ~/.vnc/passwd

# Copy toàn bộ code vào container
COPY . .

# Đảm bảo script khởi động có quyền thực thi
RUN chmod +x docker-entrypoint.sh

# Mở các cổng
EXPOSE 8000 5900 6080

# Khi khởi động container, chạy script entrypoint
CMD ["./docker-entrypoint.sh"] 