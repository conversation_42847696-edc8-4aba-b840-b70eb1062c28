# Tổng quan Hệ thống FastAPI Crawl Thời gian thực với Manus

## 1. <PERSON><PERSON><PERSON><PERSON> thiệu

Hệ thống FastAPI Crawl Thời gian thực với Manus là một backend gi<PERSON><PERSON> tương tác với Manus AI Chat thông qua crawling tự động. <PERSON>ệ thống sử dụng Playwright để tự động hóa trình duyệt và trích xuất dữ liệu chat theo thời gian thực.

## 2. C<PERSON>u trúc dự án

```
fastapi/
├── app/
│   ├── main.py                    # Điểm khởi đầu ứng dụng FastAPI
│   ├── manus_api.py               # Định nghĩa API endpoints
│   ├── manus_crawler.py           # Xử lý crawl Manus Chat
│   ├── manus_session_manager.py   # Quản lý phiên chat
│   ├── manus_websocket_handler.py # Xử lý WebSocket realtime
│   └── ...
├── main.py                        # Entry point
├── requirements.txt               # Thư viện phụ thuộc
└── start_manus_chat.sh            # Script khởi động
```

## 3. Cài đặt

### Thư viện phụ thuộc

```bash
pip install -r requirements.txt
```

Các thư viện chính:
- fastapi
- uvicorn
- playwright>=1.40.0
- websockets
- python-dotenv
- pydantic>=2.0.0
- aiohttp
- asyncio
- starlette

### Cài đặt Playwright

```bash
python -m playwright install chromium
```

## 4. Triển khai

### Khởi động thông thường

```bash
cd fastapi
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### Khởi động với script tự động

```bash
chmod +x start_manus_chat.sh
./start_manus_chat.sh
```

Script `start_manus_chat.sh` tự động:
- Cài đặt Playwright
- Thiết lập Chrome profiles
- Khởi động X server (cho môi trường không có GUI)
- Thiết lập VNC server (cho truy cập từ xa)
- Khởi động ứng dụng FastAPI

## 5. API Endpoints

### 1. Tạo phiên mới

```
POST /api/manus/session
```

Tham số:
- `chrome_profile_path` (tùy chọn): Đường dẫn đến Chrome profile
- `headless` (tùy chọn): Chạy ẩn trình duyệt (mặc định: false)
- `proxy_host`, `proxy_port`, `proxy_username`, `proxy_password` (tùy chọn): Cấu hình proxy

Ví dụ:
```json
{
  "chrome_profile_path": "/path/to/profile",
  "headless": false
}
```

### 2. Gửi tin nhắn

```
POST /api/manus/message?session_id=YOUR_SESSION_ID
```

Tham số:
- `content`: Nội dung tin nhắn
- `role` (tùy chọn): Vai trò (mặc định: "user")

Ví dụ:
```json
{
  "content": "Giới thiệu về bản thân bạn"
}
```

### 3. Lấy lịch sử chat

```
GET /api/manus/history/{session_id}
```

### 4. Đóng phiên

```
DELETE /api/manus/session/{session_id}
```

### 5. Danh sách Chrome profiles

```
GET /api/manus/profiles
```

### 6. WebSocket thời gian thực

```
WebSocket: /api/manus/ws/{session_id}
```

Định dạng tin nhắn WebSocket:
```json
{
  "type": "chat_message",
  "content": "Nội dung tin nhắn"
}
```

## 6. Thành phần chính

### ManusSessionManager

Quản lý các phiên chat với Manus, bao gồm:
- Khởi tạo trình duyệt Playwright
- Quản lý Chrome profiles
- Tạo/đóng phiên chat
- Gửi tin nhắn và theo dõi phản hồi

### ManusCrawler

Xử lý crawl dữ liệu từ Manus Chat:
- Điều hướng đến trang Manus
- Gửi tin nhắn qua giao diện
- Trích xuất tin nhắn từ DOM
- Theo dõi phản hồi theo thời gian thực

### ManusWebSocketManager

Quản lý kết nối WebSocket với frontend:
- Thiết lập/đóng kết nối
- Gửi/nhận tin nhắn theo thời gian thực
- Quản lý lịch sử chat
- Xử lý nhiều kết nối cùng lúc

## 7. Quy trình hoạt động

### 1. Khởi tạo phiên

Tạo phiên mới với Manus thông qua REST API hoặc WebSocket.

```python
# Tạo phiên mới qua REST API
async def create_manus_session(config: ManusSessionConfig):
    try:
        # Thiết lập proxy nếu có
        proxy_config = None
        if config.proxy_host and config.proxy_port:
            proxy_config = {
                "host": config.proxy_host,
                "port": config.proxy_port,
                "username": config.proxy_username,
                "password": config.proxy_password
            }
        
        # Tạo phiên mới
        session_id = await manus_session_manager.create_session(
            chrome_profile_path=config.chrome_profile_path,
            headless=config.headless,
            proxy_config=proxy_config
        )
        
        return {
            "session_id": session_id,
            "status": "success",
            "message": "Đã tạo phiên chat Manus mới"
        }
    except Exception as e:
        logger.error(f"Lỗi khi tạo phiên chat: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Lỗi khi tạo phiên chat: {str(e)}")
```

### 2. Load Chrome profile

Thiết lập và tải profile Chrome để duy trì trạng thái đăng nhập và tránh xác thực CAPTCHA.

```python
# Trong ManusSessionManager.create_session
async def create_session(self, 
                       session_id: str = None,
                       chrome_profile_path: str = None,
                       headless: bool = False,
                       proxy_config: Dict = None,
                       message_callback: Callable = None) -> str:
    # Xác định đường dẫn Chrome profile
    if chrome_profile_path:
        profile_path = chrome_profile_path
    else:
        # Tạo profile mới nếu không chỉ định
        profile_path = os.path.join(DEFAULT_PROFILE_DIR, f"profile_{session_id}")
        os.makedirs(profile_path, exist_ok=True)
    
    # Khởi động trình duyệt với profile
    context = await self.playwright.chromium.launch_persistent_context(
        user_data_dir=profile_path,
        headless=headless,
        args=browser_args,
        proxy=playwright_proxy
    )
    browser = context.browser
    
    return session_id
```

### 3. Điều hướng đến Manus Chat

Truy cập trang Manus Chat và xử lý quá trình đăng nhập nếu cần.

```python
# Trong ManusCrawler
async def navigate_to_manus(self) -> bool:
    try:
        # Điều hướng trực tiếp đến trang app của Manus
        await self.page.goto("https://manus.im/app", timeout=60000)
        await self.page.wait_for_load_state("networkidle")

        # Kiểm tra xem đã đăng nhập chưa
        if "app" in self.page.url or "dashboard" in self.page.url or "chat" in self.page.url:
            logger.info("Đã đăng nhập vào Manus AI")
            return True

        # Nếu chuyển hướng về trang đăng nhập, thử đăng nhập tự động
        if "login" in self.page.url or "signin" in self.page.url:
            logger.info("Đang trong trang đăng nhập, cần đăng nhập trước")
            # Xử lý đăng nhập tự động (nếu có thông tin)
            return False

        return True
    except Exception as e:
        logger.error(f"Lỗi khi điều hướng đến Manus AI: {str(e)}")
        return False
```

### 4. Gửi tin nhắn

Gửi tin nhắn đến Manus và theo dõi trạng thái gửi.

```python
# Trong ManusCrawler
async def send_message(self, message: str) -> bool:
    try:
        # Tìm ô nhập liệu
        input_selectors = [
            "textarea[placeholder='Send message to Manus']",
            "textarea.resize-none.min-h-\\[28px\\]",
            "textarea"
        ]

        input_element = None
        for selector in input_selectors:
            try:
                if await self.page.is_visible(selector, timeout=2000):
                    input_element = await self.page.query_selector(selector)
                    logger.info(f"Đã tìm thấy ô nhập liệu với selector: {selector}")
                    break
            except Exception:
                continue

        if not input_element:
            logger.error("Không tìm thấy ô nhập liệu")
            return False

        # Nhập tin nhắn và gửi
        await input_element.fill(message)
        await asyncio.sleep(0.5)
        await input_element.press("Enter")
        
        logger.info(f"Đã gửi tin nhắn: {message}")
        return True
    except Exception as e:
        logger.error(f"Lỗi khi gửi tin nhắn: {str(e)}")
        return False
```

### 5. Crawl dữ liệu

Thu thập dữ liệu phản hồi từ Manus theo hai hình thức: trích xuất nội dung DOM đơn giản và bản đầy đủ có HTML/CSS.

```python
# Trong ManusCrawler - Trích xuất dữ liệu DOM và HTML+CSS
async def extract_chat_messages(self, include_html: bool = False) -> List[Dict[str, Any]]:
    try:
        # Trích xuất tin nhắn bằng JavaScript
        messages = await self.page.evaluate("""
            (includeHtml) => {
                const messages = [];
                const extractedAt = new Date().toISOString();
                
                // Tìm container chính
                const messageContainers = document.querySelectorAll('.simplebar-content > div');
                
                for (const container of messageContainers) {
                    try {
                        // Xác định vai trò (user/assistant)
                        const isUserMessage = container.classList.contains('items-end') || 
                                            container.classList.contains('justify-end');
                        const role = isUserMessage ? 'user' : 'assistant';
                        
                        // Trích xuất nội dung văn bản
                        const contentElements = container.querySelectorAll('span, p, div[class*="text-"], pre, code');
                        let textContent = '';
                        
                        for (const elem of contentElements) {
                            textContent += elem.textContent.trim() + '\\n';
                        }
                        
                        // Chuẩn bị dữ liệu tin nhắn
                        const message = {
                            role: role,
                            content: textContent.trim(),
                            timestamp: extractedAt
                        };
                        
                        // Nếu yêu cầu bao gồm HTML
                        if (includeHtml) {
                            message.html = container.innerHTML;
                            message.css = Array.from(document.styleSheets)
                                .filter(sheet => !sheet.href) // Chỉ lấy inline styles
                                .map(sheet => Array.from(sheet.cssRules)
                                    .map(rule => rule.cssText)
                                    .join('\\n'))
                                .join('\\n');
                        }
                        
                        messages.push(message);
                    } catch (e) {
                        console.error('Error extracting message:', e);
                    }
                }
                
                return messages;
            }
        """, include_html)
        
        logger.info(f"Đã trích xuất {len(messages)} tin nhắn từ cuộc trò chuyện")
        return messages
    except Exception as e:
        logger.error(f"Lỗi khi trích xuất tin nhắn: {str(e)}")
        return []
```

### 6. Phản hồi

Gửi dữ liệu phản hồi về client qua REST API hoặc WebSocket.

```python
# Trong ManusWebSocketManager - Gửi phản hồi qua WebSocket
async def send_chat_response(self, session_id: str, message: Dict[str, Any], include_html: bool = False):
    try:
        # Xây dựng phản hồi
        response = {
            "type": "new_message",
            "message": message
        }
        
        # Loại bỏ HTML/CSS nếu không yêu cầu
        if not include_html and "html" in message:
            del message["html"]
            del message["css"]
        
        # Gửi qua WebSocket
        await self.send_json(session_id, response)
        
        # Thêm vào lịch sử
        if session_id in self.chat_history:
            self.chat_history[session_id].append(message)
            
    except Exception as e:
        logger.error(f"Lỗi khi gửi phản hồi: {str(e)}")
```

### 7. Duy trì kết nối

Theo dõi kết nối của user, duy trì phiên nếu người dùng vẫn ở trang web, đóng phiên nếu người dùng thoát hoặc reload.

```python
# Trong ManusWebSocketManager - Theo dõi trạng thái kết nối
async def monitor_connection(self, session_id: str):
    try:
        while session_id in self.connections:
            # Gửi ping định kỳ để kiểm tra kết nối
            try:
                await self.send_json(session_id, {"type": "ping", "timestamp": time.time()})
                await asyncio.sleep(30)  # Kiểm tra mỗi 30 giây
            except Exception:
                # Kết nối đã đóng
                logger.info(f"Kết nối WebSocket đã đóng: {session_id}")
                self.disconnect(session_id)
                break
    except asyncio.CancelledError:
        logger.info(f"Task theo dõi kết nối bị hủy: {session_id}")

# Xử lý khi client ngắt kết nối
@router.websocket("/api/manus/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str = None):
    try:
        # Kết nối WebSocket
        session_id = await websocket_manager.connect(websocket, session_id)
        
        # Bắt đầu task theo dõi kết nối
        connection_task = asyncio.create_task(
            websocket_manager.monitor_connection(session_id)
        )
        
        # Vòng lặp xử lý tin nhắn
        while True:
            data = await websocket.receive_text()
            # Xử lý tin nhắn...
            
    except WebSocketDisconnect:
        # Người dùng đã thoát hoặc reload trang
        websocket_manager.disconnect(session_id)
        logger.info(f"WebSocket ngắt kết nối: {session_id}")
    finally:
        # Dọn dẹp task theo dõi kết nối
        if 'connection_task' in locals() and not connection_task.done():
            connection_task.cancel()
```

### 8. Kết thúc phiên

Đóng phiên khi người dùng rời khỏi trang hoặc yêu cầu đóng.

```python
# Trong ManusSessionManager - Đóng phiên
async def close_session(self, session_id: str):
    if session_id in self.sessions:
        session = self.sessions[session_id]
        try:
            # Dừng crawler nếu đang chạy
            if session.crawler:
                session.crawler.stop_monitoring()
            
            # Đóng context và browser
            if session.context:
                await session.context.close()
            
            # Đánh dấu phiên không còn hoạt động
            session.is_active = False
            
            logger.info(f"Đã đóng phiên Manus: {session_id}")
        except Exception as e:
            logger.error(f"Lỗi khi đóng phiên {session_id}: {str(e)}")
        finally:
            # Xóa phiên khỏi danh sách
            del self.sessions[session_id]
```

## 8. Lưu ý triển khai

### Cấu hình Chrome Profile

Mỗi phiên chat sử dụng một Chrome profile riêng, hỗ trợ:
- Lưu phiên đăng nhập
- Sử dụng cookie và session
- Tránh CAPTCHA và giới hạn truy cập

### Môi trường không có GUI

Khi triển khai trên server không có GUI:
- Sử dụng Xvfb để tạo màn hình ảo
- Cấu hình VNC/noVNC để truy cập từ xa
- Thiết lập fluxbox làm window manager

### Xử lý bất đồng bộ

Hệ thống sử dụng asyncio để xử lý đồng thời nhiều phiên:
- Mỗi phiên là một task riêng biệt
- Tự động dọn dẹp phiên không hoạt động
- Xử lý callback khi có tin nhắn mới

## 9. Mở rộng

### Tích hợp với frontend

```javascript
// Kết nối WebSocket
const ws = new WebSocket(`ws://your-server:8000/api/manus/ws/`);

// Gửi tin nhắn
ws.send(JSON.stringify({
  type: "chat_message",
  content: "Xin chào Manus!"
}));

// Nhận phản hồi
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  if (data.type === "new_message") {
    console.log("Tin nhắn mới:", data.message);
  }
};
```

### Xử lý lỗi

Hệ thống tự động xử lý các lỗi phổ biến:
- Đăng nhập lại khi phiên hết hạn
- Thử lại khi gặp lỗi mạng
- Tự động đóng phiên khi không hoạt động

## 10. Ví dụ sử dụng

### Tạo phiên và gửi tin nhắn

```python
import requests
import json

# Tạo phiên mới
session_response = requests.post(
    "http://localhost:8000/api/manus/session",
    json={"headless": False}
)
session_data = session_response.json()
session_id = session_data["session_id"]

# Gửi tin nhắn
message_response = requests.post(
    f"http://localhost:8000/api/manus/message?session_id={session_id}",
    json={"content": "Giới thiệu về bản thân bạn"}
)

# Lấy lịch sử chat
history_response = requests.get(
    f"http://localhost:8000/api/manus/history/{session_id}"
)
messages = history_response.json()["messages"]
```

## 11. Ví dụ sử dụng CURL

### 1. Tạo phiên mới với profile cụ thể

```bash
curl -X POST "http://localhost:8000/api/manus/session" \
  -H "Content-Type: application/json" \
  -d '{
    "chrome_profile_path": "/path/to/chrome_profiles/my_profile",
    "headless": false
  }'
```

Phản hồi:
```json
{
  "session_id": "manus_session_1234567890",
  "status": "success",
  "message": "Đã tạo phiên chat Manus mới"
}
```

### 2. Gửi tin nhắn đến Manus

```bash
curl -X POST "http://localhost:8000/api/manus/message?session_id=manus_session_1234567890" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "Giới thiệu về bản thân bạn",
    "include_html": false
  }'
```

Phản hồi:
```json
{
  "success": true,
  "message": "Đã gửi tin nhắn thành công",
  "session_id": "manus_session_1234567890"
}
```

### 3. Gửi tin nhắn với yêu cầu trả về HTML và CSS

```bash
curl -X POST "http://localhost:8000/api/manus/message?session_id=manus_session_1234567890" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "Viết cho tôi đoạn code Python để tạo API sử dụng FastAPI",
    "include_html": true
  }'
```

### 4. Lấy lịch sử chat (không bao gồm HTML)

```bash
curl -X GET "http://localhost:8000/api/manus/history/manus_session_1234567890"
```

Phản hồi:
```json
{
  "session_id": "manus_session_1234567890",
  "messages": [
    {
      "role": "user",
      "content": "Giới thiệu về bản thân bạn",
      "timestamp": 1623456789.123
    },
    {
      "role": "assistant",
      "content": "Xin chào! Tôi là Manus AI, một trí tuệ nhân tạo được tạo ra để hỗ trợ bạn trong các công việc hàng ngày...",
      "timestamp": 1623456790.456
    }
  ],
  "count": 2
}
```

### 5. Lấy lịch sử chat (bao gồm HTML và CSS)

```bash
curl -X GET "http://localhost:8000/api/manus/history/manus_session_1234567890?include_html=true"
```

### 6. Liệt kê các Chrome profile có sẵn

```bash
curl -X GET "http://localhost:8000/api/manus/profiles"
```

Phản hồi:
```json
{
  "profiles": [
    {
      "name": "my_profile",
      "path": "/path/to/chrome_profiles/my_profile",
      "size_mb": 125.75
    },
    {
      "name": "profile_2",
      "path": "/path/to/chrome_profiles/profile_2",
      "size_mb": 87.45
    }
  ],
  "profile_dir": "/path/to/chrome_profiles",
  "count": 2
}
```

### 7. Đóng phiên chat

```bash
curl -X DELETE "http://localhost:8000/api/manus/session/manus_session_1234567890"
```

Phản hồi:
```json
{
  "status": "success",
  "message": "Đã đóng phiên chat manus_session_1234567890"
}
```

### 8. Sử dụng WebSocket (ví dụ JavaScript)

```javascript
// Kết nối WebSocket
const ws = new WebSocket("ws://localhost:8000/api/manus/ws");

// Gửi tin nhắn qua WebSocket
ws.onopen = () => {
  ws.send(JSON.stringify({
    type: "chat_message",
    content: "Giới thiệu về bản thân bạn",
    include_html: true
  }));
};

// Nhận phản hồi
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  if (data.type === "new_message") {
    console.log("Tin nhắn mới:", data.message);
    
    // Nếu tin nhắn bao gồm HTML và CSS
    if (data.message.html) {
      console.log("HTML:", data.message.html);
      console.log("CSS:", data.message.css);
    }
  }
};
``` 