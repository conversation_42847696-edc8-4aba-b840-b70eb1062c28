#!/bin/bash

# <PERSON><PERSON><PERSON> tra biến môi trường USE_GUI
if [ "$USE_GUI" = "true" ]; then
    echo "Khởi động chế độ GUI với VNC..."
    # Khởi động supervisord để quản lý xvfb, vnc và fastapi
    exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
else
    echo "Khởi động chế độ API thông thường..."
    # Chạy FastAPI trực tiếp
    exec uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
fi 