#!/bin/bash
set -e

# <PERSON><PERSON><PERSON> bảo file có quyền thực thi
chmod +x "$0"

# Xóa file lock X11 nếu tồn tại
rm -f /tmp/.X1-lock

# Đ<PERSON><PERSON> bả<PERSON> thư mục Chrome profiles tồn tại và có quyền truy cập đúng
mkdir -p /root/chrome_profiles
chmod 777 /root/chrome_profiles

# Khởi động X server, VNC và window manager
Xvfb :1 -screen 0 1280x720x16 &
sleep 2
x11vnc -display :1 -forever -shared -rfbport 5900 -passwd playwright &
fluxbox -display :1 &

# Khởi động noVNC
cd /usr/share/novnc
./utils/novnc_proxy --vnc localhost:5900 --listen 6080 &

# Đ<PERSON><PERSON> các dịch vụ khởi động
sleep 3

# Khởi động ứng dụng
cd /app
exec uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --forwarded-allow-ips='*' 