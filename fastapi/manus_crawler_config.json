{"selectors": {"message_container": "div.flex.flex-col.w-full.gap-\\[12px\\]", "user_message": "div.flex.max-w-\\[90\\%\\].relative.flex-col", "assistant_message": "div.flex.w-full.flex-col.items-end", "message_content": "div.whitespace-pre-wrap, div.prose", "attachment": "div.flex.items-center.gap-1\\.5.p-2.pr-2\\.5", "timestamp": "div.text-\\[12px\\].text-\\[var\\(--text-tertiary\\)\\]"}, "wait_for_selector": "div.flex.flex-col.w-full.gap-\\[12px\\]", "extract_config": {"messages": {"selector": "div.flex.max-w-\\[90\\%\\].relative.flex-col, div.flex.w-full.flex-col.items-end", "content_selector": "div.whitespace-pre-wrap, div.prose", "attachment_selector": "div.flex.items-center.gap-1\\.5.p-2.pr-2\\.5", "timestamp_selector": "div.text-\\[12px\\].text-\\[var\\(--text-tertiary\\)\\]"}}, "playwright_options": {"headless": true, "slow_mo": 50, "timeout": 30000}}