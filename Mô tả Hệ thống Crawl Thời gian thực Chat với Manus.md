# <PERSON><PERSON> tả Hệ thống Crawl Thời gian thực Chat với Manus

## 1. G<PERSON>ới thiệu

Tài liệu này mô tả chi tiết kiến trúc và quy trình hoạt động của hệ thống crawl thời gian thực, đ<PERSON><PERSON><PERSON> thiết kế để tương tác với Man<PERSON> thông qua giao diện chat. <PERSON>ệ thống bao gồm một frontend xây dựng bằng Next.js, một backend bằng FastAPI và sử dụng Playwright kết hợp với Chrome profile để tự động hóa việc giao tiếp với Man<PERSON>.

Mục tiêu chính là cho phép người dùng cuối tương tác với Manus thông qua giao diện của hệ thống, trong khi hệ thống tự động gửi tin nhắn đến <PERSON>, theo dõi phản hồi trong thời gian thực và hiển thị kết quả cho người dùng một cách liền mạch.

## 2. <PERSON><PERSON> tích Yêu cầu

### 2.1. Yêu cầu Chức năng

*   **Giao tiếp Người dùng - Hệ thống:** Người dùng nhập tin nhắn vào giao diện frontend (Next.js).
*   **Chuyển tiếp Tin nhắn:** Hệ thống backend (FastAPI) nhận tin nhắn từ frontend và điều phối tác vụ gửi tin nhắn đến Manus.
*   **Tương tác Tự động với Manus:** Sử dụng Playwright để mở trình duyệt, điều hướng đến giao diện chat của Manus, đăng nhập (sử dụng Chrome profile để duy trì phiên), gửi tin nhắn của người dùng.
*   **Crawl Phản hồi Thời gian thực:** Playwright phải liên tục theo dõi (crawl) khung chat của Manus để phát hiện và trích xuất các tin nhắn phản hồi mới ngay khi chúng xuất hiện.
*   **Định dạng Dữ liệu:** Dữ liệu crawl được từ Manus (tin nhắn, có thể bao gồm văn bản, code, định dạng khác) cần được xử lý và cấu trúc lại theo một định dạng thống nhất (ví dụ: JSON) trước khi gửi về backend.
*   **Cập nhật Frontend:** Backend (FastAPI) gửi dữ liệu đã xử lý đến frontend (Next.js) để hiển thị cho người dùng, đảm bảo cập nhật theo thời gian thực (ví dụ: qua WebSockets).
*   **Quản lý Phiên:** Sử dụng Chrome profile để duy trì trạng thái đăng nhập và ngữ cảnh chat với Manus qua các lần tương tác.

### 2.2. Yêu cầu Phi chức năng

*   **Thời gian thực:** Phản hồi từ Manus phải được phát hiện và hiển thị cho người dùng với độ trễ tối thiểu.
*   **Độ tin cậy:** Hệ thống cần hoạt động ổn định, xử lý được các lỗi tiềm ẩn (ví dụ: lỗi mạng, thay đổi giao diện Manus, lỗi Playwright).
*   **Khả năng mở rộng:** Kiến trúc nên cho phép mở rộng trong tương lai (ví dụ: hỗ trợ nhiều người dùng đồng thời, tăng số lượng worker Playwright).
*   **Bảo mật:** Quản lý Chrome profile và thông tin nhạy cảm (nếu có) một cách an toàn.

### 2.3. Công nghệ Sử dụng

*   **Frontend:** Next.js
*   **Backend:** FastAPI (Python)
*   **Automation/Crawling:** Playwright (Python)
*   **Trình duyệt:** Chrome (thông qua Playwright)
*   **Quản lý Phiên:** Chrome Profiles
*   **Giao tiếp Real-time (Backend-Frontend):** WebSockets (khuyến nghị)

*(Phần tiếp theo sẽ mô tả kiến trúc tổng thể)*



## 3. Kiến trúc Tổng thể

Hệ thống được thiết kế theo kiến trúc microservice/phân tán, bao gồm các thành phần chính sau:

1.  **Frontend (Next.js):**
    *   Chịu trách nhiệm hiển thị giao diện người dùng (UI) cho việc chat.
    *   Thu nhận tin nhắn từ người dùng.
    *   Gửi tin nhắn đến Backend qua API (ví dụ: HTTP POST hoặc WebSocket message).
    *   Thiết lập kết nối WebSocket với Backend để nhận phản hồi từ Manus theo thời gian thực.
    *   Hiển thị tin nhắn của người dùng và phản hồi từ Manus trong giao diện chat.

2.  **Backend (FastAPI):**
    *   Cung cấp các API endpoint cho Frontend (ví dụ: `/send_message`, `/get_history`).
    *   Quản lý kết nối WebSocket với Frontend để đẩy dữ liệu real-time.
    *   Nhận yêu cầu gửi tin nhắn từ Frontend.
    *   Điều phối và giao tiếp với Playwright Worker (có thể thông qua hàng đợi tin nhắn như RabbitMQ/Redis PubSub hoặc gọi trực tiếp nếu kiến trúc đơn giản hơn).
    *   Nhận dữ liệu đã crawl (phản hồi từ Manus) từ Playwright Worker.
    *   Xử lý, định dạng lại dữ liệu nhận được theo cấu trúc chuẩn (ví dụ: JSON).
    *   Đẩy dữ liệu đã xử lý đến Frontend qua WebSocket.
    *   (Tùy chọn) Lưu trữ lịch sử chat.

3.  **Playwright Worker (Python Script/Service):**
    *   Là một tiến trình hoặc dịch vụ riêng biệt chạy Playwright.
    *   Nhận yêu cầu tương tác với Manus từ Backend (ví dụ: "gửi tin nhắn X", "kiểm tra tin nhắn mới").
    *   Khởi chạy trình duyệt Chrome sử dụng một Chrome profile cụ thể (`user_data_dir`) để duy trì phiên đăng nhập và ngữ cảnh.
    *   Tự động hóa các thao tác trên giao diện web của Manus:
        *   Điều hướng đến trang chat.
        *   Tìm ô nhập liệu và nhập tin nhắn.
        *   Nhấn nút gửi.
        *   **Quan trọng:** Liên tục theo dõi (observe/poll) khu vực hiển thị chat của Manus để phát hiện các phần tử DOM mới hoặc thay đổi tương ứng với tin nhắn phản hồi.
    *   Trích xuất nội dung tin nhắn phản hồi (văn bản, code block, định dạng khác).
    *   Gửi dữ liệu đã trích xuất về cho Backend.
    *   Xử lý lỗi và báo cáo trạng thái về Backend.

4.  **Chrome Profile:**
    *   Một thư mục dữ liệu người dùng của Chrome (`user_data_dir`).
    *   Chứa cookies, lịch sử, trạng thái đăng nhập, tiện ích mở rộng, v.v.
    *   Được Playwright sử dụng để khởi chạy trình duyệt với ngữ cảnh đã được xác thực trước, tránh việc phải đăng nhập lại Manus mỗi lần chạy.
    *   Cần được quản lý cẩn thận, có thể tạo profile riêng cho mục đích tự động hóa này.

5.  **Giao diện Chat Manus:**
    *   Giao diện web mà Playwright sẽ tương tác.
    *   Hệ thống crawl phụ thuộc vào cấu trúc DOM và cách hoạt động của giao diện này. Bất kỳ thay đổi nào trên giao diện Manus đều có thể yêu cầu cập nhật lại logic crawl của Playwright.

### Luồng Dữ liệu Chính:

```mermaid
sequenceDiagram
    participant User
    participant Frontend (Next.js)
    participant Backend (FastAPI)
    participant Playwright Worker
    participant Manus Web UI

    User->>+Frontend (Next.js): Nhập và gửi tin nhắn
    Frontend (Next.js)->>+Backend (FastAPI): Gửi tin nhắn (API/WebSocket)
    Backend (FastAPI)->>+Playwright Worker: Yêu cầu gửi tin nhắn tới Manus
    Playwright Worker->>+Manus Web UI: Mở trình duyệt (với Profile), điều hướng, nhập và gửi tin nhắn
    Manus Web UI-->>-Playwright Worker: Hiển thị tin nhắn đã gửi
    Note right of Playwright Worker: Bắt đầu theo dõi phản hồi
    Manus Web UI-->>Playwright Worker: Manus gửi phản hồi (xuất hiện trên UI)
    Playwright Worker->>-Backend (FastAPI): Crawl và gửi dữ liệu phản hồi đã trích xuất
    Backend (FastAPI)->>-Frontend (Next.js): Xử lý và đẩy phản hồi (WebSocket)
    Frontend (Next.js)->>-User: Hiển thị phản hồi từ Manus
```

*(Phần tiếp theo sẽ mô tả chi tiết quy trình crawl thời gian thực)*



## 4. Quy trình Crawl Thời gian thực

Quy trình crawl thời gian thực là cốt lõi của hệ thống, đảm bảo rằng các phản hồi từ Manus được thu thập và hiển thị cho người dùng một cách nhanh chóng. Playwright Worker chịu trách nhiệm chính cho quy trình này.

### 4.1. Khởi tạo và Gửi Tin nhắn

1.  **Khởi chạy:** Khi Backend yêu cầu, Playwright Worker khởi chạy một instance của trình duyệt Chrome, sử dụng `user_data_dir` đã được cấu hình (Chrome Profile) để tải ngữ cảnh người dùng (phiên đăng nhập Manus).
2.  **Điều hướng:** Playwright điều hướng đến trang chat của Manus.
3.  **Xác định Vùng Chat:** Worker xác định các phần tử DOM quan trọng trên trang: ô nhập liệu (input box), nút gửi (send button), và quan trọng nhất là vùng chứa các tin nhắn chat (chat container).
4.  **Gửi Tin nhắn:** Worker nhập tin nhắn do người dùng cung cấp (nhận từ Backend) vào ô nhập liệu và mô phỏng hành động nhấn nút gửi.

### 4.2. Theo dõi và Trích xuất Phản hồi

Sau khi gửi tin nhắn, Worker bắt đầu quá trình theo dõi liên tục vùng chứa chat để phát hiện phản hồi từ Manus. Có một số chiến lược có thể áp dụng:

*   **Chiến lược 1: Polling DOM (Đơn giản nhưng kém hiệu quả hơn)**
    *   Worker định kỳ (ví dụ: mỗi 500ms) truy vấn DOM của vùng chứa chat để tìm các phần tử tin nhắn mới.
    *   Để xác định tin nhắn mới, Worker cần một cơ chế so sánh trạng thái hiện tại với trạng thái trước đó. Ví dụ:
        *   Đếm số lượng phần tử tin nhắn. Nếu số lượng tăng, các phần tử cuối cùng là tin nhắn mới.
        *   Lưu trữ ID hoặc timestamp của tin nhắn cuối cùng đã thấy. Tìm các tin nhắn có ID/timestamp mới hơn.
        *   Sử dụng các thuộc tính đặc biệt (ví dụ: `data-message-id`, class CSS) mà Manus có thể sử dụng để đánh dấu tin nhắn.
    *   **Nhược điểm:** Có độ trễ nhất định, tốn tài nguyên CPU/mạng do kiểm tra liên tục, khó xác định chính xác *tất cả* các thay đổi nếu nhiều tin nhắn đến cùng lúc hoặc có sự chỉnh sửa tin nhắn.

*   **Chiến lược 2: Sử dụng `waitForSelector` hoặc `waitForFunction` (Cân bằng)**
    *   Playwright cung cấp các hàm chờ đợi (`page.wait_for_selector`, `page.wait_for_function`) cho phép tạm dừng script cho đến khi một điều kiện cụ thể được thỏa mãn.
    *   Worker có thể chờ đợi một selector CSS/XPath đại diện cho một tin nhắn mới xuất hiện trong vùng chat. Ví dụ: `page.wait_for_selector('.chat-container .message:not(.processed)')` (giả sử ta thêm class `processed` sau khi xử lý).
    *   Hoặc sử dụng `page.wait_for_function` để thực thi một đoạn JavaScript trong trình duyệt, chờ đợi một điều kiện phức tạp hơn (ví dụ: số lượng tin nhắn tăng lên, hoặc nội dung của tin nhắn cuối cùng thay đổi).
    *   Sau khi chờ đợi thành công (phát hiện thay đổi), Worker tiến hành trích xuất dữ liệu.
    *   **Ưu điểm:** Hiệu quả hơn polling, phản ứng nhanh hơn khi có thay đổi.
    *   **Nhược điểm:** Cần xác định đúng selector hoặc logic hàm chờ đợi. Nếu Manus cập nhật giao diện phức tạp (ví dụ: không chỉ thêm phần tử mới mà sửa đổi phần tử cũ), việc chờ đợi có thể phức tạp.

*   **Chiến lược 3: DOM MutationObserver (Hiệu quả nhất nhưng phức tạp hơn)**
    *   Sử dụng `page.expose_function` để tạo một cầu nối từ JavaScript trong trình duyệt về Python script của Playwright.
    *   Sử dụng `page.evaluate` để đăng ký một `MutationObserver` trong trình duyệt, theo dõi các thay đổi (thêm/xóa node, thay đổi thuộc tính/nội dung) bên trong vùng chứa chat.
    *   Khi `MutationObserver` phát hiện thay đổi liên quan đến tin nhắn mới, nó sẽ gọi hàm đã được expose (thông qua `page.expose_function`), truyền dữ liệu về sự thay đổi (ví dụ: node mới được thêm) về cho script Python.
    *   Script Python nhận được thông báo gần như ngay lập tức và tiến hành trích xuất dữ liệu từ node được báo cáo.
    *   **Ưu điểm:** Phản ứng nhanh nhất, hiệu quả nhất về tài nguyên, dựa trên sự kiện thay vì kiểm tra định kỳ.
    *   **Nhược điểm:** Cài đặt phức tạp hơn, đòi hỏi kiến thức về JavaScript và `MutationObserver`, cần xử lý cẩn thận các loại mutation khác nhau.

**Khuyến nghị:** Bắt đầu với **Chiến lược 2 (`waitForSelector`/`waitForFunction`)** vì sự cân bằng giữa hiệu quả và độ phức tạp. Nếu yêu cầu về độ trễ cực thấp hoặc gặp vấn đề với việc xác định thay đổi, hãy cân nhắc nâng cấp lên **Chiến lược 3 (`MutationObserver`)**. Tránh **Chiến lược 1 (Polling)** nếu có thể.

### 4.3. Trích xuất và Định dạng Dữ liệu

1.  **Xác định Tin nhắn Mới:** Dựa vào chiến lược theo dõi, Worker xác định (các) phần tử DOM đại diện cho tin nhắn phản hồi mới từ Manus.
2.  **Trích xuất Nội dung:**
    *   Sử dụng các phương thức của Playwright (`element_handle.inner_text()`, `element_handle.inner_html()`, `element_handle.query_selector_all()`, `element_handle.get_attribute()`) để lấy nội dung từ các phần tử tin nhắn.
    *   **Xử lý cấu trúc phức tạp:** Tin nhắn Manus có thể chứa văn bản thuần, khối mã (code blocks), danh sách, liên kết, hình ảnh, v.v. Worker cần phân tích cấu trúc HTML bên trong phần tử tin nhắn để xác định và trích xuất các loại nội dung khác nhau.
        *   Ví dụ: Tìm các thẻ `<code>` hoặc `<pre>` cho khối mã, thẻ `<a>` cho liên kết, thẻ `<img>` cho hình ảnh.
    *   **Thu thập Metadata:** Trích xuất thông tin bổ sung nếu có và cần thiết (ví dụ: người gửi - thường là Manus, timestamp).
3.  **Định dạng Dữ liệu:** Cấu trúc lại dữ liệu đã trích xuất thành một định dạng nhất quán, ví dụ JSON, trước khi gửi về Backend. Định dạng này nên được thống nhất giữa Playwright Worker và Backend.
    ```json
    {
      "id": "msg_12345", // ID duy nhất (nếu có thể lấy)
      "sender": "Manus",
      "timestamp": "2025-05-24T10:00:00Z", // (nếu có thể lấy)
      "parts": [
        {"type": "text", "content": "Đây là phản hồi của tôi."},
        {"type": "code", "language": "python", "content": "print('Hello, world!')"},
        {"type": "text", "content": "Bạn có câu hỏi nào khác không?"}
      ]
    }
    ```

### 4.4. Gửi Dữ liệu về Backend

*   Sau khi trích xuất và định dạng, Playwright Worker gửi đối tượng JSON này về cho Backend (FastAPI) thông qua một cơ chế đã định trước (ví dụ: gọi một API endpoint nội bộ của Backend, hoặc đẩy vào một hàng đợi tin nhắn mà Backend đang lắng nghe).

### 4.5. Xử lý Lỗi và Vòng lặp

*   Worker cần có cơ chế xử lý lỗi (ví dụ: không tìm thấy selector, trang bị lỗi, mất kết nối). Khi gặp lỗi, nó nên báo cáo về Backend.
*   Quy trình theo dõi (bước 4.2 đến 4.4) cần được lặp lại liên tục sau khi gửi tin nhắn ban đầu để bắt được tất cả các phản hồi từ Manus cho đến khi có tín hiệu dừng hoặc một khoảng thời gian chờ hợp lý kết thúc.

*(Phần tiếp theo sẽ trình bày giải pháp tích hợp Playwright với Chrome Profile)*



## 5. Tích hợp Playwright với Chrome Profile

Việc sử dụng Chrome Profile (`user_data_dir`) là rất quan trọng để đảm bảo Playwright có thể tương tác với Manus một cách ổn định mà không cần phải thực hiện đăng nhập mỗi lần chạy. Nó giúp duy trì trạng thái phiên, bao gồm cookies đăng nhập và các cài đặt khác.

### 5.1. Tại sao cần Chrome Profile?

*   **Duy trì Phiên đăng nhập:** Manus yêu cầu đăng nhập. Nếu không có profile, mỗi lần Playwright khởi chạy trình duyệt sẽ là một phiên hoàn toàn mới, yêu cầu đăng nhập lại, làm gián đoạn quy trình tự động.
*   **Lưu trữ Ngữ cảnh:** Profile lưu trữ cookies, local storage, session storage, và các dữ liệu khác giúp trang web nhận diện người dùng và duy trì trạng thái ứng dụng (ví dụ: lịch sử chat gần đây trong giao diện Manus).
*   **Tùy chỉnh Trình duyệt:** Profile có thể chứa các cài đặt trình duyệt hoặc tiện ích mở rộng (extensions) cần thiết (mặc dù nên hạn chế sử dụng extension trong môi trường tự động hóa trừ khi thực sự cần thiết).

### 5.2. Cách Sử dụng Profile với Playwright

Playwright cho phép bạn khởi chạy trình duyệt với một thư mục dữ liệu người dùng cụ thể thông qua tham số `user_data_dir` khi khởi tạo ngữ cảnh trình duyệt (`browser.new_context`).

```python
from playwright.sync_api import sync_playwright

# Đường dẫn đến thư mục Chrome Profile
# Quan trọng: Đường dẫn này cần tồn tại và có quyền truy cập phù hợp.
# Nên tạo một profile riêng biệt cho mục đích tự động hóa này.
chrome_profile_path = "/path/to/your/chrome/profile/for/manus"

def run_playwright_with_profile():
    with sync_playwright() as p:
        # Khởi chạy trình duyệt Chromium (hoặc Chrome nếu cài đặt)
        # Sử dụng launch_persistent_context thay vì launch + new_context
        # để làm việc trực tiếp với user_data_dir
        context = p.chromium.launch_persistent_context(
            user_data_dir=chrome_profile_path,
            headless=False,  # Chạy ở chế độ non-headless để dễ debug ban đầu
            # Các tùy chọn khác nếu cần
            # args=["--disable-gpu", "--no-sandbox"], # Ví dụ: tùy chọn cho môi trường Linux/Docker
            channel="chrome" # Chỉ định sử dụng Chrome đã cài đặt thay vì Chromium đi kèm Playwright nếu muốn
        )
        page = context.new_page() # Mở tab mới trong ngữ cảnh đã có profile

        try:
            # Điều hướng đến trang chat Manus
            page.goto("URL_CUA_MANUS_CHAT") # Thay thế bằng URL thực tế

            # --- Thực hiện các thao tác khác --- 
            # Ví dụ: gửi tin nhắn, crawl phản hồi...
            # Do sử dụng profile, nếu đã đăng nhập trước đó trong profile này,
            # bạn sẽ vào thẳng giao diện chat mà không cần login.
            print("Đã vào trang Manus với profile.")
            page.wait_for_timeout(5000) # Chờ ví dụ

            # --- Logic gửi tin nhắn và crawl --- 
            # ... (như mô tả ở phần 4)

        except Exception as e:
            print(f"Lỗi xảy ra: {e}")
        finally:
            # Đóng ngữ cảnh sẽ đóng cả trình duyệt được khởi chạy bởi launch_persistent_context
            context.close()

# Chạy hàm
# run_playwright_with_profile()
```

### 5.3. Quản lý Chrome Profile

*   **Tạo Profile Riêng:** Nên tạo một profile Chrome hoàn toàn mới dành riêng cho việc tự động hóa này. Không nên sử dụng profile chính của bạn để tránh xung đột và rủi ro bảo mật.
    *   Cách tạo: Mở Chrome, vào phần quản lý Profile (biểu tượng avatar), chọn "Add" hoặc "Quản lý người dùng" -> "Thêm người". Sau đó, đăng nhập vào Manus trong profile mới này một lần.
    *   Xác định đường dẫn: Tìm đường dẫn đến thư mục profile vừa tạo (thường nằm trong thư mục cấu hình của Chrome, ví dụ trên Windows là `%LOCALAPPDATA%\Google\Chrome\User Data\Profile X`, trên Linux là `~/.config/google-chrome/Profile X`).
*   **Quyền Truy cập:** Đảm bảo tiến trình chạy Playwright Worker có quyền đọc/ghi vào thư mục profile.
*   **Sao lưu:** Cân nhắc sao lưu profile định kỳ, đặc biệt là nếu nó chứa thông tin quan trọng.
*   **Tránh Chạy Đồng thời:** Không nên mở trình duyệt Chrome thủ công bằng *cùng một profile* đang được Playwright sử dụng. Điều này có thể gây ra lỗi "lock file" hoặc hành vi không mong muốn.

### 5.4. Lưu ý Bảo mật

*   Thư mục profile chứa thông tin nhạy cảm như cookies đăng nhập. Bảo vệ thư mục này, hạn chế quyền truy cập chỉ cho tiến trình Playwright Worker.
*   Không lưu trữ mật khẩu trực tiếp trong code. Việc sử dụng profile giúp tránh phải xử lý mật khẩu trong script.

*(Phần tiếp theo sẽ mô tả cơ chế đồng bộ và trả kết quả định dạng Manus)*



## 6. Cơ chế Đồng bộ và Trả Kết quả Định dạng Manus

Sau khi Playwright Worker crawl và trích xuất thành công phản hồi từ giao diện Manus, dữ liệu này cần được gửi về Backend, xử lý và đẩy đến Frontend một cách kịp thời để hiển thị cho người dùng. Cơ chế đồng bộ hóa và định dạng dữ liệu đóng vai trò then chốt ở đây.

### 6.1. Giao tiếp Playwright Worker -> Backend (FastAPI)

*   **Phương thức:** Playwright Worker cần gửi dữ liệu JSON đã được định dạng (như mô tả ở mục 4.3) về cho Backend. Các phương thức phổ biến bao gồm:
    *   **API Call:** Worker gọi một endpoint API nội bộ trên Backend (ví dụ: `POST /api/v1/manus_response`) và gửi dữ liệu JSON trong body của request. Đây là cách đơn giản nhất nếu Worker và Backend chạy trên cùng mạng hoặc có thể giao tiếp trực tiếp.
    *   **Hàng đợi Tin nhắn (Message Queue - MQ):** Sử dụng một hệ thống MQ như RabbitMQ hoặc Redis Pub/Sub. Worker đẩy tin nhắn chứa dữ liệu JSON vào một hàng đợi/topic cụ thể. Backend sẽ lắng nghe (subscribe) trên hàng đợi/topic đó để nhận tin nhắn. Cách này giúp tách rời Worker và Backend, tăng khả năng chịu lỗi và mở rộng.
*   **Nội dung:** Dữ liệu gửi đi là đối tượng JSON chứa các phần (parts) của tin nhắn Manus đã được phân loại (text, code, etc.).

### 6.2. Xử lý tại Backend (FastAPI)

1.  **Tiếp nhận Dữ liệu:** Backend nhận dữ liệu JSON từ Worker (qua API hoặc MQ).
2.  **Xác định Người nhận:** Backend cần xác định đúng phiên người dùng (trên Frontend) nào đang chờ phản hồi này. Thông tin này thường được gắn với yêu cầu ban đầu khi người dùng gửi tin nhắn (ví dụ: qua `user_id`, `session_id`, hoặc `websocket_connection_id`).
3.  **(Tùy chọn) Xử lý/Biến đổi Thêm:** Backend có thể thực hiện thêm các bước xử lý nếu cần, ví dụ:
    *   Làm sạch dữ liệu.
    *   Lưu trữ lịch sử chat vào cơ sở dữ liệu.
    *   Kết hợp nhiều phần phản hồi nhỏ thành một tin nhắn lớn hơn (nếu Manus trả lời theo kiểu streaming và Worker gửi từng phần nhỏ).
4.  **Định dạng cho Frontend:** Mặc dù Worker đã định dạng dữ liệu ở mức cơ bản, Backend có thể cần điều chỉnh lại cấu trúc JSON lần cuối để hoàn toàn phù hợp với cách Frontend (Next.js) mong đợi và hiển thị các thành phần chat. Mục tiêu là giữ định dạng này càng giống với cấu trúc dữ liệu mà Manus API (nếu có thể truy cập trực tiếp) trả về càng tốt, để Frontend có thể tái sử dụng logic hiển thị.
    *   Ví dụ: Frontend có thể cần một cấu trúc cụ thể để render các khối code với syntax highlighting hoặc các thành phần tương tác khác.

### 6.3. Đồng bộ Backend (FastAPI) -> Frontend (Next.js) qua WebSockets

*   **Thiết lập Kết nối:** Khi người dùng mở giao diện chat trên Frontend, một kết nối WebSocket sẽ được thiết lập và duy trì với Backend.
*   **Đẩy Dữ liệu Real-time:** Khi Backend đã xử lý xong phản hồi từ Manus (bước 6.2), nó sẽ sử dụng kết nối WebSocket đang mở của người dùng tương ứng để đẩy (push) dữ liệu đã định dạng về Frontend.
*   **FastAPI WebSocket Support:** FastAPI cung cấp hỗ trợ tốt cho WebSockets, cho phép quản lý các kết nối và gửi/nhận tin nhắn dễ dàng.

```python
# Ví dụ đơn giản về WebSocket endpoint trong FastAPI
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from typing import List, Dict

app = FastAPI()

class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        self.active_connections[client_id] = websocket

    def disconnect(self, client_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]

    async def send_personal_message(self, message: dict, client_id: str):
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id]
            await websocket.send_json(message)

manager = ConnectionManager()

@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    await manager.connect(websocket, client_id)
    try:
        while True:
            # Lắng nghe tin nhắn từ Frontend (ví dụ: người dùng gửi tin nhắn mới)
            data = await websocket.receive_text()
            # Xử lý tin nhắn từ frontend, ví dụ gửi tới Playwright Worker
            # ... (logic điều phối tới Playwright)
            # Giả sử đây là nơi nhận phản hồi từ Worker và gửi lại FE
            # response_from_manus = await get_response_from_worker(client_id, data)
            # await manager.send_personal_message(response_from_manus, client_id)
            pass # Trong ví dụ này, chủ yếu chờ để gửi từ nơi khác
    except WebSocketDisconnect:
        manager.disconnect(client_id)
        print(f"Client #{client_id} disconnected")

# Endpoint giả định để Playwright Worker gọi khi có phản hồi
@app.post("/api/v1/manus_response/{client_id}")
async def receive_manus_response(client_id: str, response_data: dict):
    # response_data là JSON từ Playwright Worker
    print(f"Received response for {client_id}: {response_data}")
    # Xử lý/định dạng lại nếu cần
    formatted_data = response_data # Giả sử không cần đổi
    # Gửi tới client_id cụ thể qua WebSocket
    await manager.send_personal_message(formatted_data, client_id)
    return {"status": "received"}

```

### 6.4. Hiển thị trên Frontend (Next.js)

*   Frontend lắng nghe các tin nhắn đến trên kết nối WebSocket.
*   Khi nhận được dữ liệu JSON chứa phản hồi từ Manus (đã được Backend xử lý và đẩy về), Frontend sẽ cập nhật trạng thái (state) của ứng dụng.
*   Việc cập nhật state sẽ kích hoạt re-render các thành phần React/Next.js, hiển thị tin nhắn mới trong giao diện chat cho người dùng.
*   Logic hiển thị trên Frontend cần được thiết kế để xử lý các `parts` khác nhau trong dữ liệu (văn bản, code, danh sách, v.v.) và render chúng một cách phù hợp, giống với trải nghiệm trên giao diện gốc của Manus.

*(Phần tiếp theo sẽ tổng hợp tài liệu và kiểm tra)*



## 7. Tổng kết và Lưu ý

Hệ thống được mô tả ở trên cung cấp một giải pháp toàn diện để tích hợp khả năng tương tác với Manus vào ứng dụng của bạn thông qua việc crawl giao diện web. Tuy nhiên, cần lưu ý một số điểm quan trọng:

*   **Tính ổn định của Giao diện Manus:** Giải pháp này phụ thuộc rất nhiều vào cấu trúc DOM và hoạt động của giao diện chat Manus. Bất kỳ thay đổi nào từ phía Manus (cập nhật UI, thay đổi selector, cơ chế tải tin nhắn) đều có thể làm hỏng logic crawl của Playwright. Cần có cơ chế giám sát và quy trình cập nhật script Playwright thường xuyên.
*   **Hiệu suất:** Việc chạy nhiều instance Playwright (nếu cần hỗ trợ nhiều người dùng đồng thời) có thể tốn nhiều tài nguyên (CPU, RAM). Cần tối ưu hóa script Playwright và cân nhắc kiến trúc hạ tầng phù hợp.
*   **Điều khoản Sử dụng:** Luôn kiểm tra và tuân thủ điều khoản sử dụng của Manus liên quan đến việc tự động hóa tương tác hoặc crawl dữ liệu.
*   **Xử lý CAPTCHA và Biện pháp Chống Bot:** Manus có thể triển khai các biện pháp chống bot (như CAPTCHA). Hệ thống cần có khả năng phát hiện và xử lý (hoặc thông báo cho người dùng/quản trị viên) các trường hợp này, mặc dù việc tự động giải CAPTCHA rất phức tạp và thường không đáng tin cậy.
*   **Thay thế bằng API (Nếu có):** Nếu Manus cung cấp API chính thức trong tương lai, việc chuyển sang sử dụng API sẽ là giải pháp ổn định, hiệu quả và đáng tin cậy hơn nhiều so với việc crawl giao diện.

Bằng cách xem xét cẩn thận các yêu cầu, kiến trúc, quy trình và các lưu ý này, bạn có thể xây dựng một hệ thống crawl thời gian thực hiệu quả để tích hợp Manus vào ứng dụng của mình.

