#!/usr/bin/env python3
"""
Playwright Web Data Extractor

This module provides functionality to extract content from web pages in both text and HTML formats
using Playwright. It's designed to support real-time data streaming to frontend applications.

Requirements:
- playwright
- asyncio

Installation:
pip install playwright
playwright install chromium
"""

import asyncio
import json
import time
import logging
from typing import Dict, Any, Optional, Tuple, Union
from urllib.parse import urlparse

# Import Playwright components
try:
    from playwright.async_api import async_playwright, <PERSON>, Error as PlaywrightError, TimeoutError
except ImportError:
    raise ImportError("Playwright is required. Install it with: pip install playwright")

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('playwright_extractor')

async def extract_web_data(
    url: str, 
    timeout: int = 5000, 
    wait_until: str = "domcontentloaded",
    selector: str = "body"
) -> Dict[str, Any]:
    """
    Extract both text content and HTML from a web page using Playwright.
    
    Args:
        url: The URL to extract data from
        timeout: Maximum time to wait for page load in milliseconds
        wait_until: When to consider navigation successful ('domcontentloaded', 'load', 'networkidle')
        selector: CSS selector to extract content from (default: 'body')
        
    Returns:
        Dictionary containing:
        {
            "status": "success" or "error",
            "content": Text content of the page or None if error,
            "html": HTML content of the page or None if error,
            "error": Error message if status is "error", otherwise None
        }
    """
    result = {
        "status": "success",
        "content": None,
        "html": None,
        "error": None
    }
    
    # Validate URL format
    try:
        parsed_url = urlparse(url)
        if not parsed_url.scheme or not parsed_url.netloc:
            result["status"] = "error"
            result["error"] = f"Invalid URL format: {url}"
            return result
    except Exception as e:
        result["status"] = "error"
        result["error"] = f"URL parsing error: {str(e)}"
        return result
    
    # Initialize playwright
    async with async_playwright() as playwright:
        browser = None
        try:
            # Launch browser
            browser = await playwright.chromium.launch(headless=True)
            context = await browser.new_context(
                viewport={"width": 1280, "height": 800},
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            )
            
            # Create new page and navigate to URL
            page = await context.new_page()
            
            # Set timeout for navigation
            page.set_default_timeout(timeout)
            
            # Navigate to the URL
            await page.goto(url, wait_until=wait_until)
            
            # Extract text content
            content = await page.content()
            text_content = await page.inner_text(selector)
            
            # Populate result
            result["content"] = text_content
            result["html"] = content
            
        except TimeoutError as e:
            result["status"] = "error"
            result["error"] = f"Lỗi khi tải trang {url}: {str(e)}"
            logger.error(f"Timeout error: {str(e)}")
            
        except PlaywrightError as e:
            result["status"] = "error"
            result["error"] = f"Lỗi Playwright khi xử lý {url}: {str(e)}"
            logger.error(f"Playwright error: {str(e)}")
            
        except Exception as e:
            result["status"] = "error"
            result["error"] = f"Lỗi không xác định khi xử lý {url}: {str(e)}"
            logger.error(f"Unexpected error: {str(e)}")
            
        finally:
            # Close browser if it was opened
            if browser:
                await browser.close()
                
    return result

async def extract_web_data_realtime(
    url: str,
    websocket_send_callback,
    interval: float = 0.5,
    max_duration: int = 30,
    timeout: int = 5000
) -> None:
    """
    Extract web data in real-time and send updates through WebSocket.
    
    Args:
        url: The URL to extract data from
        websocket_send_callback: Callback function to send data through WebSocket
        interval: Interval between updates in seconds
        max_duration: Maximum duration to run the extraction in seconds
        timeout: Maximum time to wait for page load in milliseconds
    """
    start_time = time.time()
    update_count = 0
    
    async with async_playwright() as playwright:
        browser = None
        try:
            # Launch browser
            browser = await playwright.chromium.launch(headless=True)
            context = await browser.new_context()
            page = await context.new_page()
            page.set_default_timeout(timeout)
            
            # Initial navigation
            await page.goto(url, wait_until="domcontentloaded")
            
            # Send initial data
            initial_data = {
                "type": "realtime_update",
                "data": {
                    "content": await page.inner_text("body"),
                    "html": await page.content(),
                    "update_count": update_count,
                    "elapsed_time": 0
                }
            }
            await websocket_send_callback(json.dumps(initial_data))
            update_count += 1
            
            # Continue monitoring for changes
            while time.time() - start_time < max_duration:
                await asyncio.sleep(interval)
                
                # Get current content
                current_content = await page.inner_text("body")
                current_html = await page.content()
                elapsed_time = time.time() - start_time
                
                # Send update
                update_data = {
                    "type": "realtime_update",
                    "data": {
                        "content": current_content,
                        "html": current_html,
                        "update_count": update_count,
                        "elapsed_time": elapsed_time
                    }
                }
                await websocket_send_callback(json.dumps(update_data))
                update_count += 1
                
            # Send completion notification
            completion_data = {
                "type": "extraction_completed",
                "data": {
                    "content": await page.inner_text("body"),
                    "html": await page.content(),
                    "update_count": update_count,
                    "elapsed_time": time.time() - start_time,
                    "message": "Extraction completed successfully"
                }
            }
            await websocket_send_callback(json.dumps(completion_data))
            
        except Exception as e:
            error_data = {
                "type": "extraction_error",
                "data": {
                    "error": str(e),
                    "elapsed_time": time.time() - start_time
                }
            }
            await websocket_send_callback(json.dumps(error_data))
            logger.error(f"Error during realtime extraction: {str(e)}")
            
        finally:
            if browser:
                await browser.close()

# Example usage
async def main():
    # Test the extraction function
    print("Đang trích xuất từ: https://httpbin.org/html")
    result = await extract_web_data("https://httpbin.org/html")
    print("--- Kết quả thành công ---")
    print(f"Status: {result['status']}")
    print(f"Content (đầu): {result['content'][:150]}...")
    print(f"HTML (đầu): {result['html'][:150]}...")
    print(f"Error: {result['error']}")
    print("-------------------------")
    
    # Test with invalid URL
    print("Đang trích xuất từ: invalid-url")
    result = await extract_web_data("invalid-url")
    print("--- Kết quả lỗi (URL không hợp lệ) ---")
    print(f"Status: {result['status']}")
    print(f"Content: {result['content']}")
    print(f"HTML: {result['html']}")
    print(f"Error: {result['error']}")
    print("-------------------------")
    
    # Test with timeout
    print("Đang trích xuất từ: https://httpbin.org/delay/10 (dự kiến timeout)")
    result = await extract_web_data("https://httpbin.org/delay/10")
    print("--- Kết quả lỗi (Timeout) ---")
    print(f"Status: {result['status']}")
    print(f"Content: {result['content']}")
    print(f"HTML: {result['html']}")
    print(f"Error: {result['error']}")
    print("-------------------------")

if __name__ == "__main__":
    asyncio.run(main())