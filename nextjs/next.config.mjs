/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  reactStrictMode: true,
  output: 'standalone',
  env: {
    NEXT_PUBLIC_WEBSOCKET_URL: process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'ws://localhost:8000/ws/minus-chat',
    NEXT_PUBLIC_API_BASE_URL: process.env.API_BASE_URL || 'http://localhost:8000'
  },
  // C<PERSON><PERSON> hình webpack để hỗ trợ hot reload tốt hơn
  webpack: (config, { isServer }) => {
    // C<PERSON>u hình cho NextJS tương thích với Docker tốt hơn
    config.watchOptions = {
      poll: 1000,
      aggregateTimeout: 300,
    }
    return config
  }
}

export default nextConfig
