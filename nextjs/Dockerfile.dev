FROM node:22-alpine

# <PERSON><PERSON><PERSON> mục làm việc trong container
WORKDIR /app

# Sao chép package.json và package-lock.json (nếu có)
COPY package.json ./

# Cài đặt phụ thuộc với --legacy-peer-deps để bỏ qua kiểm tra xung đột
RUN npm install --legacy-peer-deps
RUN npm install -D @types/ws --legacy-peer-deps

# Sao chép tất cả các file còn lại
COPY . .

# Expose cổng 3000
EXPOSE 3000

# Khởi chạy ứng dụng trong chế độ phát triển
CMD ["npm", "run", "dev"]

