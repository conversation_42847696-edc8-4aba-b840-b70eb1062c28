import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ status: 'error', message: '<PERSON><PERSON><PERSON><PERSON> thức không được hỗ trợ' });
  }

  try {
    const response = await fetch('http://backend:8000/setup-chrome-profile', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(req.body),
    });

    const data = await response.json();
    return res.status(response.status).json(data);
  } catch (error) {
    console.error('Lỗi khi gọi API backend:', error);
    return res.status(500).json({ 
      status: 'error', 
      message: 'Lỗi khi kết nối với backend',
      error: error.message
    });
  }
} 