"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Paperclip, Mic, Send, FileText, FileIcon as FilePdf, ChevronDown, ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"
import DocumentViewer from "@/components/document-viewer"
import PDFViewer from "@/components/pdf-viewer"
import { useWebSocket } from "@/contexts/WebSocketContext"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { Clock, AlertCircle } from "lucide-react"

interface ChatInterfaceProps {
  selectedFile: string | null
}

interface Message {
  id: number
  content: string
  sender: "user" | "assistant"
  timestamp: string
  attachments?: Array<{
    id: string
    name: string
    type: "markdown" | "pdf"
  }>
}

export default function ChatInterface({ selectedFile }: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      content:
        "Xin chào! Tôi đã hoàn thành tài liệu chi tiết về Detectron2 và đính kèm cả phiên bản Markdown và PDF để bạn có thể sử dụng theo nhu cầu.",
      sender: "assistant",
      timestamp: "10:30",
      attachments: [
        {
          id: "markdown",
          name: "Detectron2: Nền tảng thị giác máy tính",
          type: "markdown",
        },
        {
          id: "pdf",
          name: "detectron2_document.pdf",
          type: "pdf",
        },
      ],
    },
  ])

  const [inputValue, setInputValue] = useState("")
  const [showAttachment, setShowAttachment] = useState<string | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const {
    isConnected,
    status,
    formattedElapsedTime,
    connect,
    sendMessage
  } = useWebSocket()

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages, showAttachment])

  useEffect(() => {
    if (!isConnected) {
      connect()
    }
  }, [isConnected, connect])

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault()

    if (!inputValue.trim()) return

    const requestData = {
      chat_content: inputValue,
      profile_name: "default_profile",
      headless: true,
      use_stealth: true,
      file_context: selectedFile
    }

    sendMessage(requestData)
    setInputValue("")
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage(e)
    }
  }

  return (
    <div className="flex h-full flex-col">
      <Tabs defaultValue="chat" className="h-full">
        <div className="border-b border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-900">
          <TabsList className="h-12 bg-transparent">
            <TabsTrigger
              value="chat"
              className="data-[state=active]:bg-zinc-100 data-[state=active]:text-zinc-900 dark:data-[state=active]:bg-zinc-800 dark:data-[state=active]:text-zinc-100"
            >
              Chat
            </TabsTrigger>
            <TabsTrigger
              value="files"
              className="data-[state=active]:bg-zinc-100 data-[state=active]:text-zinc-900 dark:data-[state=active]:bg-zinc-800 dark:data-[state=active]:text-zinc-100"
            >
              Files
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="chat" className="h-[calc(100%-3rem)] flex flex-col data-[state=inactive]:hidden">
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-4">
              {messages.map((message) => (
                <div key={message.id}>
                  <div className={cn("flex", message.sender === "user" ? "justify-end" : "justify-start")}>
                    <div className="flex max-w-[80%] gap-3">
                      {message.sender === "assistant" && (
                        <Avatar className="h-8 w-8">
                          <AvatarImage src="/placeholder.svg?height=32&width=32" />
                          <AvatarFallback className="bg-teal-600 text-white">A</AvatarFallback>
                        </Avatar>
                      )}
                      <div>
                        <div
                          className={cn(
                            "rounded-lg p-4",
                            message.sender === "user"
                              ? "bg-teal-600 text-white"
                              : "bg-white text-zinc-900 shadow-sm dark:bg-zinc-800 dark:text-zinc-100",
                          )}
                        >
                          <p>{message.content}</p>
                        </div>
                        <p className="mt-1 text-xs text-zinc-500 dark:text-zinc-400">{message.timestamp}</p>
                      </div>
                      {message.sender === "user" && (
                        <Avatar className="h-8 w-8">
                          <AvatarImage src="/placeholder.svg?height=32&width=32" />
                          <AvatarFallback className="bg-zinc-300 text-zinc-800 dark:bg-zinc-700 dark:text-zinc-300">
                            U
                          </AvatarFallback>
                        </Avatar>
                      )}
                    </div>
                  </div>

                  {message.attachments && message.attachments.length > 0 && (
                    <div className={cn("mt-2", message.sender === "user" ? "text-right" : "text-left")}>
                      <div
                        className={cn(
                          "inline-block rounded-lg border p-2",
                          message.sender === "user"
                            ? "border-teal-200 bg-teal-50 dark:border-teal-800 dark:bg-teal-900/20"
                            : "border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-800",
                        )}
                      >
                        <div className="mb-1 px-2 text-xs font-medium text-zinc-500 dark:text-zinc-400">
                          Attached Files
                        </div>
                        {message.attachments.map((attachment) => (
                          <div key={attachment.id} className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-auto w-full justify-start gap-2 px-2 py-1 text-left"
                              onClick={() => setShowAttachment(showAttachment === attachment.id ? null : attachment.id)}
                            >
                              {attachment.type === "markdown" ? (
                                <FileText className="h-4 w-4 text-blue-500" />
                              ) : (
                                <FilePdf className="h-4 w-4 text-red-500" />
                              )}
                              <span className="flex-1 truncate">{attachment.name}</span>
                              {showAttachment === attachment.id ? (
                                <ChevronDown className="h-4 w-4" />
                              ) : (
                                <ChevronRight className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {message.attachments &&
                    message.attachments.map(
                      (attachment) =>
                        showAttachment === attachment.id && (
                          <div
                            key={`preview-${attachment.id}`}
                            className="mt-2 overflow-hidden rounded-lg border border-zinc-200 dark:border-zinc-700"
                          >
                            <div className="h-[400px] md:h-[500px]">
                              {attachment.type === "markdown" ? <DocumentViewer type="markdown" /> : <PDFViewer />}
                            </div>
                          </div>
                        ),
                    )}
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          <div className="border-t border-zinc-200 bg-white p-4 dark:border-zinc-700 dark:bg-zinc-900">
            <div className="flex items-center gap-2">
              <Button variant="outline" size="icon" className="shrink-0">
                <Paperclip className="h-5 w-5" />
              </Button>
              <Textarea
                placeholder="Type a message..."
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                className="min-h-[44px] resize-none border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-800"
              />
              <Button variant="outline" size="icon" className="shrink-0 md:hidden">
                <Mic className="h-5 w-5" />
              </Button>
              <Button
                className="shrink-0 gap-2 bg-teal-600 hover:bg-teal-700 dark:bg-teal-600 dark:hover:bg-teal-700"
                onClick={handleSendMessage}
                disabled={!inputValue.trim()}
              >
                <Send className="h-4 w-4" />
                <span className="hidden sm:inline">Send</span>
              </Button>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="files" className="h-[calc(100%-3rem)] data-[state=inactive]:hidden">
          <ScrollArea className="h-full">
            <div className="p-6">
              <h3 className="mb-4 text-lg font-medium">Attached Files</h3>
              <div className="space-y-4">
                <div className="rounded-lg border border-zinc-200 bg-white p-4 transition-colors hover:bg-zinc-50 dark:border-zinc-700 dark:bg-zinc-800 dark:hover:bg-zinc-700/50">
                  <div className="flex items-center gap-3">
                    <FileText className="h-8 w-8 text-blue-500" />
                    <div className="flex-1">
                      <h4 className="font-medium">Detectron2: Nền tảng thị giác máy tính</h4>
                      <p className="text-sm text-zinc-500 dark:text-zinc-400">Markdown · 14.60 KB</p>
                    </div>
                    <Button variant="outline" size="sm" className="gap-2">
                      <FileText className="h-4 w-4" />
                      View
                    </Button>
                  </div>
                </div>

                <div className="rounded-lg border border-zinc-200 bg-white p-4 transition-colors hover:bg-zinc-50 dark:border-zinc-700 dark:bg-zinc-800 dark:hover:bg-zinc-700/50">
                  <div className="flex items-center gap-3">
                    <FilePdf className="h-8 w-8 text-red-500" />
                    <div className="flex-1">
                      <h4 className="font-medium">detectron2_document.pdf</h4>
                      <p className="text-sm text-zinc-500 dark:text-zinc-400">PDF · 318.01 KB</p>
                    </div>
                    <Button variant="outline" size="sm" className="gap-2">
                      <FilePdf className="h-4 w-4" />
                      View
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </ScrollArea>
        </TabsContent>
      </Tabs>

      <div className="p-4 border-t dark:border-zinc-700">
        <form onSubmit={handleSendMessage} className="flex gap-2">
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="Nhập câu hỏi hoặc yêu cầu..."
            disabled={status.status === 'processing'}
            className="flex-1"
          />
          <Button type="submit" disabled={status.status === 'processing' || !inputValue.trim()}>
            <Send className="h-4 w-4" />
            <span className="ml-2 hidden md:inline">Gửi</span>
          </Button>
        </form>

        <div className="mt-2 text-xs text-zinc-500 dark:text-zinc-400 flex items-center gap-1">
          <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
          {isConnected ? 'Kết nối với Manus AI' : 'Mất kết nối với Manus AI'}
        </div>
      </div>
    </div>
  )
}
