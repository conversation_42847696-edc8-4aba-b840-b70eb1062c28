"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Textarea } from "@/components/ui/textarea"
import { ArrowU<PERSON>, <PERSON>c<PERSON> } from "lucide-react"
import { cn } from "@/lib/utils"

export default function DemoChat() {
  const [messages, setMessages] = useState([
    {
      id: 1,
      role: "assistant",
      content: "Hello! I'm manus, your AI assistant. How can I help you today?",
      timestamp: "Just now",
    },
  ])
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const handleSendMessage = () => {
    if (!input.trim()) return

    // Add user message
    const userMessage = {
      id: messages.length + 1,
      role: "user",
      content: input,
      timestamp: "Just now",
    }

    setMessages((prev) => [...prev, userMessage])
    setInput("")
    setIsLoading(true)

    // Simulate AI response
    setTimeout(() => {
      let response

      if (input.toLowerCase().includes("hello") || input.toLowerCase().includes("hi")) {
        response = "Hello! It's nice to meet you. How can I assist you today?"
      } else if (input.toLowerCase().includes("help")) {
        response =
          "I can help with a variety of tasks including answering questions, generating code, assisting with research, and more. What specific help do you need?"
      } else if (input.toLowerCase().includes("weather")) {
        response =
          "I don't have real-time weather data in this demo, but in the full version I can provide current weather information for any location."
      } else if (input.toLowerCase().includes("code") || input.toLowerCase().includes("programming")) {
        response =
          "I can help with coding tasks! I can generate code snippets, explain programming concepts, or help debug issues. What programming language are you working with?"
      } else {
        response =
          "Thank you for your message. In the full version, I would provide a detailed response to your query. Is there anything specific you'd like to know about manus?"
      }

      const assistantMessage = {
        id: messages.length + 2,
        role: "assistant",
        content: response,
        timestamp: "Just now",
      }

      setMessages((prev) => [...prev, assistantMessage])
      setIsLoading(false)
    }, 1500)
  }

  const handleKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <div className="flex h-[500px] flex-col">
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-4">
          {messages.map((message) => (
            <div key={message.id} className={cn("flex", message.role === "user" ? "justify-end" : "justify-start")}>
              <div className="flex max-w-[80%] gap-3">
                {message.role === "assistant" && (
                  <Avatar className="h-8 w-8">
                    <AvatarImage src="/placeholder.svg?height=32&width=32" />
                    <AvatarFallback className="bg-teal-600 text-white">M</AvatarFallback>
                  </Avatar>
                )}
                <div>
                  <div
                    className={cn(
                      "rounded-lg p-3",
                      message.role === "user" ? "bg-teal-600 text-white" : "bg-zinc-800 text-zinc-100",
                    )}
                  >
                    <p>{message.content}</p>
                  </div>
                  <p className="mt-1 text-xs text-zinc-500">{message.timestamp}</p>
                </div>
                {message.role === "user" && (
                  <Avatar className="h-8 w-8">
                    <AvatarImage src="/placeholder.svg?height=32&width=32" />
                    <AvatarFallback className="bg-zinc-700 text-zinc-300">U</AvatarFallback>
                  </Avatar>
                )}
              </div>
            </div>
          ))}
          {isLoading && (
            <div className="flex justify-start">
              <div className="flex max-w-[80%] gap-3">
                <Avatar className="h-8 w-8">
                  <AvatarImage src="/placeholder.svg?height=32&width=32" />
                  <AvatarFallback className="bg-teal-600 text-white">M</AvatarFallback>
                </Avatar>
                <div>
                  <div className="rounded-lg bg-zinc-800 p-3 text-zinc-100">
                    <div className="flex space-x-2">
                      <div className="h-2 w-2 animate-bounce rounded-full bg-zinc-400"></div>
                      <div
                        className="h-2 w-2 animate-bounce rounded-full bg-zinc-400"
                        style={{ animationDelay: "0.2s" }}
                      ></div>
                      <div
                        className="h-2 w-2 animate-bounce rounded-full bg-zinc-400"
                        style={{ animationDelay: "0.4s" }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      <div className="border-t border-zinc-800 p-4">
        <div className="flex items-end gap-2">
          <Button variant="ghost" size="icon" className="h-9 w-9 rounded-full text-zinc-400 hover:text-zinc-100">
            <Paperclip className="h-5 w-5" />
          </Button>
          <Textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Type a message..."
            className="min-h-[44px] flex-1 resize-none border-zinc-700 bg-zinc-800 text-zinc-100 placeholder:text-zinc-500 focus-visible:ring-1 focus-visible:ring-teal-500"
          />
          <Button
            onClick={handleSendMessage}
            disabled={!input.trim() || isLoading}
            size="icon"
            className="h-9 w-9 rounded-full bg-teal-600 hover:bg-teal-700"
          >
            <ArrowUp className="h-5 w-5" />
          </Button>
        </div>
        <p className="mt-2 text-center text-xs text-zinc-500">
          This is a demo. Try asking about manus features or capabilities.
        </p>
      </div>
    </div>
  )
}
