"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>lide<PERSON> } from "@/components/ui/slider"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  ChevronLeft,
  ChevronRight,
  ZoomIn,
  ZoomOut,
  Download,
  Maximize2,
  RotateCw,
  Search,
  Printer,
} from "lucide-react"
import { cn } from "@/lib/utils"

interface PDFViewerProps {
  className?: string
}

export default function PDFViewer({ className }: PDFViewerProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages] = useState(12) // In a real app, this would be determined from the PDF
  const [zoom, setZoom] = useState(100)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const viewerRef = useRef<HTMLDivElement>(null)

  const handleZoomIn = () => {
    setZoom((prev) => Math.min(prev + 10, 200))
  }

  const handleZoomOut = () => {
    setZoom((prev) => Math.max(prev - 10, 50))
  }

  const handlePrevPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1))
  }

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages))
  }

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      viewerRef.current?.requestFullscreen().catch((err) => {
        console.error(`Error attempting to enable fullscreen: ${err.message}`)
      })
    } else {
      document.exitFullscreen()
    }
  }

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener("fullscreenchange", handleFullscreenChange)
    return () => {
      document.removeEventListener("fullscreenchange", handleFullscreenChange)
    }
  }, [])

  // This would be the actual PDF content in a real implementation
  const renderPDFPage = () => {
    return (
      <div className="flex flex-col items-center justify-center">
        <div
          className="relative bg-white shadow-md"
          style={{
            width: `${8.5 * (zoom / 100)}in`,
            height: `${11 * (zoom / 100)}in`,
            maxWidth: "100%",
          }}
        >
          {/* PDF page content */}
          <div className="absolute inset-0 p-8">
            <h1 className="mb-6 text-2xl font-bold">Detectron2: Nền tảng thị giác máy tính tiên tiến của Meta AI</h1>

            {currentPage === 1 && (
              <>
                <h2 className="mb-4 text-xl font-semibold">Giới thiệu</h2>
                <p className="mb-4 text-sm">
                  Detectron2 là nền tảng thế hệ tiếp theo được phát triển bởi Facebook AI Research (FAIR) thuộc Meta AI,
                  cung cấp các thuật toán phát hiện và phân đoạn đối tượng tiên tiến. Đây là phiên bản kế nhiệm của
                  Detectron và maskrcnn-benchmark, được thiết kế để hỗ trợ triển khai nhanh chóng và đánh giá các nghiên
                  cứu thị giác máy tính mới.
                </p>
                <p className="mb-4 text-sm">
                  Detectron2 được xây dựng trên nền tảng PyTorch, được coi là một trong những thư viện nhận diện đối
                  tượng mô-đun hóa hàng đầu hiện nay. Mặc dù ban đầu được phát triển để đáp ứng nhu cầu nghiên cứu của
                  Facebook AI, Detectron2 đã được cộng đồng phát triển áp dụng rộng rãi và có sẵn cho các nhóm công khai
                  thông qua giấy phép Apache 2.0.
                </p>
                <blockquote className="my-4 border-l-4 border-gray-300 pl-4 text-sm italic">
                  "Mục tiêu của chúng tôi với Detectron2 là hỗ trợ phạm vi rộng các mô hình phát hiện và phân đoạn đối
                  tượng tiên tiến hiện có, đồng thời phục vụ bối cảnh nghiên cứu tiên tiến luôn thay đổi."
                </blockquote>
              </>
            )}

            {currentPage === 2 && (
              <>
                <h2 className="mb-4 text-xl font-semibold">Lịch sử và nguồn gốc</h2>
                <p className="mb-4 text-sm">
                  Detectron2 là sự tiếp nối của dự án Detectron ban đầu, nhưng với nhiều cải tiến đáng kể. Trong khi
                  Detectron ban đầu được xây dựng trên Caffe2, Detectron2 được viết lại hoàn toàn bằng PyTorch. Sự thay
                  đổi này phản ánh xu hướng chung trong cộng đồng học máy, khi PyTorch ngày càng được ưa chuộng hơn nhờ
                  tính linh hoạt và dễ sử dụng.
                </p>
                <p className="mb-4 text-sm">
                  Các nhóm phát triển đã lưu ý rằng Caffe2 và PyTorch hiện đã hợp nhất, làm cho sự khác biệt về nền tảng
                  không còn quá quan trọng. Tuy nhiên, việc chuyển sang PyTorch đã mang lại nhiều lợi ích, bao gồm khả
                  năng tích hợp tốt hơn với hệ sinh thái PyTorch rộng lớn và cải thiện đáng kể về tốc độ huấn luyện.
                </p>
              </>
            )}

            {currentPage === 3 && (
              <>
                <h2 className="mb-4 text-xl font-semibold">Kiến trúc và thành phần</h2>
                <p className="mb-4 text-sm">
                  Detectron2 có kiến trúc mô-đun hóa cao, cho phép các nhà nghiên cứu và nhà phát triển dễ dàng thử
                  nghiệm với các kiến trúc mô hình, hàm mất mát và kỹ thuật huấn luyện khác nhau. Ba cấu trúc chính
                  trong kiến trúc Detectron2 bao gồm:
                </p>
                <h3 className="mb-2 text-lg font-medium">1. Backbone Network (Mạng xương sống)</h3>
                <p className="mb-4 text-sm">
                  Mạng backbone Detectron2 trích xuất bản đồ đặc trưng ở các tỷ lệ khác nhau từ hình ảnh đầu vào.
                  Detectron2 cung cấp một số tùy chọn backbone, bao gồm:
                </p>
                <ul className="mb-4 list-disc pl-6 text-sm">
                  <li>ResNet</li>
                  <li>ResNeXt</li>
                  <li>MobileNet</li>
                </ul>
              </>
            )}

            {/* Page number */}
            <div className="absolute bottom-4 right-4 text-xs text-gray-500">{currentPage}</div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div ref={viewerRef} className={cn("flex h-full flex-col", className)}>
      {/* Toolbar */}
      <div className="flex items-center justify-between border-b border-zinc-200 bg-white p-2 dark:border-zinc-700 dark:bg-zinc-900">
        <div className="flex items-center gap-1">
          <Button variant="ghost" size="icon" onClick={handlePrevPage} disabled={currentPage <= 1}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <div className="flex min-w-[80px] items-center justify-center text-sm">
            <span>
              {currentPage} / {totalPages}
            </span>
          </div>
          <Button variant="ghost" size="icon" onClick={handleNextPage} disabled={currentPage >= totalPages}>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        <div className="hidden items-center gap-1 md:flex">
          <Button variant="ghost" size="icon" onClick={handleZoomOut} disabled={zoom <= 50}>
            <ZoomOut className="h-4 w-4" />
          </Button>
          <div className="flex w-32 items-center gap-2">
            <Slider
              value={[zoom]}
              min={50}
              max={200}
              step={10}
              onValueChange={(value) => setZoom(value[0])}
              className="w-24"
            />
            <span className="text-xs">{zoom}%</span>
          </div>
          <Button variant="ghost" size="icon" onClick={handleZoomIn} disabled={zoom >= 200}>
            <ZoomIn className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex items-center gap-1">
          <Button variant="ghost" size="icon" className="hidden md:flex">
            <Search className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" className="hidden md:flex">
            <Printer className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" className="hidden md:flex">
            <RotateCw className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon">
            <Download className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" onClick={toggleFullscreen}>
            <Maximize2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* PDF Content */}
      <ScrollArea className="flex-1 bg-zinc-100 dark:bg-zinc-800">
        <div className="flex min-h-full items-start justify-center p-4">{renderPDFPage()}</div>
      </ScrollArea>
    </div>
  )
}
