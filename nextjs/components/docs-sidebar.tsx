import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search } from "lucide-react"

export default function DocsSidebar() {
  return (
    <div className="border-r border-zinc-800 p-4">
      <div className="relative mb-4">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-zinc-500" />
        <Input
          type="search"
          placeholder="Search documentation..."
          className="w-full border-zinc-700 bg-zinc-800 pl-9 text-zinc-100 placeholder:text-zinc-500"
        />
      </div>

      <nav className="space-y-6">
        <div>
          <h3 className="mb-2 text-sm font-semibold text-zinc-400">Getting Started</h3>
          <ul className="space-y-1 text-sm">
            <li>
              <Button
                variant="ghost"
                className="w-full justify-start font-medium text-teal-500 hover:bg-zinc-800 hover:text-teal-400"
              >
                Introduction
              </Button>
            </li>
            <li>
              <Button
                variant="ghost"
                className="w-full justify-start text-zinc-300 hover:bg-zinc-800 hover:text-zinc-100"
              >
                Installation
              </Button>
            </li>
            <li>
              <Button
                variant="ghost"
                className="w-full justify-start text-zinc-300 hover:bg-zinc-800 hover:text-zinc-100"
              >
                Quick Start
              </Button>
            </li>
            <li>
              <Button
                variant="ghost"
                className="w-full justify-start text-zinc-300 hover:bg-zinc-800 hover:text-zinc-100"
              >
                Configuration
              </Button>
            </li>
          </ul>
        </div>

        <div>
          <h3 className="mb-2 text-sm font-semibold text-zinc-400">Core Concepts</h3>
          <ul className="space-y-1 text-sm">
            <li>
              <Button
                variant="ghost"
                className="w-full justify-start text-zinc-300 hover:bg-zinc-800 hover:text-zinc-100"
              >
                Architecture
              </Button>
            </li>
            <li>
              <Button
                variant="ghost"
                className="w-full justify-start text-zinc-300 hover:bg-zinc-800 hover:text-zinc-100"
              >
                Models
              </Button>
            </li>
            <li>
              <Button
                variant="ghost"
                className="w-full justify-start text-zinc-300 hover:bg-zinc-800 hover:text-zinc-100"
              >
                Prompting
              </Button>
            </li>
            <li>
              <Button
                variant="ghost"
                className="w-full justify-start text-zinc-300 hover:bg-zinc-800 hover:text-zinc-100"
              >
                Context Management
              </Button>
            </li>
          </ul>
        </div>

        <div>
          <h3 className="mb-2 text-sm font-semibold text-zinc-400">API Reference</h3>
          <ul className="space-y-1 text-sm">
            <li>
              <Button
                variant="ghost"
                className="w-full justify-start text-zinc-300 hover:bg-zinc-800 hover:text-zinc-100"
              >
                Client
              </Button>
            </li>
            <li>
              <Button
                variant="ghost"
                className="w-full justify-start text-zinc-300 hover:bg-zinc-800 hover:text-zinc-100"
              >
                Configuration
              </Button>
            </li>
            <li>
              <Button
                variant="ghost"
                className="w-full justify-start text-zinc-300 hover:bg-zinc-800 hover:text-zinc-100"
              >
                Response Types
              </Button>
            </li>
            <li>
              <Button
                variant="ghost"
                className="w-full justify-start text-zinc-300 hover:bg-zinc-800 hover:text-zinc-100"
              >
                Error Handling
              </Button>
            </li>
          </ul>
        </div>

        <div>
          <h3 className="mb-2 text-sm font-semibold text-zinc-400">Guides</h3>
          <ul className="space-y-1 text-sm">
            <li>
              <Button
                variant="ghost"
                className="w-full justify-start text-zinc-300 hover:bg-zinc-800 hover:text-zinc-100"
              >
                Authentication
              </Button>
            </li>
            <li>
              <Button
                variant="ghost"
                className="w-full justify-start text-zinc-300 hover:bg-zinc-800 hover:text-zinc-100"
              >
                Rate Limiting
              </Button>
            </li>
            <li>
              <Button
                variant="ghost"
                className="w-full justify-start text-zinc-300 hover:bg-zinc-800 hover:text-zinc-100"
              >
                Best Practices
              </Button>
            </li>
            <li>
              <Button
                variant="ghost"
                className="w-full justify-start text-zinc-300 hover:bg-zinc-800 hover:text-zinc-100"
              >
                Troubleshooting
              </Button>
            </li>
          </ul>
        </div>
      </nav>
    </div>
  )
}
