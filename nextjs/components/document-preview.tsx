"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"

interface DocumentPreviewProps {
  type: "markdown" | "pdf"
}

export default function DocumentPreview({ type }: DocumentPreviewProps) {
  const [content, setContent] = useState<string>("")
  const [activeTab, setActiveTab] = useState<string>("preview")

  useEffect(() => {
    // In a real app, you would fetch the actual content
    if (type === "markdown") {
      setContent(`# Detectron2: Nền tảng thị giác máy tính tiên tiến của Meta AI

## Giới thiệu

Detectron2 là nền tảng thế hệ tiếp theo được phát triển bởi Facebook AI Research (FAIR) thuộc Meta AI, cung cấp các thuật toán phát hiện và phân đoạn đối tượng tiên tiến. <PERSON><PERSON><PERSON> là phiên bản kế nhiệm của Detectron và maskrcnn-benchmark, đ<PERSON><PERSON><PERSON> thiết kế để hỗ trợ triển khai nhanh chóng và đánh giá các nghiên cứu thị giác máy tính mới.

Detectron2 được xây dựng trên nền tảng PyTorch, được coi là một trong những thư viện nhận diện đối tượng mô-đun hóa hàng đầu hiện nay. Mặc dù ban đầu được phát triển để đáp ứng nhu cầu nghiên cứu của Facebook AI, Detectron2 đã được cộng đồng phát triển áp dụng rộng rãi và có sẵn cho các nhóm công khai thông qua giấy phép Apache 2.0.

Đội phát triển Detectron2 đã tuyên bố rằng: "Mục tiêu của chúng tôi với Detectron2 là hỗ trợ phạm vi rộng các mô hình phát hiện và phân đoạn đối tượng tiên tiến hiện có, đồng thời phục vụ bối cảnh nghiên cứu tiên tiến luôn thay đổi."

## Lịch sử và nguồn gốc

Detectron2 là sự tiếp nối của dự án Detectron ban đầu, nhưng với nhiều cải tiến đáng kể. Trong khi Detectron ban đầu được xây dựng trên Caffe2, Detectron2 được viết lại hoàn toàn bằng PyTorch. Sự thay đổi này phản ánh xu hướng chung trong cộng đồng học máy, khi PyTorch ngày càng được ưa chuộng hơn nhờ tính linh hoạt và dễ sử dụng.`)
    }
  }, [type])

  if (type === "markdown") {
    return (
      <div className="h-full">
        <Tabs defaultValue="preview" className="h-full">
          <div className="border-b border-zinc-800">
            <TabsList className="h-10 bg-transparent">
              <TabsTrigger
                value="preview"
                className="data-[state=active]:bg-zinc-800/50 data-[state=active]:text-zinc-100"
                onClick={() => setActiveTab("preview")}
              >
                Preview
              </TabsTrigger>
              <TabsTrigger
                value="source"
                className="data-[state=active]:bg-zinc-800/50 data-[state=active]:text-zinc-100"
                onClick={() => setActiveTab("source")}
              >
                Source
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="preview" className="h-[calc(100%-2.5rem)] overflow-auto p-6 data-[state=inactive]:hidden">
            <div className="prose prose-invert max-w-none">
              <h1>Detectron2: Nền tảng thị giác máy tính tiên tiến của Meta AI</h1>

              <h2>Giới thiệu</h2>
              <p>
                Detectron2 là nền tảng thế hệ tiếp theo được phát triển bởi Facebook AI Research (FAIR) thuộc Meta AI,
                cung cấp các thuật toán phát hiện và phân đoạn đối tượng tiên tiến. Đây là phiên bản kế nhiệm của
                Detectron và maskrcnn-benchmark, được thiết kế để hỗ trợ triển khai nhanh chóng và đánh giá các nghiên
                cứu thị giác máy tính mới.
              </p>

              <p>
                Detectron2 được xây dựng trên nền tảng PyTorch, được coi là một trong những thư viện nhận diện đối tượng
                mô-đun hóa hàng đầu hiện nay. Mặc dù ban đầu được phát triển để đáp ứng nhu cầu nghiên cứu của Facebook
                AI, Detectron2 đã được cộng đồng phát triển áp dụng rộng rãi và có sẵn cho các nhóm công khai thông qua
                giấy phép Apache 2.0.
              </p>

              <blockquote>
                <p>
                  "Mục tiêu của chúng tôi với Detectron2 là hỗ trợ phạm vi rộng các mô hình phát hiện và phân đoạn đối
                  tượng tiên tiến hiện có, đồng thời phục vụ bối cảnh nghiên cứu tiên tiến luôn thay đổi."
                </p>
              </blockquote>

              <h2>Lịch sử và nguồn gốc</h2>
              <p>
                Detectron2 là sự tiếp nối của dự án Detectron ban đầu, nhưng với nhiều cải tiến đáng kể. Trong khi
                Detectron ban đầu được xây dựng trên Caffe2, Detectron2 được viết lại hoàn toàn bằng PyTorch. Sự thay
                đổi này phản ánh xu hướng chung trong cộng đồng học máy, khi PyTorch ngày càng được ưa chuộng hơn nhờ
                tính linh hoạt và dễ sử dụng.
              </p>
            </div>
          </TabsContent>

          <TabsContent value="source" className="h-[calc(100%-2.5rem)] overflow-auto p-6 data-[state=inactive]:hidden">
            <pre className="whitespace-pre-wrap text-sm text-zinc-300">{content}</pre>
          </TabsContent>
        </Tabs>
      </div>
    )
  }

  return (
    <div className="flex h-full flex-col items-center justify-center p-6">
      <div className="mb-4 text-6xl">📕</div>
      <h3 className="mb-2 text-xl font-medium">detectron2_document.pdf</h3>
      <p className="mb-4 text-sm text-zinc-400">318.01 KB · PDF Document</p>
      <div className="max-w-md text-center text-sm text-zinc-400">
        PDF preview is displayed here. In a real application, this would show an embedded PDF viewer with the actual
        document content.
      </div>
    </div>
  )
}
