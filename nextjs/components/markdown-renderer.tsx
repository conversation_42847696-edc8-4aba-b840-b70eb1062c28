"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Copy, Eye, Code, FileText } from 'lucide-react'

interface MarkdownRendererProps {
  content: string
  markdownContent?: string
  contentType?: string
  role?: string
  showPreview?: boolean
}

export function MarkdownRenderer({ 
  content, 
  markdownContent, 
  contentType = 'text',
  role = 'assistant',
  showPreview = true 
}: MarkdownRendererProps) {
  const [activeTab, setActiveTab] = useState('preview')
  const [copied, setCopied] = useState(false)

  const displayContent = markdownContent || content

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(displayContent)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  const renderMarkdown = (text: string) => {
    // Simple markdown rendering without external dependencies
    let html = text
      // Headers
      .replace(/^### (.*$)/gim, '<h3 class="text-lg font-semibold mt-4 mb-2">$1</h3>')
      .replace(/^## (.*$)/gim, '<h2 class="text-xl font-semibold mt-4 mb-2">$1</h2>')
      .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold mt-4 mb-2">$1</h1>')
      
      // Bold and italic
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
      
      // Code blocks
      .replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
        return `<div class="bg-gray-100 dark:bg-gray-800 rounded-md p-4 my-4 overflow-x-auto">
          <div class="flex items-center justify-between mb-2">
            <span class="text-xs text-gray-500 uppercase">${lang || 'code'}</span>
            <button onclick="navigator.clipboard.writeText('${code.replace(/'/g, "\\'")}'); this.textContent='Copied!'; setTimeout(() => this.textContent='Copy', 2000)" 
                    class="text-xs bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded hover:bg-gray-300 dark:hover:bg-gray-600">
              Copy
            </button>
          </div>
          <pre class="text-sm"><code class="language-${lang || ''}">${code}</code></pre>
        </div>`
      })
      
      // Inline code
      .replace(/`([^`]+)`/g, '<code class="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm font-mono">$1</code>')
      
      // Lists
      .replace(/^\d+\.\s+(.*)$/gim, '<li class="ml-4 mb-1">$1</li>')
      .replace(/^[-*]\s+(.*)$/gim, '<li class="ml-4 mb-1 list-disc">$1</li>')
      
      // Line breaks
      .replace(/\n/g, '<br>')

    // Wrap lists
    html = html.replace(/(<li.*?<\/li>)/gs, '<ul class="list-disc ml-4 mb-4">$1</ul>')

    return html
  }

  const getContentTypeIcon = () => {
    switch (contentType) {
      case 'code':
        return <Code className="w-4 h-4" />
      case 'list':
        return <FileText className="w-4 h-4" />
      case 'greeting':
        return <span className="text-lg">👋</span>
      case 'completion':
        return <span className="text-lg">✅</span>
      default:
        return <FileText className="w-4 h-4" />
    }
  }

  const getContentTypeBadge = () => {
    const variants: Record<string, string> = {
      code: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      list: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      greeting: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
      completion: 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200',
      text: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }

    return (
      <Badge className={`${variants[contentType] || variants.text} text-xs`}>
        <span className="flex items-center gap-1">
          {getContentTypeIcon()}
          {contentType}
        </span>
      </Badge>
    )
  }

  if (!showPreview) {
    return (
      <div className="prose dark:prose-invert max-w-none">
        <div dangerouslySetInnerHTML={{ __html: renderMarkdown(displayContent) }} />
      </div>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle className="text-sm font-medium">
              {role === 'assistant' ? 'Manus AI Response' : 
               role === 'user' ? 'Your Message' : 'System Message'}
            </CardTitle>
            {getContentTypeBadge()}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={copyToClipboard}
            className="h-8 w-8 p-0"
          >
            <Copy className="w-4 h-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="preview" className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Preview
            </TabsTrigger>
            <TabsTrigger value="raw" className="flex items-center gap-2">
              <Code className="w-4 h-4" />
              Raw
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="preview" className="mt-4">
            <div className="prose dark:prose-invert max-w-none">
              <div dangerouslySetInnerHTML={{ __html: renderMarkdown(displayContent) }} />
            </div>
          </TabsContent>
          
          <TabsContent value="raw" className="mt-4">
            <div className="bg-gray-50 dark:bg-gray-900 rounded-md p-4 overflow-x-auto">
              <pre className="text-sm whitespace-pre-wrap font-mono">
                {displayContent}
              </pre>
            </div>
          </TabsContent>
        </Tabs>
        
        {copied && (
          <div className="mt-2 text-sm text-green-600 dark:text-green-400">
            ✓ Copied to clipboard!
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default MarkdownRenderer
