"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Copy, Check } from "lucide-react"
import PDFViewer from "@/components/pdf-viewer"

interface DocumentViewerProps {
  type: "markdown" | "pdf"
}

export default function DocumentViewer({ type }: DocumentViewerProps) {
  const [activeTab, setActiveTab] = useState<string>("preview")
  const [copied, setCopied] = useState(false)

  const handleCopy = () => {
    navigator.clipboard.writeText(markdownContent)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const markdownContent = `# Detectron2: Nền tảng thị giác máy tính tiên tiến của Meta AI

## Giới thiệu

Detectron2 là nền tảng thế hệ tiếp theo đ<PERSON> phát triển bởi Facebook AI Research (FAIR) thuộc Meta AI, cung cấp các thuật toán phát hiện và phân đoạn đối tượng tiên tiến. Đây là phiên bản kế nhiệm của Detectron và maskrcnn-benchmark, được thiết kế để hỗ trợ triển khai nhanh chóng và đánh giá các nghiên cứu thị giác máy tính mới.

Detectron2 được xây dựng trên nền tảng PyTorch, được coi là một trong những thư viện nhận diện đối tượng mô-đun hóa hàng đầu hiện nay. Mặc dù ban đầu được phát triển để đáp ứng nhu cầu nghiên cứu của Facebook AI, Detectron2 đã được cộng đồng phát triển áp dụng rộng rãi và có sẵn cho các nhóm công khai thông qua giấy phép Apache 2.0.

Đội phát triển Detectron2 đã tuyên bố rằng: "Mục tiêu của chúng tôi với Detectron2 là hỗ trợ phạm vi rộng các mô hình phát hiện và phân đoạn đối tượng tiên tiến hiện có, đồng thời phục vụ bối cảnh nghiên cứu tiên tiến luôn thay đổi."

## Lịch sử và nguồn gốc

Detectron2 là sự tiếp nối của dự án Detectron ban đầu, nhưng với nhiều cải tiến đáng kể. Trong khi Detectron ban đầu được xây dựng trên Caffe2, Detectron2 được viết lại hoàn toàn bằng PyTorch. Sự thay đổi này phản ánh xu hướng chung trong cộng đồng học máy, khi PyTorch ngày càng được ưa chuộng hơn nhờ tính linh hoạt và dễ sử dụng.`

  if (type === "markdown") {
    return (
      <div className="flex h-full flex-col">
        <Tabs defaultValue="preview" className="h-full" onValueChange={setActiveTab}>
          <div className="border-b border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-900">
            <div className="flex items-center justify-between px-4">
              <TabsList className="h-12 bg-transparent">
                <TabsTrigger
                  value="preview"
                  className="data-[state=active]:bg-zinc-100 data-[state=active]:text-zinc-900 dark:data-[state=active]:bg-zinc-800 dark:data-[state=active]:text-zinc-100"
                >
                  Preview
                </TabsTrigger>
                <TabsTrigger
                  value="source"
                  className="data-[state=active]:bg-zinc-100 data-[state=active]:text-zinc-900 dark:data-[state=active]:bg-zinc-800 dark:data-[state=active]:text-zinc-100"
                >
                  Source
                </TabsTrigger>
              </TabsList>
              {activeTab === "source" && (
                <Button variant="ghost" size="sm" onClick={handleCopy} className="gap-2">
                  {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                  {copied ? "Copied" : "Copy"}
                </Button>
              )}
            </div>
          </div>

          <TabsContent value="preview" className="h-[calc(100%-3rem)] data-[state=inactive]:hidden">
            <ScrollArea className="h-full">
              <div className="prose prose-zinc max-w-none p-6 dark:prose-invert">
                <h1>Detectron2: Nền tảng thị giác máy tính tiên tiến của Meta AI</h1>

                <h2>Giới thiệu</h2>
                <p>
                  Detectron2 là nền tảng thế hệ tiếp theo được phát triển bởi Facebook AI Research (FAIR) thuộc Meta AI,
                  cung cấp các thuật toán phát hiện và phân đoạn đối tượng tiên tiến. Đây là phiên bản kế nhiệm của
                  Detectron và maskrcnn-benchmark, được thiết kế để hỗ trợ triển khai nhanh chóng và đánh giá các nghiên
                  cứu thị giác máy tính mới.
                </p>

                <p>
                  Detectron2 được xây dựng trên nền tảng PyTorch, được coi là một trong những thư viện nhận diện đối
                  tượng mô-đun hóa hàng đầu hiện nay. Mặc dù ban đầu được phát triển để đáp ứng nhu cầu nghiên cứu của
                  Facebook AI, Detectron2 đã được cộng đồng phát triển áp dụng rộng rãi và có sẵn cho các nhóm công khai
                  thông qua giấy phép Apache 2.0.
                </p>

                <blockquote>
                  <p>
                    "Mục tiêu của chúng tôi với Detectron2 là hỗ trợ phạm vi rộng các mô hình phát hiện và phân đoạn đối
                    tượng tiên tiến hiện có, đồng thời phục vụ bối cảnh nghiên cứu tiên tiến luôn thay đổi."
                  </p>
                </blockquote>

                <h2>Lịch sử và nguồn gốc</h2>
                <p>
                  Detectron2 là sự tiếp nối của dự án Detectron ban đầu, nhưng với nhiều cải tiến đáng kể. Trong khi
                  Detectron ban đầu được xây dựng trên Caffe2, Detectron2 được viết lại hoàn toàn bằng PyTorch. Sự thay
                  đổi này phản ánh xu hướng chung trong cộng đồng học máy, khi PyTorch ngày càng được ưa chuộng hơn nhờ
                  tính linh hoạt và dễ sử dụng.
                </p>

                <h2>Kiến trúc và thành phần</h2>
                <p>
                  Detectron2 có kiến trúc mô-đun hóa cao, cho phép các nhà nghiên cứu và nhà phát triển dễ dàng thử
                  nghiệm với các kiến trúc mô hình, hàm mất mát và kỹ thuật huấn luyện khác nhau. Ba cấu trúc chính
                  trong kiến trúc Detectron2 bao gồm:
                </p>

                <h3>1. Backbone Network (Mạng xương sống)</h3>
                <p>
                  Mạng backbone Detectron2 trích xuất bản đồ đặc trưng ở các tỷ lệ khác nhau từ hình ảnh đầu vào.
                  Detectron2 cung cấp một số tùy chọn backbone, bao gồm:
                </p>
                <ul>
                  <li>ResNet</li>
                  <li>ResNeXt</li>
                  <li>MobileNet</li>
                </ul>
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="source" className="h-[calc(100%-3rem)] data-[state=inactive]:hidden">
            <ScrollArea className="h-full">
              <div className="p-6">
                <pre className="whitespace-pre-wrap rounded-lg bg-zinc-100 p-4 text-sm text-zinc-800 dark:bg-zinc-900 dark:text-zinc-300">
                  {markdownContent}
                </pre>
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </div>
    )
  }

  return <PDFViewer />
}
