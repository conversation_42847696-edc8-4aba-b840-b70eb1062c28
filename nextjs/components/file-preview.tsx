"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Download, Maximize, ChevronLeft, ChevronRight, X } from "lucide-react"
import PDFViewer from "@/components/pdf-viewer"

interface FilePreviewProps {
  fileName: string
  fileType: "markdown" | "pdf"
  onClose: () => void
}

export default function FilePreview({ fileName, fileType, onClose }: FilePreviewProps) {
  const [activeTab, setActiveTab] = useState<string>("preview")

  return (
    <div className="flex h-full flex-col overflow-hidden">
      <div className="flex h-12 items-center justify-between border-b border-zinc-800 bg-zinc-800/50 px-4">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" className="mr-2">
            <Download className="h-4 w-4" />
          </Button>
          <h3 className="text-sm font-medium">{fileName}</h3>
        </div>
        <div className="flex items-center gap-1">
          <Button variant="ghost" size="icon">
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon">
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon">
            <Maximize className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="flex-1 overflow-auto">
        {fileType === "markdown" ? (
          <Tabs defaultValue="preview" className="h-full">
            <div className="border-b border-zinc-800">
              <TabsList className="h-10 bg-transparent">
                <TabsTrigger
                  value="preview"
                  className="data-[state=active]:bg-zinc-800/50 data-[state=active]:text-zinc-100"
                  onClick={() => setActiveTab("preview")}
                >
                  Preview
                </TabsTrigger>
                <TabsTrigger
                  value="source"
                  className="data-[state=active]:bg-zinc-800/50 data-[state=active]:text-zinc-100"
                  onClick={() => setActiveTab("source")}
                >
                  Source
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="preview" className="h-[calc(100%-2.5rem)] overflow-auto p-6">
              <div className="prose prose-invert max-w-none">
                <h1>Detectron2: Nền tảng thị giác máy tính tiên tiến của Meta AI</h1>

                <h2>Giới thiệu</h2>
                <p>
                  Detectron2 là nền tảng thế hệ tiếp theo được phát triển bởi Facebook AI Research (FAIR) thuộc Meta AI,
                  cung cấp các thuật toán phát hiện và phân đoạn đối tượng tiên tiến. Đây là phiên bản kế nhiệm của
                  Detectron và maskrcnn-benchmark, được thiết kế để hỗ trợ triển khai nhanh chóng và đánh giá các nghiên
                  cứu thị giác máy tính mới.
                </p>

                <p>
                  Detectron2 được xây dựng trên nền tảng PyTorch, được coi là một trong những thư viện nhận diện đối
                  tượng mô-đun hóa hàng đầu hiện nay. Mặc dù ban đầu được phát triển để đáp ứng nhu cầu nghiên cứu của
                  Facebook AI, Detectron2 đã được cộng đồng phát triển áp dụng rộng rãi và có sẵn cho các nhóm công khai
                  thông qua giấy phép Apache 2.0.
                </p>
              </div>
            </TabsContent>

            <TabsContent value="source" className="h-[calc(100%-2.5rem)] overflow-auto p-6">
              <pre className="whitespace-pre-wrap text-sm text-zinc-300">
                {`# Detectron2: Nền tảng thị giác máy tính tiên tiến của Meta AI

## Giới thiệu

Detectron2 là nền tảng thế hệ tiếp theo được phát triển bởi Facebook AI Research (FAIR) thuộc Meta AI, cung cấp các thuật toán phát hiện và phân đoạn đối tượng tiên tiến. Đây là phiên bản kế nhiệm của Detectron và maskrcnn-benchmark, được thiết kế để hỗ trợ triển khai nhanh chóng và đánh giá các nghiên cứu thị giác máy tính mới.

Detectron2 được xây dựng trên nền tảng PyTorch, được coi là một trong những thư viện nhận diện đối tượng mô-đun hóa hàng đầu hiện nay. Mặc dù ban đầu được phát triển để đáp ứng nhu cầu nghiên cứu của Facebook AI, Detectron2 đã được cộng đồng phát triển áp dụng rộng rãi và có sẵn cho các nhóm công khai thông qua giấy phép Apache 2.0.`}
              </pre>
            </TabsContent>
          </Tabs>
        ) : (
          <PDFViewer />
        )}
      </div>
    </div>
  )
}
