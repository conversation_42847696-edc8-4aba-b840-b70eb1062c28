FROM node:22-alpine AS base

# <PERSON><PERSON><PERSON> mục làm việc trong container
WORKDIR /app

# Cài đặt phụ thuộc
FROM base AS deps
COPY package.json ./
RUN npm install --legacy-peer-deps

# B<PERSON>ớc build
FROM base AS builder
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN npm run build

# Bước production
FROM base AS runner
ENV NODE_ENV production

# Tạo người dùng không phải root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
USER nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Mở cổng
EXPOSE 3000

# Chạy ứng dụng
CMD ["npm", "run", "dev"]

