"use client"

import React, { createContext, useContext, useState, useEffect, useRef, useCallback } from 'react';

export interface ChatMessage {
  id: number;
  content: string;
  sender: 'user' | 'assistant';
  timestamp: string;
}

export interface ManusStatusUpdate {
  status: 'idle' | 'processing' | 'success' | 'error' | 'warning';
  message: string;
  progress: number;
  completed: boolean;
  response: string | null;
}

export interface ChatContext {
  extracted_at: string;
  page_url: string;
  page_title: string;
  chat_container_html: string | null;
  sidebar_html: string | null;
  full_page_html: string | null;
  messages: any[];
  completion_status: boolean;
  active_elements: any[];
}

export interface RealtimeUpdate {
  chat_context: ChatContext;
  elapsed_time: number;
  new_messages: number;
  monitoring_status: string;
}

export interface MonitoringHeartbeat {
  elapsed_time: number;
  message_count: number;
  monitoring_status: string;
  remaining_time: number;
}

interface WebSocketContextType {
  isConnected: boolean;
  status: ManusStatusUpdate;
  elapsedTime: number;
  formattedElapsedTime: () => string;
  connect: () => void;
  sendMessage: (data: any, enableRealtimeMonitoring?: boolean) => boolean;
  disconnect: () => void;
  chatHistory: ChatMessage[];
  addMessageToHistory: (message: ChatMessage) => void;
  clearChatHistory: () => void;
  realtimeData: RealtimeUpdate | null;
  monitoringActive: boolean;
  chatContext: ChatContext | null;
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(undefined);

export const useWebSocket = () => {
  const context = useContext(WebSocketContext);
  if (context === undefined) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};

interface WebSocketProviderProps {
  children: React.ReactNode;
  endpoint?: string;
}

export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children, endpoint }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [status, setStatus] = useState<ManusStatusUpdate>({
    status: 'idle',
    message: 'Sẵn sàng kết nối',
    progress: 0,
    completed: false,
    response: null
  });
  const [elapsedTime, setElapsedTime] = useState(0);
  const [chatHistory, setChatHistory] = useState<ChatMessage[]>([]);
  const [realtimeData, setRealtimeData] = useState<RealtimeUpdate | null>(null);
  const [monitoringActive, setMonitoringActive] = useState(false);
  const [chatContext, setChatContext] = useState<ChatContext | null>(null);

  const socketRef = useRef<WebSocket | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const timerRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const isConnectingRef = useRef(false);

  // Khởi tạo WebSocket
  const connect = useCallback(() => {
    // Nếu đang kết nối hoặc đã kết nối, không làm gì cả
    if (isConnectingRef.current || (socketRef.current && socketRef.current.readyState === WebSocket.OPEN)) {
      console.log('WebSocket đã kết nối hoặc đang kết nối, bỏ qua yêu cầu kết nối mới');
      return;
    }

    // Đánh dấu đang trong quá trình kết nối
    isConnectingRef.current = true;

    // Đóng kết nối cũ nếu có
    if (socketRef.current) {
      socketRef.current.close();
      socketRef.current = null;
    }

    try {
      // Sử dụng endpoint được cung cấp hoặc tạo URL mặc định
      let wsUrl;

      if (endpoint) {
        // Sử dụng endpoint được cung cấp
        wsUrl = endpoint;
      } else {
        // Tạo URL mặc định từ hostname
        const wsHostname = typeof window !== 'undefined'
          ? window.location.hostname
          : 'localhost';
        wsUrl = `ws://${wsHostname}:8000/ws/minus-chat`;
      }

      console.log('Sử dụng WebSocket URL:', wsUrl);

      console.log('Đang kết nối đến WebSocket URL:', wsUrl);
      const socket = new WebSocket(wsUrl);

      socket.onopen = () => {
        console.log('WebSocket kết nối thành công');
        setIsConnected(true);
        reconnectAttemptsRef.current = 0;
        isConnectingRef.current = false;
      };

      socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('Nhận dữ liệu từ server:', data);

          // Xử lý ping từ server
          if (data.type === 'ping') {
            console.debug('Nhận ping từ server, gửi lại pong');
            if (socket.readyState === WebSocket.OPEN) {
              socket.send(JSON.stringify({ type: 'pong', timestamp: data.timestamp }));
            }
            return;
          }

          // Xử lý lịch sử cuộc trò chuyện
          if (data.type === 'history') {
            console.log('Nhận lịch sử cuộc trò chuyện:', data.data);
            if (Array.isArray(data.data)) {
              const formattedHistory = data.data.map((msg, index) => ({
                id: index,
                content: msg.content,
                sender: msg.role === 'user' ? 'user' : 'assistant',
                timestamp: msg.time || new Date(msg.timestamp * 1000).toLocaleTimeString()
              }));

              // Chỉ cập nhật nếu có sự thay đổi thực sự
              const currentHistoryString = JSON.stringify(chatHistory);
              const newHistoryString = JSON.stringify(formattedHistory);

              if (currentHistoryString !== newHistoryString) {
                console.log('Cập nhật lịch sử chat với dữ liệu mới');
                setChatHistory(formattedHistory);
              }
            }
            return;
          }

          // Xử lý real-time updates
          if (data.type === 'realtime_update') {
            console.log('Nhận real-time update:', data.data);
            setRealtimeData(data.data);
            setChatContext(data.data.chat_context);

            // Cập nhật chat history từ real-time data (chỉ khi có messages mới)
            if (data.data.chat_context && data.data.chat_context.messages) {
              const formattedMessages = data.data.chat_context.messages.map((msg: any, index: number) => ({
                id: index,
                content: msg.content,
                sender: msg.role === 'user' ? 'user' : 'assistant',
                timestamp: msg.timestamp || new Date().toLocaleTimeString()
              }));

              // Chỉ cập nhật nếu số lượng messages tăng hoặc có thay đổi content
              const hasNewContent = formattedMessages.length > chatHistory.length ||
                JSON.stringify(formattedMessages) !== JSON.stringify(chatHistory);

              if (hasNewContent) {
                console.log(`Cập nhật chat history: ${chatHistory.length} -> ${formattedMessages.length} messages`);
                setChatHistory(formattedMessages);
              }
            }

            // Kiểm tra completion status và cập nhật status
            if (data.data.chat_context && data.data.chat_context.completion_status) {
              console.log('Phát hiện task completion từ real-time update');
              setMonitoringActive(false);
              setStatus({
                status: 'success',
                message: 'Manus đã hoàn thành nhiệm vụ!',
                progress: 100,
                completed: true,
                response: 'Manus has completed the current task'
              });

              // Dừng đếm thời gian
              if (timerRef.current) {
                clearInterval(timerRef.current);
                timerRef.current = null;
              }
            }
            return;
          }

          // Xử lý processing started
          if (data.type === 'processing_started') {
            console.log('Processing started:', data.data);
            setStatus({
              status: 'processing',
              message: data.data.message || 'Đang xử lý tin nhắn...',
              progress: 5,
              completed: false,
              response: null
            });
            return;
          }

          // Xử lý monitoring started
          if (data.type === 'monitoring_started') {
            console.log('Monitoring started:', data.data);
            setMonitoringActive(true);
            setStatus({
              status: 'processing',
              message: data.data.message || 'Bắt đầu giám sát real-time',
              progress: 10,
              completed: false,
              response: null
            });
            return;
          }

          // Xử lý initial context
          if (data.type === 'initial_context') {
            console.log('Nhận initial context:', data.data);
            setChatContext(data.data.chat_context);
            setStatus({
              status: 'processing',
              message: data.data.message || 'Đã nhận ngữ cảnh ban đầu',
              progress: 20,
              completed: false,
              response: null
            });
            return;
          }

          // Xử lý new messages
          if (data.type === 'new_messages') {
            console.log('New messages received:', data.data);
            if (data.data.messages && Array.isArray(data.data.messages)) {
              // Thêm tin nhắn mới vào lịch sử
              const newMessages = data.data.messages.map((msg: any, index: number) => ({
                id: Date.now() + index,
                content: msg.content,
                sender: msg.role === 'user' ? 'user' : 'assistant',
                timestamp: msg.timestamp || new Date().toLocaleTimeString()
              }));

              setChatHistory(prev => [...prev, ...newMessages]);
            }
            return;
          }

          // Xử lý task completed
          if (data.type === 'task_completed') {
            console.log('Task completed:', data.data);
            setMonitoringActive(false);

            // Cập nhật chat history với final messages nếu có
            if (data.data.final_messages && Array.isArray(data.data.final_messages)) {
              const formattedMessages = data.data.final_messages.map((msg: any, index: number) => ({
                id: index,
                content: msg.content,
                sender: msg.role === 'user' ? 'user' : 'assistant',
                timestamp: msg.timestamp || new Date().toLocaleTimeString()
              }));
              setChatHistory(formattedMessages);
            }

            setStatus({
              status: 'success',
              message: 'Manus đã hoàn thành nhiệm vụ!',
              progress: 100,
              completed: true,
              response: 'Manus has completed the current task'
            });

            // Dừng đếm thời gian
            if (timerRef.current) {
              clearInterval(timerRef.current);
              timerRef.current = null;
            }
            return;
          }

          // Xử lý monitoring heartbeat
          if (data.type === 'monitoring_heartbeat') {
            console.log('Monitoring heartbeat:', data.data);
            setStatus(prevStatus => ({
              ...prevStatus,
              message: `Đang giám sát... (${Math.floor(data.data.remaining_time / 60)}:${String(Math.floor(data.data.remaining_time % 60)).padStart(2, '0')} còn lại)`,
              progress: Math.min(50 + (data.data.elapsed_time / 900) * 40, 90)
            }));
            return;
          }

          // Xử lý monitoring error
          if (data.type === 'monitoring_error') {
            console.log('Monitoring error:', data.data);
            setMonitoringActive(false);
            setStatus({
              status: 'error',
              message: data.data.message || 'Lỗi trong quá trình giám sát',
              progress: 0,
              completed: true,
              response: null
            });

            // Dừng đếm thời gian
            if (timerRef.current) {
              clearInterval(timerRef.current);
              timerRef.current = null;
            }
            return;
          }

          // Xử lý monitoring ended
          if (data.type === 'monitoring_ended') {
            console.log('Monitoring ended:', data.data);
            setMonitoringActive(false);
            setStatus(prevStatus => ({
              ...prevStatus,
              message: data.data.reason === 'timeout' ? 'Hết thời gian giám sát' : 'Kết thúc giám sát',
              completed: true
            }));

            // Dừng đếm thời gian
            if (timerRef.current) {
              clearInterval(timerRef.current);
              timerRef.current = null;
            }
            return;
          }

          // Xử lý phản hồi từ server
          if (data.type === 'response') {
            console.log('Nhận phản hồi từ server:', data.data);

            if (data.data.status === 'success' && data.data.message) {
              // Kiểm tra xem tin nhắn này đã có trong lịch sử chưa
              const messageContent = data.data.message.content;

              // Kiểm tra messageContent hợp lệ
              if (!messageContent && typeof messageContent !== 'string') {
                console.warn('Cảnh báo: Nội dung tin nhắn không hợp lệ:', data.data.message);
              }

              const isDuplicate = messageContent ? chatHistory.some((msg: any) =>
                msg.sender === 'assistant' && msg.content === messageContent
              ) : false;

              if (!isDuplicate) {
                console.log('Thêm tin nhắn mới từ assistant vào lịch sử:', messageContent ? messageContent.substring(0, 100) + '...' : 'Không có nội dung');
                const newMessage = {
                  id: Date.now(),
                  content: messageContent || '',
                  sender: 'assistant' as const,
                  timestamp: data.data.message.time || new Date().toLocaleTimeString()
                };
                addMessageToHistory(newMessage);
              } else {
                console.log('Bỏ qua tin nhắn trùng lặp từ assistant');
              }

              // Cập nhật trạng thái
              setStatus({
                status: 'success',
                message: 'Đã nhận phản hồi từ Manus',
                progress: 100,
                completed: true,
                response: messageContent
              });

              // Dừng đếm thời gian
              if (timerRef.current) {
                clearInterval(timerRef.current);
                timerRef.current = null;
              }
            } else {
              // Xử lý lỗi
              const errorMessage = data.data.message || 'Lỗi khi nhận phản hồi từ Manus';

              // Thêm thông báo lỗi vào lịch sử chat
              const systemErrorMessage = {
                id: Date.now(),
                content: `Error: ${errorMessage}`,
                sender: 'assistant' as const,
                timestamp: new Date().toLocaleTimeString()
              };

              addMessageToHistory(systemErrorMessage);

              // Cập nhật trạng thái
              setStatus({
                status: 'error',
                message: errorMessage,
                progress: 0,
                completed: true,
                response: null
              });
            }
            return;
          }

          // Xử lý trạng thái từ server
          if (data.type === 'status') {
            console.log('Nhận cập nhật trạng thái:', data.data);
            setStatus({
              status: 'processing',
              message: 'Đang xử lý tin nhắn...',
              progress: 50,
              completed: false,
              response: null
            });

            // Bắt đầu đếm thời gian nếu chưa
            if (!timerRef.current) {
              timerRef.current = setInterval(() => {
                setElapsedTime(prev => prev + 1);
              }, 1000);
            }
            return;
          }

          // Xử lý các tin nhắn khác (định dạng cũ)
          console.log('Nhận cập nhật định dạng cũ:', data);

          // Xử lý cập nhật trạng thái theo định dạng cũ
          if (data && typeof data === 'object') {
            // Đảm bảo trường response được xử lý
            if (data.response) {
              console.log('Phát hiện phản hồi:', typeof data.response === 'string' ? data.response.substring(0, 100) + '...' : 'Phản hồi không phải chuỗi');

              // Kiểm tra xem tin nhắn này đã có trong lịch sử chưa
              const isDuplicate = chatHistory.some((msg: any) =>
                msg.sender === 'assistant' && msg.content === data.response
              );

              if (!isDuplicate) {
                console.log('Thêm tin nhắn mới từ assistant vào lịch sử (định dạng cũ)');
                // Thêm tin nhắn mới vào lịch sử
                const newMessage = {
                  id: Date.now(), // Sử dụng timestamp để tạo ID duy nhất
                  content: typeof data.response === 'string' ? data.response : String(data.response),
                  sender: 'assistant' as const,
                  timestamp: new Date().toLocaleTimeString()
                };
                addMessageToHistory(newMessage);
              } else {
                console.log('Bỏ qua tin nhắn trùng lặp từ assistant (định dạng cũ)');
              }
            }

            // Cập nhật trạng thái
            setStatus(data as ManusStatusUpdate);
          }

          // Bắt đầu đếm thời gian khi bắt đầu xử lý
          if (data && data.status === 'processing' && data.progress > 0 && !timerRef.current) {
            timerRef.current = setInterval(() => {
              setElapsedTime(prev => prev + 1);
            }, 1000);
          }

          // Dừng đếm thời gian khi hoàn thành
          if (data && data.completed && timerRef.current) {
            clearInterval(timerRef.current);
            timerRef.current = null;
          }
        } catch (error) {
          console.error('Lỗi khi xử lý dữ liệu WebSocket:', error);
        }
      };

      socket.onclose = (event) => {
        console.log('WebSocket closed:', event.code, event.reason);
        setIsConnected(false);
        isConnectingRef.current = false;

        // Stop timer if running
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }

        // Update status to show disconnection
        setStatus({
          status: 'warning',
          message: `WebSocket disconnected (code: ${event.code})${event.reason ? ': ' + event.reason : ''}`,
          progress: 0,
          completed: false,
          response: null
        });

        // Try to reconnect if not a normal closure (not code 1000 or 1001)
        if (event.code !== 1000 && event.code !== 1001) {
          // Calculate backoff time with exponential backoff
          const backoffTime = 3000 * Math.pow(1.5, reconnectAttemptsRef.current);
          const reconnectTime = Math.min(backoffTime, 30000); // Maximum 30 seconds

          reconnectAttemptsRef.current += 1;
          console.log(`Attempting to reconnect (attempt ${reconnectAttemptsRef.current}) in ${reconnectTime}ms`);

          setTimeout(() => {
            // Check if socket has been reconnected before trying again
            if (!isConnected && reconnectAttemptsRef.current <= 10) {
              console.log(`Reconnecting (attempt ${reconnectAttemptsRef.current})...`);
              connect();
            }
          }, reconnectTime);
        } else {
          console.log('WebSocket closed normally, no reconnection needed');
        }
      };

      socket.onerror = (error) => {
        console.error('WebSocket error:', error);
        console.dir(error, { depth: null }); // Log full error object

        // Update error status
        setStatus({
          status: 'error',
          message: 'WebSocket connection error. Attempting to reconnect...',
          progress: 0,
          completed: false,
          response: null
        });

        // Reset connecting flag
        isConnectingRef.current = false;

        // Set a timeout to attempt reconnection
        setTimeout(() => {
          if (!isConnected) {
            console.log('Attempting to reconnect after error...');
            connect();
          }
        }, 3000);
      };

      socketRef.current = socket;
    } catch (error) {
      console.error('Lỗi khi tạo kết nối WebSocket:', error);
      isConnectingRef.current = false;
    }
  }, []);

  // Add message to chat history
  const addMessageToHistory = useCallback((message: ChatMessage) => {
    setChatHistory(prev => [...prev, message]);
  }, []);

  // Clear chat history
  const clearChatHistory = useCallback(() => {
    setChatHistory([]);
  }, []);

  // Send data to the server
  const sendMessage = useCallback((data: any, enableRealtimeMonitoring: boolean = true, profileName?: string) => {
    // Kiểm tra dữ liệu đầu vào
    if (!data) {
      console.error('Dữ liệu gửi không được để trống');
      return false;
    }

    if (!socketRef.current) {
      console.error('WebSocket chưa được khởi tạo, không thể gửi dữ liệu');

      // Thử kết nối lại
      setTimeout(() => {
        connect();
      }, 1000);

      return false;
    }

    if (socketRef.current.readyState !== WebSocket.OPEN) {
      console.error('WebSocket không ở trạng thái OPEN, trạng thái hiện tại:', socketRef.current.readyState);

      // Thử kết nối lại nếu socket đã đóng
      if (socketRef.current.readyState === WebSocket.CLOSED ||
          socketRef.current.readyState === WebSocket.CLOSING) {
        console.log('WebSocket đã đóng hoặc đang đóng, đang thử kết nối lại...');
        setTimeout(() => {
          connect();
        }, 1000);
      }

      return false;
    }

    try {
      // Xử lý dữ liệu đầu vào (hỗ trợ cả chuỗi và object)
      let messageContent = '';
      if (typeof data === 'string') {
        messageContent = data;
      } else if (typeof data === 'object') {
        messageContent = data.chat_content || data.message || '';
      } else {
        messageContent = String(data); // Cố gắng chuyển đổi thành chuỗi
      }

      // Tạo tin nhắn người dùng mới
      const userMessage = {
        id: chatHistory.length + 1,
        content: messageContent,
        sender: 'user' as const,
        timestamp: new Date().toLocaleTimeString()
      };

      // Thêm tin nhắn người dùng vào lịch sử
      addMessageToHistory(userMessage);

      // Chuẩn bị dữ liệu để gửi
      const messageToSend: any = {
        type: 'message',
        message: messageContent,
        enable_realtime_monitoring: enableRealtimeMonitoring
      };

      // Thêm profile_name nếu có
      if (profileName) {
        messageToSend.profile_name = profileName;
      } else if (typeof data === 'object' && data.profile_name) {
        messageToSend.profile_name = data.profile_name;
      }

      console.log('Đang gửi tin nhắn:', messageToSend);
      socketRef.current.send(JSON.stringify(messageToSend));

      // Cập nhật trạng thái
      setStatus({
        status: 'processing',
        message: 'Đang xử lý...',
        progress: 10,
        completed: false,
        response: null
      });

      // Reset thời gian đếm
      setElapsedTime(0);

      return true;
    } catch (error) {
      console.error('Lỗi khi gửi dữ liệu qua WebSocket:', error);

      // Nếu gặp lỗi "closed", thử kết nối lại
      if (error instanceof Error &&
          (error.message.includes('closed') ||
           error.message.includes('CLOSING') ||
           error.message.includes('CLOSED'))) {
        console.log('WebSocket có vẻ đã đóng, đang thử kết nối lại...');
        setTimeout(() => {
          connect();
        }, 1000);
      }

      return false;
    }
  }, [connect, chatHistory, addMessageToHistory]);

  // Đóng kết nối
  const disconnect = useCallback(() => {
    if (socketRef.current) {
      // Đóng kết nối với mã 1000 (đóng bình thường)
      socketRef.current.close(1000, "Đóng có chủ ý");
      socketRef.current = null;
    }

    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    setIsConnected(false);
    setElapsedTime(0);
  }, []);

  // Khởi tạo kết nối khi component được mount
  useEffect(() => {
    // Chỉ kết nối nếu chạy ở phía client
    if (typeof window !== 'undefined') {
      connect();

      // Xử lý sự kiện beforeunload để đóng kết nối khi rời trang
      const handleBeforeUnload = () => {
        if (socketRef.current) {
          socketRef.current.close(1000, "Đóng khi rời trang");
        }
      };

      window.addEventListener('beforeunload', handleBeforeUnload);

      // Dọn dẹp khi unmount
      return () => {
        window.removeEventListener('beforeunload', handleBeforeUnload);
        // Không đóng kết nối khi component unmount để giữ kết nối giữa các trang
      };
    }
  }, [connect]);

  // Format thời gian chờ
  const formattedElapsedTime = useCallback(() => {
    const minutes = Math.floor(elapsedTime / 60);
    const seconds = elapsedTime % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, [elapsedTime]);

  const value = {
    isConnected,
    status,
    elapsedTime,
    formattedElapsedTime,
    connect,
    sendMessage,
    disconnect,
    chatHistory,
    addMessageToHistory,
    clearChatHistory,
    realtimeData,
    monitoringActive,
    chatContext
  };

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  );
};
