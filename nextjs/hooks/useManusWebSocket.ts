import { useState, useEffect, useRef, useCallback } from 'react';

export interface ManusStatusUpdate {
  status: 'idle' | 'processing' | 'success' | 'error' | 'warning';
  message: string;
  progress: number;
  completed: boolean;
  response: string | null;
}

interface ManusWebSocketOptions {
  onOpen?: () => void;
  onClose?: () => void;
  onError?: (error: Event) => void;
  autoReconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

export const useManusWebSocket = (options: ManusWebSocketOptions = {}) => {
  const [isConnected, setIsConnected] = useState(false);
  const [status, setStatus] = useState<ManusStatusUpdate>({
    status: 'idle',
    message: 'Sẵn sàng kết nối',
    progress: 0,
    completed: false,
    response: null
  });
  const [elapsedTime, setElapsedTime] = useState(0);

  const socketRef = useRef<WebSocket | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const timerRef = useRef<ReturnType<typeof setInterval> | null>(null);

  const {
    onOpen,
    onClose,
    onError,
    autoReconnect = true,
    reconnectInterval = 3000,
    maxReconnectAttempts = 5
  } = options;

  // Khởi tạo WebSocket
  const connect = useCallback(() => {
    // Đóng kết nối cũ nếu có
    if (socketRef.current) {
      socketRef.current.close();
    }

    try {
      // Lấy hostname từ window.location và sử dụng đúng giao thức ws://
      const wsHostname = typeof window !== 'undefined'
        ? window.location.hostname
        : 'localhost';
      // Sử dụng websocket trực tiếp đến backend
      const wsUrl = `ws://${wsHostname}:8000/ws/minus-chat`;

      console.log('Đang kết nối đến WebSocket URL:', wsUrl);
      const socket = new WebSocket(wsUrl);

      socket.onopen = () => {
        console.log('WebSocket kết nối thành công');
        setIsConnected(true);
        reconnectAttemptsRef.current = 0;
        if (onOpen) onOpen();
      };

      socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          // Xử lý ping từ server
          if (data.type === 'ping') {
            console.debug('Nhận ping từ server, gửi lại pong');
            if (socket.readyState === WebSocket.OPEN) {
              socket.send(JSON.stringify({ type: 'pong', timestamp: data.timestamp }));
            }
            return;
          }

          console.log('Nhận cập nhật:', data);

          // Xử lý cập nhật trạng thái
          setStatus(data as ManusStatusUpdate);

          // Bắt đầu đếm thời gian khi bắt đầu xử lý
          if (data.status === 'processing' && data.progress > 0 && !timerRef.current) {
            timerRef.current = setInterval(() => {
              setElapsedTime(prev => prev + 1);
            }, 1000);
          }

          // Dừng đếm thời gian khi hoàn thành
          if (data.completed && timerRef.current) {
            clearInterval(timerRef.current);
            timerRef.current = null;
          }
        } catch (error) {
          console.error('Lỗi khi xử lý dữ liệu WebSocket:', error);
        }
      };

      socket.onclose = (event) => {
        console.log('WebSocket đã đóng:', event.code, event.reason);
        setIsConnected(false);

        // Dừng timer nếu có
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }

        if (onClose) onClose();

        // Thử kết nối lại nếu được cấu hình
        if (autoReconnect && reconnectAttemptsRef.current < maxReconnectAttempts) {
          // Tính toán thời gian chờ với backoff theo cấp số nhân
          const backoffTime = reconnectInterval * Math.pow(1.5, reconnectAttemptsRef.current);
          const reconnectTime = Math.min(backoffTime, 30000); // Tối đa 30 giây

          reconnectAttemptsRef.current += 1;
          console.log(`Thử kết nối lại lần ${reconnectAttemptsRef.current} sau ${reconnectTime}ms`);

          setTimeout(() => {
            // Kiểm tra xem socket đã được kết nối lại chưa trước khi thử lại
            if (!isConnected) {
              console.log(`Đang kết nối lại lần ${reconnectAttemptsRef.current}...`);
              connect();
            }
          }, reconnectTime);
        } else if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
          console.log(`Đã đạt đến số lần thử kết nối lại tối đa (${maxReconnectAttempts})`);
        }
      };

      socket.onerror = (error) => {
        console.error('Lỗi WebSocket cụ thể:', error);
        console.dir(error, { depth: null }); // Log đầy đủ object lỗi

        // Cập nhật trạng thái lỗi
        setStatus({
          status: 'error',
          message: 'Lỗi kết nối WebSocket. Đang thử kết nối lại...',
          progress: 0,
          completed: false,
          response: null
        });

        if (onError) onError(error);

        // Không cần đóng socket ở đây vì sự kiện onclose sẽ được gọi sau đó
        // và sẽ xử lý việc kết nối lại
      };

      socketRef.current = socket;
    } catch (error) {
      console.error('Lỗi khi tạo kết nối WebSocket:', error);
    }
  }, [onOpen, onClose, onError, autoReconnect, reconnectInterval, maxReconnectAttempts]);

  // Gửi dữ liệu đến máy chủ
  const sendMessage = useCallback((data: any) => {
    if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
      socketRef.current.send(JSON.stringify(data));

      // Reset trạng thái
      setStatus({
        status: 'processing',
        message: 'Đang xử lý...',
        progress: 0,
        completed: false,
        response: null
      });

      // Reset elapsed time
      setElapsedTime(0);

      return true;
    }
    return false;
  }, []);

  // Đóng kết nối
  const disconnect = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.close();
      socketRef.current = null;
    }

    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    setIsConnected(false);
    setElapsedTime(0);
  }, []);

  // Khởi tạo kết nối khi component được mount
  useEffect(() => {
    connect();

    // Dọn dẹp khi unmount
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // Format thời gian chờ
  const formattedElapsedTime = useCallback(() => {
    const minutes = Math.floor(elapsedTime / 60);
    const seconds = elapsedTime % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, [elapsedTime]);

  return {
    isConnected,
    status,
    elapsedTime,
    formattedElapsedTime,
    connect,
    sendMessage,
    disconnect
  };
};