"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import {
  Menu,
  Search,
  FileText,
  FileIcon as FilePdf,
  ChevronDown,
  Star,
  Share,
  Download,
  Maximize2,
  ChevronLeft,
  ChevronRight,
  X,
  Eye,
  MessageSquare,
  Clock,
  CheckCircle2,
  LayoutGrid,
  Settings,
  HelpCircle,
  User,
  LogOut,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { <PERSON>rollArea } from "@/components/ui/scroll-area"
import DocumentViewer from "@/components/document-viewer"
import ChatInterface from "@/components/chat-interface"

export default function Home() {
  const [showSidebar, setShowSidebar] = useState(true)
  const [showPreview, setShowPreview] = useState(true)
  const [selectedFile, setSelectedFile] = useState<string | null>("markdown")
  const [viewMode, setViewMode] = useState<"split" | "document" | "chat">("split")
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    // Initial check
    checkMobile()

    // Add event listener for window resize
    window.addEventListener("resize", checkMobile)

    // Cleanup
    return () => window.removeEventListener("resize", checkMobile)
  }, [])

  // Adjust layout for mobile
  useEffect(() => {
    if (isMobile) {
      setShowSidebar(false)
      if (viewMode === "split") {
        setViewMode("document")
      }
    }
  }, [isMobile, viewMode])

  const files = [
    {
      id: "markdown",
      name: "Detectron2: Nền tảng thị giác máy tính",
      type: "markdown",
      size: "14.60 KB",
      icon: <FileText className="h-5 w-5 text-blue-400" />,
      date: "15/05/2023",
    },
    {
      id: "pdf",
      name: "detectron2_document.pdf",
      type: "pdf",
      size: "318.01 KB",
      icon: <FilePdf className="h-5 w-5 text-red-400" />,
      date: "15/05/2023",
    },
  ]

  return (
    <div className="flex h-screen flex-col bg-zinc-50 text-zinc-900 dark:bg-zinc-900 dark:text-zinc-50">
      {/* Header */}
      <header className="sticky top-0 z-50 flex h-16 items-center justify-between border-b border-zinc-200 bg-white px-4 dark:border-zinc-800 dark:bg-zinc-900">
        <div className="flex items-center gap-4">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" onClick={() => setShowSidebar(!showSidebar)}>
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Toggle sidebar</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">Toggle sidebar</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <div className="flex items-center gap-2">
            <MessageSquare className="h-6 w-6 text-teal-500" />
            <h1 className="text-xl font-bold">DocuChat</h1>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <div className="relative hidden md:block">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-zinc-500" />
            <Input
              placeholder="Search documents..."
              className="w-64 pl-9 dark:border-zinc-700 dark:bg-zinc-800 dark:text-zinc-100 dark:placeholder:text-zinc-400"
            />
          </div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Search className="h-5 w-5 md:hidden" />
                  <span className="sr-only">Search</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">Search</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="hidden sm:flex">
                  <HelpCircle className="h-5 w-5" />
                  <span className="sr-only">Help</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">Help</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="hidden sm:flex">
                  <Settings className="h-5 w-5" />
                  <span className="sr-only">Settings</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">Settings</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                <Avatar className="h-8 w-8">
                  <AvatarImage src="/placeholder.svg?height=32&width=32" alt="@user" />
                  <AvatarFallback>A</AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">A Le Thanh Son</p>
                  <p className="text-xs leading-none text-zinc-500 dark:text-zinc-400"><EMAIL></p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="mr-2 h-4 w-4" />
                <span>Settings</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </header>

      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        {showSidebar && (
          <div className="w-64 border-r border-zinc-200 bg-white dark:border-zinc-800 dark:bg-zinc-900">
            <div className="p-4">
              <Button
                variant="outline"
                className="w-full justify-start gap-2 border-zinc-200 bg-white text-zinc-900 hover:bg-zinc-100 dark:border-zinc-700 dark:bg-zinc-800 dark:text-zinc-100 dark:hover:bg-zinc-700"
              >
                <span className="text-lg">+</span> New document
              </Button>
            </div>

            <div className="px-3">
              <div className="mb-2 flex items-center justify-between px-2">
                <h2 className="text-xs font-semibold uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                  Recent Documents
                </h2>
                <Button variant="ghost" size="icon" className="h-7 w-7">
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </div>

              <ScrollArea className="h-[calc(100vh-12rem)]">
                <div className="space-y-1 pb-4">
                  <Button variant="ghost" className="w-full justify-start gap-3 rounded-lg px-3 py-2 text-left">
                    <div className="flex h-9 w-9 shrink-0 items-center justify-center rounded-md border border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-800">
                      <FileText className="h-5 w-5 text-blue-500" />
                    </div>
                    <div className="flex-1 truncate">
                      <div className="font-medium">Detectron2 Documentation</div>
                      <div className="text-xs text-zinc-500 dark:text-zinc-400">Updated 2 hours ago</div>
                    </div>
                  </Button>

                  <Button
                    variant="ghost"
                    className="w-full justify-start gap-3 rounded-lg bg-zinc-100 px-3 py-2 text-left dark:bg-zinc-800"
                  >
                    <div className="flex h-9 w-9 shrink-0 items-center justify-center rounded-md border border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-800">
                      <MessageSquare className="h-5 w-5 text-teal-500" />
                    </div>
                    <div className="flex-1 truncate">
                      <div className="font-medium">Tài liệu về Detectron2</div>
                      <div className="text-xs text-zinc-500 dark:text-zinc-400">Updated today</div>
                    </div>
                  </Button>

                  <Button variant="ghost" className="w-full justify-start gap-3 rounded-lg px-3 py-2 text-left">
                    <div className="flex h-9 w-9 shrink-0 items-center justify-center rounded-md border border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-800">
                      <FilePdf className="h-5 w-5 text-red-500" />
                    </div>
                    <div className="flex-1 truncate">
                      <div className="font-medium">Research Paper</div>
                      <div className="text-xs text-zinc-500 dark:text-zinc-400">Updated 3 days ago</div>
                    </div>
                  </Button>
                </div>
              </ScrollArea>
            </div>
          </div>
        )}

        {/* Main content */}
        <div className="flex flex-1 flex-col overflow-hidden">
          {/* Document toolbar */}
          <div className="flex h-12 items-center justify-between border-b border-zinc-200 bg-white px-4 dark:border-zinc-800 dark:bg-zinc-900">
            <div className="flex items-center gap-4">
              <h2 className="font-medium">Tài liệu về Detectron2</h2>
              <Badge variant="outline" className="hidden text-xs sm:flex">
                <Clock className="mr-1 h-3 w-3" /> Last edited: Today
              </Badge>
            </div>

            <div className="flex items-center gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 gap-1 hidden sm:flex">
                      <Star className="h-4 w-4" />
                      <span className="hidden sm:inline">Favorite</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">Add to favorites</TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 gap-1 hidden sm:flex">
                      <Share className="h-4 w-4" />
                      <span className="hidden sm:inline">Share</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">Share document</TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <Separator orientation="vertical" className="mx-1 h-6 hidden sm:flex" />

              <div className="flex rounded-md border border-zinc-200 dark:border-zinc-700">
                <Button
                  variant={viewMode === "document" ? "secondary" : "ghost"}
                  size="sm"
                  className="h-8 rounded-none rounded-l-md px-2"
                  onClick={() => setViewMode("document")}
                >
                  <FileText className="h-4 w-4" />
                </Button>
                {!isMobile && (
                  <Button
                    variant={viewMode === "split" ? "secondary" : "ghost"}
                    size="sm"
                    className="h-8 rounded-none px-2"
                    onClick={() => setViewMode("split")}
                  >
                    <LayoutGrid className="h-4 w-4" />
                  </Button>
                )}
                <Button
                  variant={viewMode === "chat" ? "secondary" : "ghost"}
                  size="sm"
                  className="h-8 rounded-none rounded-r-md px-2"
                  onClick={() => setViewMode("chat")}
                >
                  <MessageSquare className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Document content */}
          <div className="flex flex-1 overflow-hidden">
            {/* File list - only shown in split or document mode */}
            {(viewMode === "split" || viewMode === "document") && (
              <div
                className={`${
                  viewMode === "split" ? "w-1/3 border-r border-zinc-200 dark:border-zinc-800" : "w-full"
                } overflow-auto bg-white dark:bg-zinc-900`}
              >
                <div className="p-6">
                  <div className="mb-6">
                    <h2 className="mb-2 text-2xl font-bold">Tài liệu về Detectron2</h2>
                    <div className="flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400">
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                      <span>Completed</span>
                      <span>•</span>
                      <span>2 files</span>
                    </div>
                  </div>

                  <div className="mb-6">
                    <h3 className="mb-3 text-sm font-medium text-zinc-500 dark:text-zinc-400">Table of Contents</h3>
                    <div className="space-y-1 text-sm">
                      <ol className="list-inside list-decimal space-y-1 pl-1">
                        <li className="text-zinc-700 dark:text-zinc-300">Giới thiệu</li>
                        <li className="text-zinc-700 dark:text-zinc-300">Lịch sử và nguồn gốc</li>
                        <li className="text-zinc-700 dark:text-zinc-300">Kiến trúc và thành phần</li>
                        <li className="text-zinc-700 dark:text-zinc-300">Tính năng chính</li>
                        <li className="text-zinc-700 dark:text-zinc-300">Các tác vụ thị giác máy tính được hỗ trợ</li>
                        <li className="text-zinc-700 dark:text-zinc-300">Các thuật toán được hỗ trợ</li>
                        <li className="text-zinc-700 dark:text-zinc-300">Hướng dẫn cài đặt</li>
                        <li className="text-zinc-700 dark:text-zinc-300">Ví dụ sử dụng cơ bản</li>
                        <li className="text-zinc-700 dark:text-zinc-300">Ứng dụng thực tế</li>
                        <li className="text-zinc-700 dark:text-zinc-300">Cộng đồng và phát triển</li>
                        <li className="text-zinc-700 dark:text-zinc-300">Tài liệu tham khảo</li>
                      </ol>
                    </div>
                  </div>

                  <div className="mb-6">
                    <h3 className="mb-3 text-sm font-medium text-zinc-500 dark:text-zinc-400">Attached Files</h3>
                    <div className="space-y-3">
                      {files.map((file) => (
                        <div
                          key={file.id}
                          className={`flex cursor-pointer items-center rounded-lg border p-3 transition-colors ${
                            selectedFile === file.id
                              ? "border-teal-500 bg-teal-50 dark:border-teal-500 dark:bg-teal-900/20"
                              : "border-zinc-200 bg-white hover:border-zinc-300 dark:border-zinc-700 dark:bg-zinc-800 dark:hover:border-zinc-600"
                          }`}
                          onClick={() => setSelectedFile(file.id)}
                        >
                          <div className="mr-3">{file.icon}</div>
                          <div className="flex-1">
                            <div className="font-medium">{file.name}</div>
                            <div className="text-xs text-zinc-500 dark:text-zinc-400">
                              {file.type} · {file.size}
                            </div>
                          </div>
                          <div className="flex items-center gap-1">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-7 w-7"
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      setShowPreview(!showPreview)
                                    }}
                                  >
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent side="left">
                                  {showPreview ? "Hide preview" : "Show preview"}
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-7 w-7"
                                    onClick={(e) => e.stopPropagation()}
                                  >
                                    <Download className="h-4 w-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent side="left">Download file</TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="mb-6">
                    <h3 className="mb-3 text-sm font-medium text-zinc-500 dark:text-zinc-400">Status</h3>
                    <div className="rounded-lg border border-zinc-200 bg-white p-4 dark:border-zinc-700 dark:bg-zinc-800">
                      <div className="mb-2 flex items-center">
                        <div className="mr-2 text-green-500">
                          <CheckCircle2 className="h-5 w-5" />
                        </div>
                        <h3 className="font-medium">Task completed</h3>
                      </div>
                      <p className="text-sm text-zinc-500 dark:text-zinc-400">
                        Tôi đã hoàn thành toàn bộ quy trình và gửi tài liệu Detectron2 cho người dùng, tiến trình đã kết
                        thúc.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Preview panel - only shown in split mode or chat mode */}
            {((viewMode === "split" && showPreview) || viewMode === "chat") && (
              <div
                className={`${
                  viewMode === "split" ? "w-2/3" : "w-full"
                } flex flex-col overflow-hidden bg-zinc-50 dark:bg-zinc-800`}
              >
                {viewMode === "split" && (
                  <div className="flex h-12 items-center justify-between border-b border-zinc-200 bg-white px-4 dark:border-zinc-700 dark:bg-zinc-900">
                    <div className="flex items-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button variant="ghost" size="icon" className="mr-2 h-8 w-8">
                              <Download className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent side="bottom">Download file</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      <h3 className="text-sm font-medium">
                        {selectedFile === "markdown"
                          ? "Detectron2: Nền tảng thị giác máy tính"
                          : "detectron2_document.pdf"}
                      </h3>
                    </div>
                    <div className="flex items-center gap-1">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <ChevronLeft className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent side="bottom">Previous file</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <ChevronRight className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent side="bottom">Next file</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <Maximize2 className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent side="bottom">Fullscreen</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      {viewMode === "split" && (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => setShowPreview(false)}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent side="bottom">Close preview</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                    </div>
                  </div>
                )}

                {viewMode === "chat" ? (
                  <ChatInterface selectedFile={selectedFile} />
                ) : (
                  <DocumentViewer type={selectedFile === "markdown" ? "markdown" : "pdf"} />
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
