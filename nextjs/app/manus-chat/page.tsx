"use client"

import { useEffect, useState } from "react";
import ManusChatMonitor from "@/components/manus-chat-monitor";
import { WebSocketProvider } from "@/contexts/WebSocketContext";

export default function ManusChat() {
  // Tạo session ID duy nhất cho mỗi phiên trò chuyện
  const [sessionId, setSessionId] = useState<string>("");

  useEffect(() => {
    // Kiểm tra xem đã có session ID trong localStorage chưa
    const storedSessionId = localStorage.getItem("manus_chat_session_id");

    if (storedSessionId) {
      // Sử dụng session ID đã lưu
      setSessionId(storedSessionId);
    } else {
      // Tạo session ID mới
      const newSessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      localStorage.setItem("manus_chat_session_id", newSessionId);
      setSessionId(newSessionId);
    }
  }, []);

  // Chỉ render khi đã có session ID
  if (!sessionId) {
    return <div className="container mx-auto py-8 text-center">Đang khởi tạo phiên trò chuyện...</div>;
  }

  // Tạo WebSocket endpoint với session ID
  const wsEndpoint = `ws://localhost:8000/ws/chat/${sessionId}`;

  return (
    <WebSocketProvider endpoint={wsEndpoint}>
      <div className="container mx-auto py-8">
        <h1 className="text-3xl font-bold mb-8 text-center">Manus AI Chat</h1>
        <ManusChatMonitor />
      </div>
    </WebSocketProvider>
  );
}