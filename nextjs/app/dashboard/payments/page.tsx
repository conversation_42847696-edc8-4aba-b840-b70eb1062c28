import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { CreditCard, Wallet, QrCode } from "lucide-react"
import DashboardHeader from "@/components/dashboard-header"

export default function PaymentsPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <DashboardHeader />

      <main className="flex-1 container py-6">
        <h1 className="text-2xl font-bold mb-6">Thanh toán</h1>

        <div className="grid gap-6 md:grid-cols-2">
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Nạp tiền vào tài khoản</CardTitle>
                <CardDescription><PERSON><PERSON><PERSON> phương thức thanh toán và số tiền bạn muốn nạp</CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="card">
                  <TabsList className="grid w-full grid-cols-3 mb-6">
                    <TabsTrigger value="card">
                      <CreditCard className="h-4 w-4 mr-2" />
                      Thẻ
                    </TabsTrigger>
                    <TabsTrigger value="banking">
                      <Wallet className="h-4 w-4 mr-2" />
                      Banking
                    </TabsTrigger>
                    <TabsTrigger value="qr">
                      <QrCode className="h-4 w-4 mr-2" />
                      QR Code
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="card" className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="cardNumber">Số thẻ</Label>
                      <Input id="cardNumber" placeholder="1234 5678 9012 3456" />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="expiry">Ngày hết hạn</Label>
                        <Input id="expiry" placeholder="MM/YY" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="cvv">CVV</Label>
                        <Input id="cvv" placeholder="123" />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="cardName">Tên chủ thẻ</Label>
                      <Input id="cardName" placeholder="NGUYEN VAN A" />
                    </div>
                  </TabsContent>

                  <TabsContent value="banking" className="space-y-4">
                    <div className="p-4 bg-gray-50 rounded-lg border">
                      <h3 className="font-medium mb-2">Thông tin chuyển khoản</h3>
                      <div className="space-y-1 text-sm">
                        <p>
                          <span className="font-medium">Ngân hàng:</span> Vietcombank
                        </p>
                        <p>
                          <span className="font-medium">Số tài khoản:</span> **********
                        </p>
                        <p>
                          <span className="font-medium">Chủ tài khoản:</span> CONG TY CHAT APP
                        </p>
                        <p>
                          <span className="font-medium">Nội dung:</span> NAP [email]
                        </p>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="transferAmount">Số tiền chuyển khoản</Label>
                      <Input id="transferAmount" placeholder="500,000" />
                    </div>
                  </TabsContent>

                  <TabsContent value="qr" className="space-y-4">
                    <div className="flex justify-center p-4">
                      <div className="bg-white p-4 border rounded-lg">
                        <div className="w-48 h-48 bg-gray-200 flex items-center justify-center">
                          <QrCode className="h-24 w-24 text-gray-400" />
                        </div>
                        <p className="text-center mt-2 text-sm">Quét mã QR để thanh toán</p>
                      </div>
                    </div>
                  </TabsContent>

                  <div className="mt-6 space-y-4">
                    <div className="space-y-2">
                      <Label>Chọn gói nạp tiền</Label>
                      <RadioGroup defaultValue="package2">
                        <div className="flex items-center space-x-2 border p-3 rounded-lg">
                          <RadioGroupItem value="package1" id="package1" />
                          <Label htmlFor="package1" className="flex-1">
                            100,000 VND
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2 border p-3 rounded-lg">
                          <RadioGroupItem value="package2" id="package2" />
                          <Label htmlFor="package2" className="flex-1">
                            200,000 VND
                          </Label>
                          <span className="text-sm text-teal-600 font-medium">Phổ biến</span>
                        </div>
                        <div className="flex items-center space-x-2 border p-3 rounded-lg">
                          <RadioGroupItem value="package3" id="package3" />
                          <Label htmlFor="package3" className="flex-1">
                            500,000 VND
                          </Label>
                          <span className="text-sm text-teal-600 font-medium">Tiết kiệm 5%</span>
                        </div>
                        <div className="flex items-center space-x-2 border p-3 rounded-lg">
                          <RadioGroupItem value="custom" id="custom" />
                          <Label htmlFor="custom" className="flex-1">
                            Tùy chọn
                          </Label>
                          <Input className="w-32" placeholder="VND" />
                        </div>
                      </RadioGroup>
                    </div>

                    <Button className="w-full">Thanh toán</Button>
                  </div>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Thông tin tài khoản</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 bg-gray-50 rounded-lg border">
                    <h3 className="font-medium mb-2">Số dư hiện tại</h3>
                    <p className="text-3xl font-bold">500,000 VND</p>
                  </div>

                  <div>
                    <h3 className="font-medium mb-2">Gói dịch vụ hiện tại</h3>
                    <div className="p-4 border rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <h4 className="font-medium">Gói Tiêu chuẩn</h4>
                        <span className="bg-teal-100 text-teal-800 text-xs px-2 py-1 rounded-full">Đang sử dụng</span>
                      </div>
                      <p className="text-sm text-gray-600 mb-4">
                        Truy cập không giới hạn đến các tính năng chat cơ bản
                      </p>
                      <Button variant="outline" size="sm" className="w-full">
                        Nâng cấp gói
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Lịch sử giao dịch gần đây</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <TransactionItem
                    type="deposit"
                    amount="200,000 VND"
                    date="15/05/2025"
                    status="success"
                    description="Nạp tiền qua thẻ tín dụng"
                  />
                  <TransactionItem
                    type="payment"
                    amount="50,000 VND"
                    date="10/05/2025"
                    status="success"
                    description="Thanh toán dịch vụ chat"
                  />
                  <TransactionItem
                    type="deposit"
                    amount="300,000 VND"
                    date="01/05/2025"
                    status="success"
                    description="Nạp tiền qua chuyển khoản ngân hàng"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}

function TransactionItem({ type, amount, date, status, description }) {
  return (
    <div className="flex items-start gap-4 p-3 rounded-lg hover:bg-gray-50 border">
      <div className={`p-2 rounded-full ${type === "deposit" ? "bg-green-100" : "bg-blue-100"}`}>
        {type === "deposit" ? (
          <Wallet className={`h-4 w-4 ${type === "deposit" ? "text-green-600" : "text-blue-600"}`} />
        ) : (
          <CreditCard className={`h-4 w-4 ${type === "deposit" ? "text-green-600" : "text-blue-600"}`} />
        )}
      </div>
      <div className="flex-1">
        <h4 className="font-medium">{type === "deposit" ? "Nạp tiền" : "Thanh toán"}</h4>
        <p className="text-sm text-gray-600">{description}</p>
      </div>
      <div className="text-right">
        <p className={`font-medium ${type === "deposit" ? "text-green-600" : "text-blue-600"}`}>
          {type === "deposit" ? "+" : "-"}
          {amount}
        </p>
        <p className="text-sm text-gray-500">{date}</p>
        <span
          className={`text-xs px-2 py-0.5 rounded-full ${
            status === "success"
              ? "bg-green-100 text-green-800"
              : status === "pending"
                ? "bg-yellow-100 text-yellow-800"
                : "bg-red-100 text-red-800"
          }`}
        >
          {status === "success" ? "Thành công" : status === "pending" ? "Đang xử lý" : "Thất bại"}
        </span>
      </div>
    </div>
  )
}
