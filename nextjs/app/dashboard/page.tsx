import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { MessageSquare, CreditCard, History, User, LogOut } from "lucide-react"
import DashboardHeader from "@/components/dashboard-header"

export default function DashboardPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <DashboardHeader />

      <main className="flex-1 container py-6">
        <div className="flex flex-col md:flex-row gap-6">
          <aside className="w-full md:w-64 space-y-4">
            <Card>
              <CardContent className="p-4">
                <nav className="space-y-2">
                  <Link href="/dashboard">
                    <Button variant="ghost" className="w-full justify-start">
                      <User className="mr-2 h-4 w-4" />
                      Tổng quan
                    </Button>
                  </Link>
                  <Link href="/chat">
                    <Button variant="ghost" className="w-full justify-start">
                      <MessageSquare className="mr-2 h-4 w-4" />
                      Chat
                    </Button>
                  </Link>
                  <Link href="/dashboard/payments">
                    <Button variant="ghost" className="w-full justify-start">
                      <CreditCard className="mr-2 h-4 w-4" />
                      Thanh toán
                    </Button>
                  </Link>
                  <Link href="/dashboard/history">
                    <Button variant="ghost" className="w-full justify-start">
                      <History className="mr-2 h-4 w-4" />
                      Lịch sử nạp tiền
                    </Button>
                  </Link>
                  <Link href="/dashboard/account">
                    <Button variant="ghost" className="w-full justify-start">
                      <User className="mr-2 h-4 w-4" />
                      Thông tin tài khoản
                    </Button>
                  </Link>
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-red-500 hover:text-red-600 hover:bg-red-50"
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    Đăng xuất
                  </Button>
                </nav>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="space-y-2">
                  <h3 className="font-medium">Số dư tài khoản</h3>
                  <p className="text-2xl font-bold">500,000 VND</p>
                  <Link href="/dashboard/payments">
                    <Button size="sm" className="w-full">
                      Nạp tiền
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </aside>

          <div className="flex-1 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Chào mừng, Nguyễn Văn A</CardTitle>
                <CardDescription>Quản lý tài khoản và sử dụng dịch vụ chat của chúng tôi</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  <DashboardCard
                    icon={<MessageSquare className="h-8 w-8 text-teal-500" />}
                    title="Tin nhắn"
                    value="24"
                    description="Tin nhắn trong 7 ngày qua"
                  />
                  <DashboardCard
                    icon={<CreditCard className="h-8 w-8 text-teal-500" />}
                    title="Giao dịch"
                    value="3"
                    description="Giao dịch trong tháng này"
                  />
                  <DashboardCard
                    icon={<History className="h-8 w-8 text-teal-500" />}
                    title="Thời gian sử dụng"
                    value="14 giờ"
                    description="Thời gian sử dụng trong tháng"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Hoạt động gần đây</CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="messages">
                  <TabsList className="mb-4">
                    <TabsTrigger value="messages">Tin nhắn</TabsTrigger>
                    <TabsTrigger value="payments">Thanh toán</TabsTrigger>
                  </TabsList>
                  <TabsContent value="messages">
                    <div className="space-y-4">
                      <ActivityItem
                        title="Cuộc trò chuyện mới"
                        description="Bạn đã bắt đầu một cuộc trò chuyện mới"
                        time="2 giờ trước"
                      />
                      <ActivityItem
                        title="Tin nhắn đã gửi"
                        description="Bạn đã gửi 5 tin nhắn trong cuộc trò chuyện"
                        time="Hôm qua"
                      />
                      <ActivityItem
                        title="Cuộc trò chuyện đã kết thúc"
                        description="Cuộc trò chuyện đã kết thúc sau 30 phút"
                        time="3 ngày trước"
                      />
                    </div>
                  </TabsContent>
                  <TabsContent value="payments">
                    <div className="space-y-4">
                      <ActivityItem
                        title="Nạp tiền thành công"
                        description="Bạn đã nạp 200,000 VND vào tài khoản"
                        time="1 tuần trước"
                      />
                      <ActivityItem
                        title="Thanh toán dịch vụ"
                        description="Thanh toán 50,000 VND cho dịch vụ chat"
                        time="2 tuần trước"
                      />
                      <ActivityItem
                        title="Nạp tiền thành công"
                        description="Bạn đã nạp 300,000 VND vào tài khoản"
                        time="1 tháng trước"
                      />
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}

function DashboardCard({ icon, title, value, description }) {
  return (
    <div className="bg-gray-50 p-4 rounded-lg border">
      <div className="flex items-center gap-4">
        <div>{icon}</div>
        <div>
          <h3 className="font-medium text-gray-600">{title}</h3>
          <p className="text-2xl font-bold">{value}</p>
          <p className="text-sm text-gray-500">{description}</p>
        </div>
      </div>
    </div>
  )
}

function ActivityItem({ title, description, time }) {
  return (
    <div className="flex items-start gap-4 p-3 rounded-lg hover:bg-gray-50">
      <div className="bg-teal-100 p-2 rounded-full">
        <History className="h-4 w-4 text-teal-600" />
      </div>
      <div className="flex-1">
        <h4 className="font-medium">{title}</h4>
        <p className="text-sm text-gray-600">{description}</p>
      </div>
      <div className="text-sm text-gray-500">{time}</div>
    </div>
  )
}
