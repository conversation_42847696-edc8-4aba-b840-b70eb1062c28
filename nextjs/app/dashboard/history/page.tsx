import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Download, Search, Filter } from "lucide-react"
import DashboardHeader from "@/components/dashboard-header"

export default function HistoryPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <DashboardHeader />

      <main className="flex-1 container py-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">L<PERSON><PERSON> sử nạp tiền</h1>
          <Button variant="outline" size="sm" className="gap-2">
            <Download className="h-4 w-4" />
            <PERSON><PERSON><PERSON> b<PERSON><PERSON> c<PERSON>o
          </Button>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Tìm kiếm giao dịch</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                <Input className="pl-9" placeholder="Tìm kiếm theo mô tả hoặc ID giao dịch" />
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Loại giao dịch" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tất cả</SelectItem>
                    <SelectItem value="deposit">Nạp tiền</SelectItem>
                    <SelectItem value="payment">Thanh toán</SelectItem>
                  </SelectContent>
                </Select>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Trạng thái" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tất cả</SelectItem>
                    <SelectItem value="success">Thành công</SelectItem>
                    <SelectItem value="pending">Đang xử lý</SelectItem>
                    <SelectItem value="failed">Thất bại</SelectItem>
                  </SelectContent>
                </Select>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Thời gian" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tất cả</SelectItem>
                    <SelectItem value="today">Hôm nay</SelectItem>
                    <SelectItem value="week">Tuần này</SelectItem>
                    <SelectItem value="month">Tháng này</SelectItem>
                    <SelectItem value="year">Năm nay</SelectItem>
                  </SelectContent>
                </Select>
                <Button className="gap-2">
                  <Filter className="h-4 w-4" />
                  Lọc
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID giao dịch</TableHead>
                  <TableHead>Ngày</TableHead>
                  <TableHead>Loại</TableHead>
                  <TableHead>Phương thức</TableHead>
                  <TableHead>Mô tả</TableHead>
                  <TableHead>Số tiền</TableHead>
                  <TableHead>Trạng thái</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell className="font-mono text-xs">#TRX12345678</TableCell>
                  <TableCell>15/05/2025</TableCell>
                  <TableCell>
                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Nạp tiền</span>
                  </TableCell>
                  <TableCell>Thẻ tín dụng</TableCell>
                  <TableCell>Nạp tiền vào tài khoản</TableCell>
                  <TableCell className="font-medium text-green-600">+200,000 VND</TableCell>
                  <TableCell>
                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Thành công</span>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-mono text-xs">#TRX12345677</TableCell>
                  <TableCell>10/05/2025</TableCell>
                  <TableCell>
                    <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Thanh toán</span>
                  </TableCell>
                  <TableCell>Tài khoản</TableCell>
                  <TableCell>Thanh toán dịch vụ chat</TableCell>
                  <TableCell className="font-medium text-blue-600">-50,000 VND</TableCell>
                  <TableCell>
                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Thành công</span>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-mono text-xs">#TRX12345676</TableCell>
                  <TableCell>01/05/2025</TableCell>
                  <TableCell>
                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Nạp tiền</span>
                  </TableCell>
                  <TableCell>Chuyển khoản</TableCell>
                  <TableCell>Nạp tiền qua ngân hàng</TableCell>
                  <TableCell className="font-medium text-green-600">+300,000 VND</TableCell>
                  <TableCell>
                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Thành công</span>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-mono text-xs">#TRX12345675</TableCell>
                  <TableCell>25/04/2025</TableCell>
                  <TableCell>
                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Nạp tiền</span>
                  </TableCell>
                  <TableCell>Thẻ tín dụng</TableCell>
                  <TableCell>Nạp tiền vào tài khoản</TableCell>
                  <TableCell className="font-medium text-green-600">+100,000 VND</TableCell>
                  <TableCell>
                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Thành công</span>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-mono text-xs">#TRX12345674</TableCell>
                  <TableCell>20/04/2025</TableCell>
                  <TableCell>
                    <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Thanh toán</span>
                  </TableCell>
                  <TableCell>Tài khoản</TableCell>
                  <TableCell>Thanh toán dịch vụ chat</TableCell>
                  <TableCell className="font-medium text-blue-600">-50,000 VND</TableCell>
                  <TableCell>
                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Thành công</span>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <div className="flex items-center justify-between mt-6">
          <div className="text-sm text-gray-500">Hiển thị 1-5 của 12 giao dịch</div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              Trước
            </Button>
            <Button variant="outline" size="sm" className="bg-teal-50">
              1
            </Button>
            <Button variant="outline" size="sm">
              2
            </Button>
            <Button variant="outline" size="sm">
              3
            </Button>
            <Button variant="outline" size="sm">
              Sau
            </Button>
          </div>
        </div>
      </main>
    </div>
  )
}
