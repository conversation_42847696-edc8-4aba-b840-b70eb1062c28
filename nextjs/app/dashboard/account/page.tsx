"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Switch } from "@/components/ui/switch"
import { User, Lock, Bell, Shield, Upload } from "lucide-react"
import DashboardHeader from "@/components/dashboard-header"

export default function AccountPage() {
  const [isEditing, setIsEditing] = useState(false)

  return (
    <div className="min-h-screen flex flex-col">
      <DashboardHeader />

      <main className="flex-1 container py-6">
        <h1 className="text-2xl font-bold mb-6">Thông tin tài khoản</h1>

        <div className="grid gap-6 md:grid-cols-3">
          <div className="md:col-span-1">
            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col items-center space-y-4">
                  <Avatar className="h-24 w-24">
                    <AvatarImage src="/placeholder.svg?height=96&width=96" />
                    <AvatarFallback>NA</AvatarFallback>
                  </Avatar>
                  <div className="text-center">
                    <h2 className="text-xl font-bold">Nguyễn Văn A</h2>
                    <p className="text-gray-500"><EMAIL></p>
                  </div>
                  <Button variant="outline" size="sm" className="gap-2">
                    <Upload className="h-4 w-4" />
                    Thay đổi ảnh
                  </Button>
                </div>

                <div className="mt-6 space-y-1">
                  <Button variant="ghost" className="w-full justify-start" size="sm">
                    <User className="mr-2 h-4 w-4" />
                    Thông tin cá nhân
                  </Button>
                  <Button variant="ghost" className="w-full justify-start" size="sm">
                    <Lock className="mr-2 h-4 w-4" />
                    Bảo mật
                  </Button>
                  <Button variant="ghost" className="w-full justify-start" size="sm">
                    <Bell className="mr-2 h-4 w-4" />
                    Thông báo
                  </Button>
                  <Button variant="ghost" className="w-full justify-start" size="sm">
                    <Shield className="mr-2 h-4 w-4" />
                    Quyền riêng tư
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="md:col-span-2">
            <Tabs defaultValue="profile">
              <TabsList className="mb-6">
                <TabsTrigger value="profile">Thông tin cá nhân</TabsTrigger>
                <TabsTrigger value="security">Bảo mật</TabsTrigger>
                <TabsTrigger value="notifications">Thông báo</TabsTrigger>
              </TabsList>

              <TabsContent value="profile">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>Thông tin cá nhân</CardTitle>
                        <CardDescription>Cập nhật thông tin cá nhân của bạn</CardDescription>
                      </div>
                      <Button variant={isEditing ? "ghost" : "outline"} onClick={() => setIsEditing(!isEditing)}>
                        {isEditing ? "Hủy" : "Chỉnh sửa"}
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="fullName">Họ tên</Label>
                        <Input id="fullName" defaultValue="Nguyễn Văn A" disabled={!isEditing} />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input id="email" type="email" defaultValue="<EMAIL>" disabled={!isEditing} />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone">Số điện thoại</Label>
                        <Input id="phone" defaultValue="0912345678" disabled={!isEditing} />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="dob">Ngày sinh</Label>
                        <Input id="dob" type="date" defaultValue="1990-01-01" disabled={!isEditing} />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="address">Địa chỉ</Label>
                      <Input
                        id="address"
                        defaultValue="123 Đường ABC, Quận XYZ, TP. Hồ Chí Minh"
                        disabled={!isEditing}
                      />
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end">{isEditing && <Button>Lưu thay đổi</Button>}</CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="security">
                <Card>
                  <CardHeader>
                    <CardTitle>Bảo mật</CardTitle>
                    <CardDescription>Quản lý mật khẩu và bảo mật tài khoản</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <h3 className="font-medium">Thay đổi mật khẩu</h3>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="currentPassword">Mật khẩu hiện tại</Label>
                          <Input id="currentPassword" type="password" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="newPassword">Mật khẩu mới</Label>
                          <Input id="newPassword" type="password" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="confirmPassword">Xác nhận mật khẩu mới</Label>
                          <Input id="confirmPassword" type="password" />
                        </div>
                      </div>
                      <Button>Cập nhật mật khẩu</Button>
                    </div>

                    <div className="space-y-4 pt-4 border-t">
                      <h3 className="font-medium">Xác thực hai yếu tố</h3>
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Xác thực qua SMS</p>
                          <p className="text-sm text-gray-500">Nhận mã xác thực qua tin nhắn SMS</p>
                        </div>
                        <Switch />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Ứng dụng xác thực</p>
                          <p className="text-sm text-gray-500">Sử dụng ứng dụng xác thực như Google Authenticator</p>
                        </div>
                        <Switch />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="notifications">
                <Card>
                  <CardHeader>
                    <CardTitle>Thông báo</CardTitle>
                    <CardDescription>Quản lý cài đặt thông báo</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Thông báo qua email</p>
                        <p className="text-sm text-gray-500">Nhận thông báo qua email</p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Thông báo thanh toán</p>
                        <p className="text-sm text-gray-500">Nhận thông báo về các giao dịch thanh toán</p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Cập nhật hệ thống</p>
                        <p className="text-sm text-gray-500">Nhận thông báo về các cập nhật hệ thống</p>
                      </div>
                      <Switch />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Tin tức và khuyến mãi</p>
                        <p className="text-sm text-gray-500">Nhận thông tin về tin tức và khuyến mãi</p>
                      </div>
                      <Switch />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>
    </div>
  )
}
