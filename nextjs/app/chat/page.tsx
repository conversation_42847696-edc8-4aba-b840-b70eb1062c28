"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Send,
  Plus,
  ImageIcon,
  Paperclip,
  Smile,
  MoreVertical,
  Search,
  Settings,
  ChevronLeft,
  Edit,
  Trash2,
  Copy,
} from "lucide-react"
import DashboardHeader from "@/components/dashboard-header"
import { cn } from "@/lib/utils"

export default function ChatPage() {
  const [conversations, setConversations] = useState([
    { id: 1, name: "<PERSON>t mới", preview: "Bắt đầu cuộc trò chuyện mới", time: "Bây giờ", unread: 0, active: true },
    {
      id: 2,
      name: "Hỗ tr<PERSON> kỹ thuật",
      preview: "Cảm ơn bạn đã liên hệ với chúng tôi",
      time: "10:30",
      unread: 2,
      active: false,
    },
    {
      id: 3,
      name: "<PERSON><PERSON> vấn sản phẩm",
      preview: "Chúng tôi có thể giúp gì cho bạn?",
      time: "Hôm qua",
      unread: 0,
      active: false,
    },
    {
      id: 4,
      name: "Báo cáo vấn đề",
      preview: "Vấn đề của bạn đã được ghi nhận",
      time: "Thứ 2",
      unread: 0,
      active: false,
    },
  ])

  const [messages, setMessages] = useState([
    { id: 1, text: "Xin chào! Tôi có thể giúp gì cho bạn?", sender: "bot", time: "10:30" },
    { id: 2, text: "Tôi cần hỗ trợ về cách sử dụng ứng dụng", sender: "user", time: "10:31" },
    {
      id: 3,
      text: "Tất nhiên, tôi rất vui được giúp đỡ bạn. Bạn đang gặp khó khăn ở phần nào của ứng dụng?",
      sender: "bot",
      time: "10:32",
    },
    { id: 4, text: "Tôi không biết cách thanh toán", sender: "user", time: "10:33" },
    {
      id: 5,
      text: 'Để thanh toán, bạn có thể vào mục "Thanh toán" trong trang Dashboard. Tại đó, bạn có thể chọn phương thức thanh toán như thẻ tín dụng, chuyển khoản ngân hàng hoặc quét mã QR. Sau khi chọn phương thức, bạn nhập số tiền cần nạp và làm theo hướng dẫn.',
      sender: "bot",
      time: "10:34",
    },
    {
      id: 6,
      text: 'Bạn có thể xem lịch sử thanh toán trong mục "Lịch sử nạp tiền" để theo dõi các giao dịch đã thực hiện.',
      sender: "bot",
      time: "10:34",
    },
    { id: 7, text: "Cảm ơn bạn, tôi sẽ thử làm theo", sender: "user", time: "10:35" },
  ])

  const [inputValue, setInputValue] = useState("")
  const [showPreview, setShowPreview] = useState(false)
  const [previewMessage, setPreviewMessage] = useState("")
  const messagesEndRef = useRef(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = () => {
    if (inputValue.trim()) {
      const newMessage = {
        id: messages.length + 1,
        text: inputValue,
        sender: "user",
        time: new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
      }

      setMessages([...messages, newMessage])
      setInputValue("")
      setPreviewMessage("")

      // Simulate bot response
      setTimeout(() => {
        const botResponse = {
          id: messages.length + 2,
          text: "Cảm ơn bạn đã gửi tin nhắn. Tôi sẽ phản hồi sớm nhất có thể.",
          sender: "bot",
          time: new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
        }

        setMessages((prev) => [...prev, botResponse])
      }, 1000)
    }
  }

  const handleInputChange = (e) => {
    const value = e.target.value
    setInputValue(value)
    setPreviewMessage(value)
  }

  const handleKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <div className="min-h-screen flex flex-col">
      <DashboardHeader />

      <main className="flex-1 flex">
        <div className="w-full flex">
          {/* Sidebar */}
          <div className="w-80 border-r flex flex-col bg-gray-50">
            <div className="p-4 border-b bg-white">
              <div className="flex items-center justify-between mb-4">
                <h2 className="font-bold text-lg">Tin nhắn</h2>
                <Button variant="ghost" size="icon">
                  <Plus className="h-5 w-5" />
                </Button>
              </div>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                <Input className="pl-9" placeholder="Tìm kiếm cuộc trò chuyện" />
              </div>
            </div>

            <ScrollArea className="flex-1">
              <div className="p-2">
                {conversations.map((conversation) => (
                  <div
                    key={conversation.id}
                    className={cn(
                      "flex items-start gap-3 p-3 rounded-lg cursor-pointer hover:bg-gray-100",
                      conversation.active && "bg-teal-50 hover:bg-teal-50",
                    )}
                  >
                    <Avatar>
                      <AvatarImage src={`/placeholder.svg?height=40&width=40`} />
                      <AvatarFallback>{conversation.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium truncate">{conversation.name}</h3>
                        <span className="text-xs text-gray-500">{conversation.time}</span>
                      </div>
                      <p className="text-sm text-gray-600 truncate">{conversation.preview}</p>
                    </div>
                    {conversation.unread > 0 && (
                      <div className="bg-teal-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                        {conversation.unread}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>

          {/* Chat Area */}
          <div className="flex-1 flex flex-col">
            <div className="p-4 border-b flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Avatar>
                  <AvatarImage src={`/placeholder.svg?height=40&width=40`} />
                  <AvatarFallback>C</AvatarFallback>
                </Avatar>
                <div>
                  <h2 className="font-medium">Chat mới</h2>
                  <p className="text-xs text-gray-500">Đang hoạt động</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="icon">
                  <Search className="h-5 w-5" />
                </Button>
                <Button variant="ghost" size="icon">
                  <Settings className="h-5 w-5" />
                </Button>
                <Button variant="ghost" size="icon">
                  <MoreVertical className="h-5 w-5" />
                </Button>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto p-4 bg-gray-50">
              <div className="space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={cn("flex", message.sender === "user" ? "justify-end" : "justify-start")}
                  >
                    <div className="flex gap-3 max-w-[80%]">
                      {message.sender === "bot" && (
                        <Avatar>
                          <AvatarImage src={`/placeholder.svg?height=40&width=40`} />
                          <AvatarFallback>B</AvatarFallback>
                        </Avatar>
                      )}
                      <div>
                        <div
                          className={cn(
                            "p-3 rounded-lg",
                            message.sender === "user" ? "bg-teal-500 text-white" : "bg-white border",
                          )}
                        >
                          <p>{message.text}</p>
                        </div>
                        <div className="flex items-center mt-1 gap-2">
                          <span className="text-xs text-gray-500">{message.time}</span>
                          {message.sender === "user" && (
                            <div className="flex items-center gap-1">
                              <Button variant="ghost" size="icon" className="h-6 w-6">
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button variant="ghost" size="icon" className="h-6 w-6">
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                          {message.sender === "bot" && (
                            <Button variant="ghost" size="icon" className="h-6 w-6">
                              <Copy className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </div>
                      {message.sender === "user" && (
                        <Avatar>
                          <AvatarImage src={`/placeholder.svg?height=40&width=40`} />
                          <AvatarFallback>U</AvatarFallback>
                        </Avatar>
                      )}
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
            </div>

            {/* Message Preview */}
            {showPreview && previewMessage && (
              <div className="px-4 py-2 border-t bg-gray-50">
                <div className="flex items-center gap-2">
                  <div className="text-xs font-medium text-gray-500">Xem trước:</div>
                  <div className="text-sm text-gray-700 truncate">{previewMessage}</div>
                </div>
              </div>
            )}

            {/* Input Area */}
            <div className="p-4 border-t bg-white">
              <div className="flex items-end gap-2">
                <div className="flex-1">
                  <Textarea
                    placeholder="Nhập tin nhắn..."
                    value={inputValue}
                    onChange={handleInputChange}
                    onKeyDown={handleKeyDown}
                    className="min-h-[60px] max-h-[200px]"
                  />
                </div>
                <div className="flex flex-col gap-2">
                  <div className="flex gap-1">
                    <Button variant="ghost" size="icon" onClick={() => setShowPreview(!showPreview)}>
                      <ChevronLeft className={cn("h-5 w-5 transition-transform", showPreview && "rotate-180")} />
                    </Button>
                    <Button variant="ghost" size="icon">
                      <Paperclip className="h-5 w-5" />
                    </Button>
                    <Button variant="ghost" size="icon">
                      <ImageIcon className="h-5 w-5" />
                    </Button>
                    <Button variant="ghost" size="icon">
                      <Smile className="h-5 w-5" />
                    </Button>
                  </div>
                  <Button onClick={handleSendMessage} className="gap-2">
                    <Send className="h-4 w-4" />
                    Gửi
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
