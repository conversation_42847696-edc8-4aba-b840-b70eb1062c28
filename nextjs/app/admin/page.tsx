import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Users,
  CreditCard,
  MessageSquare,
  Activity,
  BarChart,
  Settings,
  LogOut,
  ChevronUp,
  ChevronDown,
} from "lucide-react"
import AdminHeader from "@/components/admin-header"

export default function AdminDashboardPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <AdminHeader />

      <div className="flex flex-1">
        {/* Sidebar */}
        <aside className="w-64 border-r bg-gray-50">
          <div className="p-4">
            <nav className="space-y-1">
              <Link href="/admin">
                <Button variant="secondary" className="w-full justify-start">
                  <BarChart className="mr-2 h-4 w-4" />
                  Tổng quan
                </Button>
              </Link>
              <Link href="/admin/users">
                <Button variant="ghost" className="w-full justify-start">
                  <Users className="mr-2 h-4 w-4" />
                  Quản lý người dùng
                </Button>
              </Link>
              <Link href="/admin/payments">
                <Button variant="ghost" className="w-full justify-start">
                  <CreditCard className="mr-2 h-4 w-4" />
                  Quản lý thanh toán
                </Button>
              </Link>
              <Link href="/admin/chats">
                <Button variant="ghost" className="w-full justify-start">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Quản lý chat
                </Button>
              </Link>
              <Link href="/admin/settings">
                <Button variant="ghost" className="w-full justify-start">
                  <Settings className="mr-2 h-4 w-4" />
                  Cài đặt hệ thống
                </Button>
              </Link>
              <Button variant="ghost" className="w-full justify-start text-red-500 hover:text-red-600 hover:bg-red-50">
                <LogOut className="mr-2 h-4 w-4" />
                Đăng xuất
              </Button>
            </nav>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6 overflow-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold">Tổng quan hệ thống</h1>
            <p className="text-gray-500">Xem thống kê và hoạt động của hệ thống</p>
          </div>

          {/* Stats Cards */}
          <div className="grid gap-6 mb-8 md:grid-cols-2 lg:grid-cols-4">
            <StatsCard
              title="Tổng người dùng"
              value="1,234"
              change="+12%"
              trend="up"
              icon={<Users className="h-8 w-8 text-teal-500" />}
            />
            <StatsCard
              title="Doanh thu tháng"
              value="45,678,000 VND"
              change="+8%"
              trend="up"
              icon={<CreditCard className="h-8 w-8 text-teal-500" />}
            />
            <StatsCard
              title="Tin nhắn hôm nay"
              value="5,678"
              change="-3%"
              trend="down"
              icon={<MessageSquare className="h-8 w-8 text-teal-500" />}
            />
            <StatsCard
              title="Người dùng mới"
              value="78"
              change="+24%"
              trend="up"
              icon={<Activity className="h-8 w-8 text-teal-500" />}
            />
          </div>

          {/* Charts and Tables */}
          <div className="grid gap-6 mb-8 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Thống kê người dùng</CardTitle>
                <CardDescription>Số lượng người dùng đăng ký mới trong 7 ngày qua</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80 flex items-end gap-2">
                  {[35, 45, 30, 65, 85, 65, 75].map((height, index) => (
                    <div key={index} className="flex-1 flex flex-col items-center gap-2">
                      <div className="w-full bg-teal-100 rounded-t-md" style={{ height: `${height}%` }}>
                        <div className="w-full bg-teal-500 rounded-t-md h-full" style={{ opacity: height / 100 }}></div>
                      </div>
                      <span className="text-xs text-gray-500">{["T2", "T3", "T4", "T5", "T6", "T7", "CN"][index]}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Doanh thu</CardTitle>
                <CardDescription>Doanh thu theo tháng trong năm 2025</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80 flex items-end gap-2">
                  {[40, 35, 45, 30, 55, 65, 75, 85, 65, 75, 60, 50].map((height, index) => (
                    <div key={index} className="flex-1 flex flex-col items-center gap-2">
                      <div className="w-full bg-teal-100 rounded-t-md" style={{ height: `${height}%` }}>
                        <div className="w-full bg-teal-500 rounded-t-md h-full" style={{ opacity: height / 100 }}></div>
                      </div>
                      <span className="text-xs text-gray-500">
                        {["T1", "T2", "T3", "T4", "T5", "T6", "T7", "T8", "T9", "T10", "T11", "T12"][index]}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-6 mb-8 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Người dùng mới gần đây</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <UserItem name="Nguyễn Văn A" email="<EMAIL>" date="15/05/2025" status="active" />
                  <UserItem name="Trần Thị B" email="<EMAIL>" date="14/05/2025" status="active" />
                  <UserItem name="Lê Văn C" email="<EMAIL>" date="13/05/2025" status="pending" />
                  <UserItem name="Phạm Thị D" email="<EMAIL>" date="12/05/2025" status="active" />
                  <UserItem name="Hoàng Văn E" email="<EMAIL>" date="11/05/2025" status="inactive" />
                </div>
                <div className="mt-4 text-center">
                  <Link href="/admin/users">
                    <Button variant="link">Xem tất cả người dùng</Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Giao dịch gần đây</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <TransactionItem user="Nguyễn Văn A" amount="200,000 VND" date="15/05/2025" status="success" />
                  <TransactionItem user="Trần Thị B" amount="500,000 VND" date="14/05/2025" status="success" />
                  <TransactionItem user="Lê Văn C" amount="100,000 VND" date="13/05/2025" status="pending" />
                  <TransactionItem user="Phạm Thị D" amount="300,000 VND" date="12/05/2025" status="success" />
                  <TransactionItem user="Hoàng Văn E" amount="150,000 VND" date="11/05/2025" status="failed" />
                </div>
                <div className="mt-4 text-center">
                  <Link href="/admin/payments">
                    <Button variant="link">Xem tất cả giao dịch</Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Hoạt động hệ thống</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="all">
                <TabsList className="mb-4">
                  <TabsTrigger value="all">Tất cả</TabsTrigger>
                  <TabsTrigger value="users">Người dùng</TabsTrigger>
                  <TabsTrigger value="payments">Thanh toán</TabsTrigger>
                  <TabsTrigger value="system">Hệ thống</TabsTrigger>
                </TabsList>

                <TabsContent value="all">
                  <div className="space-y-4">
                    <ActivityLogItem
                      type="user"
                      message="Người dùng mới đăng ký: Nguyễn Văn A"
                      time="15/05/2025 10:30"
                    />
                    <ActivityLogItem
                      type="payment"
                      message="Giao dịch thành công: 200,000 VND từ Nguyễn Văn A"
                      time="15/05/2025 10:35"
                    />
                    <ActivityLogItem
                      type="system"
                      message="Cập nhật hệ thống phiên bản 1.2.3"
                      time="14/05/2025 22:15"
                    />
                    <ActivityLogItem type="user" message="Người dùng mới đăng ký: Trần Thị B" time="14/05/2025 15:45" />
                    <ActivityLogItem
                      type="payment"
                      message="Giao dịch thành công: 500,000 VND từ Trần Thị B"
                      time="14/05/2025 16:00"
                    />
                  </div>
                </TabsContent>

                <TabsContent value="users">
                  <div className="space-y-4">
                    <ActivityLogItem
                      type="user"
                      message="Người dùng mới đăng ký: Nguyễn Văn A"
                      time="15/05/2025 10:30"
                    />
                    <ActivityLogItem type="user" message="Người dùng mới đăng ký: Trần Thị B" time="14/05/2025 15:45" />
                    <ActivityLogItem
                      type="user"
                      message="Người dùng cập nhật thông tin: Lê Văn C"
                      time="13/05/2025 09:20"
                    />
                  </div>
                </TabsContent>

                <TabsContent value="payments">
                  <div className="space-y-4">
                    <ActivityLogItem
                      type="payment"
                      message="Giao dịch thành công: 200,000 VND từ Nguyễn Văn A"
                      time="15/05/2025 10:35"
                    />
                    <ActivityLogItem
                      type="payment"
                      message="Giao dịch thành công: 500,000 VND từ Trần Thị B"
                      time="14/05/2025 16:00"
                    />
                    <ActivityLogItem
                      type="payment"
                      message="Giao dịch đang xử lý: 100,000 VND từ Lê Văn C"
                      time="13/05/2025 14:10"
                    />
                  </div>
                </TabsContent>

                <TabsContent value="system">
                  <div className="space-y-4">
                    <ActivityLogItem
                      type="system"
                      message="Cập nhật hệ thống phiên bản 1.2.3"
                      time="14/05/2025 22:15"
                    />
                    <ActivityLogItem type="system" message="Bảo trì hệ thống hoàn tất" time="10/05/2025 05:30" />
                    <ActivityLogItem type="system" message="Bắt đầu bảo trì hệ thống" time="10/05/2025 01:00" />
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  )
}

function StatsCard({ title, value, change, trend, icon }) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-gray-500 font-medium">{title}</h3>
          {icon}
        </div>
        <div className="flex items-end justify-between">
          <p className="text-2xl font-bold">{value}</p>
          <div className={`flex items-center ${trend === "up" ? "text-green-600" : "text-red-600"}`}>
            {trend === "up" ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            <span className="text-sm font-medium">{change}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

function UserItem({ name, email, date, status }) {
  return (
    <div className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg">
      <div className="flex items-center gap-3">
        <div className="bg-gray-100 rounded-full h-10 w-10 flex items-center justify-center">
          <Users className="h-5 w-5 text-gray-600" />
        </div>
        <div>
          <h4 className="font-medium">{name}</h4>
          <p className="text-sm text-gray-500">{email}</p>
        </div>
      </div>
      <div className="text-right">
        <p className="text-sm text-gray-500">{date}</p>
        <span
          className={`text-xs px-2 py-0.5 rounded-full ${
            status === "active"
              ? "bg-green-100 text-green-800"
              : status === "pending"
                ? "bg-yellow-100 text-yellow-800"
                : "bg-gray-100 text-gray-800"
          }`}
        >
          {status === "active" ? "Hoạt động" : status === "pending" ? "Chờ xác nhận" : "Không hoạt động"}
        </span>
      </div>
    </div>
  )
}

function TransactionItem({ user, amount, date, status }) {
  return (
    <div className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg">
      <div className="flex items-center gap-3">
        <div className="bg-teal-100 rounded-full h-10 w-10 flex items-center justify-center">
          <CreditCard className="h-5 w-5 text-teal-600" />
        </div>
        <div>
          <h4 className="font-medium">{user}</h4>
          <p className="text-sm text-gray-500">{date}</p>
        </div>
      </div>
      <div className="text-right">
        <p className="font-medium text-teal-600">{amount}</p>
        <span
          className={`text-xs px-2 py-0.5 rounded-full ${
            status === "success"
              ? "bg-green-100 text-green-800"
              : status === "pending"
                ? "bg-yellow-100 text-yellow-800"
                : "bg-red-100 text-red-800"
          }`}
        >
          {status === "success" ? "Thành công" : status === "pending" ? "Đang xử lý" : "Thất bại"}
        </span>
      </div>
    </div>
  )
}

function ActivityLogItem({ type, message, time }) {
  return (
    <div className="flex items-start gap-3 p-3 hover:bg-gray-50 rounded-lg">
      <div
        className={`rounded-full h-8 w-8 flex items-center justify-center ${
          type === "user" ? "bg-blue-100" : type === "payment" ? "bg-green-100" : "bg-purple-100"
        }`}
      >
        {type === "user" ? (
          <Users className={`h-4 w-4 text-blue-600`} />
        ) : type === "payment" ? (
          <CreditCard className={`h-4 w-4 text-green-600`} />
        ) : (
          <Settings className={`h-4 w-4 text-purple-600`} />
        )}
      </div>
      <div className="flex-1">
        <p className="text-gray-800">{message}</p>
        <p className="text-xs text-gray-500">{time}</p>
      </div>
    </div>
  )
}
