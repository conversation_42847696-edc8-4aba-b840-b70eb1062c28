/**
 * Manus Chat HTML Selectors Constants
 * Các selector để crawl dữ liệu chat từ Manus.im
 */

export const MANUS_SELECTORS = {
  // Main chat container
  CHAT_CONTAINER: {
    // Container ch<PERSON><PERSON> chứa toàn bộ chat content
    MAIN_WRAPPER: '.simplebar-content',
    // Scrollable content area
    SCROLLABLE_CONTENT: '[role="scrollable content"]',
    // Chat messages container
    MESSAGES_CONTAINER: 'div[class*="flex flex-col"]'
  },

  // Input elements
  INPUT: {
    // Textarea cho input message
    MESSAGE_INPUT: 'textarea[placeholder*="Give <PERSON><PERSON> a task to work on"]',
    MESSAGE_INPUT_ACTIVE: 'textarea[placeholder*="Send message to Manus"]',
    // Send button
    SEND_BUTTON: 'button[class*="flex items-center justify-center"]',
    // Alternative send button selector
    SEND_BUTTON_ALT: 'button:has(img[alt*="send"])'
  },

  // Chat messages
  MESSAGES: {
    // User messages
    USER_MESSAGE: 'div[class*="flex justify-end"]',
    USER_MESSAGE_CONTENT: 'div[class*="bg-[var(--primary)]"]',
    
    // AI messages
    AI_MESSAGE: 'div[class*="prose"]',
    AI_MESSAGE_CONTAINER: 'div:has(.prose)',
    
    // Message timestamp
    TIMESTAMP: 'div[class*="text-xs text-[var(--text-secondary)]"]',
    
    // Message content wrapper
    MESSAGE_WRAPPER: 'div[class*="max-w-[80%]"]'
  },

  // Task completion indicators
  COMPLETION: {
    // Success notification
    SUCCESS_NOTIFICATION: 'div[class*="px-3 py-\\[5px\\] rounded-full text-\\[var\\(--function-success\\)\\]"]',
    SUCCESS_TEXT: 'div:contains("Manus has completed the current task")',
    
    // Task status indicators
    TASK_STATUS: 'div[class*="text-[var(--function-success)]"]',
    COMPLETION_BADGE: 'div[class*="rounded-full"][class*="text-[13px]"]'
  },

  // Navigation and UI elements
  UI: {
    // Sidebar
    SIDEBAR: 'div[class*="w-64"]',
    SIDEBAR_CONTENT: 'nav[class*="flex flex-col"]',
    
    // Header
    HEADER: 'header',
    TITLE: 'h1, h2, h3',
    
    // New task button
    NEW_TASK_BUTTON: 'button:contains("New task")',
    
    // Settings and menu
    MENU_BUTTON: 'button[class*="cursor-pointer"]',
    SETTINGS_ICON: 'img[alt*="settings"], img[alt*="menu"]'
  },

  // Content extraction
  CONTENT: {
    // Text content
    TEXT_CONTENT: 'p, span, div:not(:has(*))',
    
    // Code blocks
    CODE_BLOCK: 'pre, code',
    
    // Links
    LINKS: 'a[href]',
    
    // Images
    IMAGES: 'img[src]',
    
    // Lists
    LISTS: 'ul, ol, li'
  },

  // Chat history
  HISTORY: {
    // Chat session container
    SESSION_CONTAINER: 'div[class*="flex flex-col gap-4"]',
    
    // Individual chat items
    CHAT_ITEM: 'div[class*="border-b"]',
    
    // Chat metadata
    CHAT_META: 'div[class*="text-sm text-gray"]',
    
    // Chat preview
    CHAT_PREVIEW: 'div[class*="truncate"]'
  },

  // Loading states
  LOADING: {
    // Loading indicators
    LOADING_SPINNER: 'div[class*="animate-spin"]',
    LOADING_DOTS: 'div[class*="loading"]',
    
    // Typing indicator
    TYPING_INDICATOR: 'div[class*="typing"]',
    
    // Processing status
    PROCESSING: 'div:contains("Processing")'
  },

  // Error states
  ERROR: {
    // Error messages
    ERROR_MESSAGE: 'div[class*="error"]',
    ERROR_NOTIFICATION: 'div[class*="text-red"]',
    
    // Warning messages
    WARNING: 'div[class*="warning"]',
    WARNING_NOTIFICATION: 'div[class*="text-yellow"]'
  }
};

// CSS Selector utilities for complex queries
export const MANUS_QUERIES = {
  // Get all chat messages in order
  ALL_MESSAGES: `${MANUS_SELECTORS.MESSAGES.USER_MESSAGE}, ${MANUS_SELECTORS.MESSAGES.AI_MESSAGE_CONTAINER}`,
  
  // Get latest message
  LATEST_MESSAGE: `(${MANUS_SELECTORS.MESSAGES.USER_MESSAGE}, ${MANUS_SELECTORS.MESSAGES.AI_MESSAGE_CONTAINER}):last-child`,
  
  // Get completion status
  IS_COMPLETED: MANUS_SELECTORS.COMPLETION.SUCCESS_NOTIFICATION,
  
  // Get input field (dynamic based on state)
  ACTIVE_INPUT: `${MANUS_SELECTORS.INPUT.MESSAGE_INPUT}, ${MANUS_SELECTORS.INPUT.MESSAGE_INPUT_ACTIVE}`,
  
  // Get send button
  ACTIVE_SEND_BUTTON: `${MANUS_SELECTORS.INPUT.SEND_BUTTON}, ${MANUS_SELECTORS.INPUT.SEND_BUTTON_ALT}`
};

// XPath selectors for more complex queries
export const MANUS_XPATH = {
  // Text-based selectors
  COMPLETION_TEXT: "//div[contains(text(), 'Manus has completed the current task')]",
  NEW_TASK_BUTTON: "//button[contains(text(), 'New task')]",
  
  // Attribute-based selectors
  MESSAGE_INPUT: "//textarea[contains(@placeholder, 'Give Manus a task') or contains(@placeholder, 'Send message')]",
  
  // Complex structure selectors
  CHAT_MESSAGES: "//div[contains(@class, 'prose') or contains(@class, 'justify-end')]",
  
  // Parent-child relationships
  MESSAGE_WITH_TIMESTAMP: "//div[contains(@class, 'text-xs')]/parent::div"
};

export default MANUS_SELECTORS;
