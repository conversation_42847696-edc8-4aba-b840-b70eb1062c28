# Manus Chat HTML Structure Analysis Report

## Tổng quan
Báo cáo này phân tích cấu trúc HTML của trang chat Manus.im để xác định các selector cần thiết cho việc crawl dữ liệu chat.

## Cấu trúc chính của trang

### 1. Container chính
- **Selector**: `.simplebar-content`
- **M<PERSON><PERSON> đích**: Container ch<PERSON>h chứa toàn bộ nội dung chat
- **Đặc điểm**: Sử dụng SimpleBar library cho scrolling
- **Sử dụng**: Làm root element để crawl toàn bộ chat content

### 2. Khu vực chat messages

#### 2.1 User Messages
- **Selector**: `div[class*="flex justify-end"]`
- **M<PERSON><PERSON> đích**: Chứa tin nhắn từ user
- **Đặc điểm**: 
  - <PERSON><PERSON><PERSON> ph<PERSON> (justify-end)
  - Background màu primary
  - Max width 80%
- **Content selector**: `div[class*="bg-[var(--primary)]"]`

#### 2.2 AI Messages  
- **Selector**: `div[class*="prose"]`
- **Mục đích**: Chứa phản hồi từ Manus AI
- **Đặc điểm**:
  - Sử dụng prose class cho typography
  - Căn trái
  - Có thể chứa markdown content
- **Container**: `div:has(.prose)`

#### 2.3 Timestamps
- **Selector**: `div[class*="text-xs text-[var(--text-secondary)]"]`
- **Mục đích**: Hiển thị thời gian gửi tin nhắn
- **Đặc điểm**: Text nhỏ, màu secondary

### 3. Input Area

#### 3.1 Message Input
- **Selector chính**: `textarea[placeholder*="Give Manus a task to work on"]`
- **Selector phụ**: `textarea[placeholder*="Send message to Manus"]`
- **Mục đích**: Nhập tin nhắn mới
- **Đặc điểm**:
  - Placeholder thay đổi theo trạng thái chat
  - Khi chưa có chat: "Give Manus a task to work on..."
  - Khi đã có chat: "Send message to Manus"

#### 3.2 Send Button
- **Selector**: `button[class*="flex items-center justify-center"]`
- **Selector phụ**: `button:has(img[alt*="send"])`
- **Mục đích**: Gửi tin nhắn
- **Đặc điểm**: Chứa icon send

### 4. Task Completion Indicators

#### 4.1 Success Notification
- **Selector**: `div[class*="px-3 py-[5px] rounded-full text-[var(--function-success)]"]`
- **Mục đích**: Hiển thị thông báo hoàn thành task
- **Nội dung**: "Manus has completed the current task"
- **Đặc điểm**:
  - Màu xanh success
  - Border radius full (pill shape)
  - Font size 13px

#### 4.2 Task Status
- **Selector**: `div[class*="text-[var(--function-success)]"]`
- **Mục đích**: Các indicator khác về trạng thái task
- **Sử dụng**: Detect khi task hoàn thành

### 5. Navigation & UI Elements

#### 5.1 Sidebar
- **Selector**: `div[class*="w-64"]`
- **Mục đích**: Navigation sidebar
- **Content**: `nav[class*="flex flex-col"]`
- **Sử dụng**: Có thể chứa chat history

#### 5.2 New Task Button
- **Selector**: `button:contains("New task")`
- **XPath**: `//button[contains(text(), 'New task')]`
- **Mục đích**: Tạo task mới
- **Đặc điểm**: Có keyboard shortcut (K)

### 6. Loading States

#### 6.1 Loading Indicators
- **Selector**: `div[class*="animate-spin"]`
- **Mục đích**: Hiển thị trạng thái loading
- **Sử dụng**: Detect khi AI đang xử lý

#### 6.2 Typing Indicator
- **Selector**: `div[class*="typing"]`
- **Mục đích**: Hiển thị khi AI đang typing
- **Sử dụng**: Real-time status update

## Chiến lược Crawling

### 1. Crawl Chat History
```javascript
// Lấy tất cả messages theo thứ tự
const messages = document.querySelectorAll(`
  ${MANUS_SELECTORS.MESSAGES.USER_MESSAGE}, 
  ${MANUS_SELECTORS.MESSAGES.AI_MESSAGE_CONTAINER}
`);
```

### 2. Detect Task Completion
```javascript
// Check completion status
const isCompleted = document.querySelector(
  MANUS_SELECTORS.COMPLETION.SUCCESS_NOTIFICATION
);
```

### 3. Send Message
```javascript
// Get active input field
const input = document.querySelector(MANUS_QUERIES.ACTIVE_INPUT);
const sendButton = document.querySelector(MANUS_QUERIES.ACTIVE_SEND_BUTTON);
```

### 4. Monitor Real-time Updates
```javascript
// Watch for new messages
const observer = new MutationObserver((mutations) => {
  // Check for new messages or completion status
});
observer.observe(chatContainer, { childList: true, subtree: true });
```

## Lưu ý quan trọng

### 1. CSS Variables
- Trang sử dụng CSS variables như `var(--primary)`, `var(--function-success)`
- Cần escape square brackets trong CSS selectors: `py-\\[5px\\]`

### 2. Dynamic Content
- Placeholder text thay đổi theo trạng thái
- Messages được load động
- Cần sử dụng MutationObserver cho real-time updates

### 3. Responsive Design
- Layout thay đổi theo screen size
- Sidebar có thể collapse
- Message width responsive (max-w-[80%])

### 4. Error Handling
- Cần handle loading states
- Check for error messages
- Timeout cho long-running tasks (10-15 phút)

## Khuyến nghị Implementation

1. **Sử dụng WebSocket** cho real-time communication
2. **Implement retry logic** cho network failures  
3. **Cache chat history** để tránh re-crawl
4. **Monitor DOM changes** với MutationObserver
5. **Handle CSS escaping** cho complex selectors
6. **Implement timeout** cho long tasks
7. **Extract complete HTML structure** bao gồm metadata và timestamps
